================================================================================
TRAIN LABELS ANALYSIS - FILE INTEGRITY & GRANU<PERSON>RITY
================================================================================
Using data path: ../../input/mitsui-commodity-prediction-challenge

1) BASIC FILE INTEGRITY:
   • Shape: (1917, 425)
   • Memory usage: 6.2 MB

2) DATA TYPES:
   • float64: 424 columns
   • int64: 1 columns

3) DATE_ID GRANULARITY:
   • Unique date_ids: 1917
   • Total rows: 1917
   • One row per date_id: True
   • Date range: 0 to 1916
   • Date span: 1917 days
   • No gaps in date sequence ✓

4) TARGET COLUMNS:
   • Total target columns: 424
   • Expected ~424 columns: ✓

5) NON-FINITE VALUES ANALYSIS:
   • Total data cells: 812,808
   • NaN values: 85,899 (10.57%)
   • Infinite values: 0

   Top 10 columns with highest NaN rates:
     target_232: 17.9%
     target_235: 17.9%
     target_290: 17.9%
     target_302: 17.9%
     target_303: 17.9%
     target_288: 17.9%
     target_286: 17.9%
     target_285: 17.9%
     target_276: 17.9%
     target_270: 17.9%

6) CONSTANT/NEAR-CONSTANT COLUMNS:
   • Constant columns (1 unique value): 0
   • Near-constant columns (≤3 unique values): 0

7) QUICK DISTRIBUTION OVERVIEW:
   Sample statistics for first 5 target columns:
        target_0   target_1   target_2   target_3   target_4
count  1787.0000  1744.0000  1831.0000  1831.0000  1631.0000
mean      0.0004    -0.0006     0.0002     0.0002    -0.0004
std       0.0120     0.0171     0.0131     0.0150     0.0150
min      -0.1238    -0.1204    -0.0673    -0.0725    -0.0813
25%      -0.0046    -0.0106    -0.0078    -0.0089    -0.0095
50%       0.0009    -0.0009     0.0003     0.0003    -0.0004
75%       0.0063     0.0091     0.0089     0.0092     0.0082
max       0.0875     0.1140     0.0478     0.0783     0.0603

================================================================================
FILE INTEGRITY CHECK COMPLETE
================================================================================

================================================================================
TRAIN LABELS ANALYSIS - COVERAGE & AVAILABILITY
================================================================================
1) PER-TARGET COVERAGE ANALYSIS:
   • Coverage statistics across 424 targets:
     - Mean coverage: 89.4%
     - Median coverage: 90.1%
     - Min coverage: 82.1%
     - Max coverage: 100.0%

   • Best covered targets (top 5):
     target_212: 100.0% (dates 0.0-1916.0)
     target_318: 100.0% (dates 0.0-1916.0)
     target_74: 95.5% (dates 0.0-1914.0)
     target_20: 95.5% (dates 0.0-1914.0)
     target_84: 95.5% (dates 0.0-1914.0)

   • Worst covered targets (bottom 5):
     target_285: 82.1% (longest gap: 8.0 days)
     target_317: 82.1% (longest gap: 8.0 days)
     target_269: 82.1% (longest gap: 8.0 days)
     target_270: 82.1% (longest gap: 8.0 days)
     target_261: 82.1% (longest gap: 8.0 days)

2) PER-DATE AVAILABILITY ANALYSIS:
   • Daily availability statistics:
     - Mean targets per day: 379.2
     - Median targets per day: 424.0
     - Min targets per day: 35
     - Max targets per day: 424

   • Worst availability days:
     Date 254.0: 35.0 targets (8.3%)
     Date 513.0: 35.0 targets (8.3%)
     Date 774.0: 35.0 targets (8.3%)
     Date 1810.0: 35.0 targets (8.3%)
     Date 1122.0: 38.0 targets (9.0%)

   • Best availability days:
     Date 9.0: 424.0 targets (100.0%)
     Date 10.0: 424.0 targets (100.0%)
     Date 11.0: 424.0 targets (100.0%)
     Date 12.0: 424.0 targets (100.0%)
     Date 13.0: 424.0 targets (100.0%)

3) DAY-OVER-DAY OVERLAP ANALYSIS:
   • Overlap statistics:
     - Mean overlap size: 348.8 targets
     - Mean Jaccard index: 0.839
     - Min Jaccard index: 0.012
     - Max Jaccard index: 1.000

   • Days with poorest overlap:
     Date 254.0: Jaccard=0.012, overlap=2.0 targets
     Date 88.0: Jaccard=0.012, overlap=4.0 targets
     Date 1385.0: Jaccard=0.012, overlap=4.0 targets
     Date 1123.0: Jaccard=0.013, overlap=4.0 targets
     Date 1381.0: Jaccard=0.014, overlap=4.0 targets
