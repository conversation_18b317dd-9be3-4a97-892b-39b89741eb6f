================================================================================
TRAIN LABELS ANALYSIS - FILE INTEGRITY & GRANU<PERSON>RITY
================================================================================
Using data path: ../../input/mitsui-commodity-prediction-challenge

1) BASIC FILE INTEGRITY:
   • Shape: (1917, 425)
   • Memory usage: 6.2 MB

2) DATA TYPES:
   • float64: 424 columns
   • int64: 1 columns

3) DATE_ID GRANULARITY:
   • Unique date_ids: 1917
   • Total rows: 1917
   • One row per date_id: True
   • Date range: 0 to 1916
   • Date span: 1917 days
   • No gaps in date sequence ✓

4) TARGET COLUMNS:
   • Total target columns: 424
   • Expected ~424 columns: ✓

5) NON-FINITE VALUES ANALYSIS:
   • Total data cells: 812,808
   • NaN values: 85,899 (10.57%)
   • Infinite values: 0

   Top 10 columns with highest NaN rates:
     target_232: 17.9%
     target_235: 17.9%
     target_290: 17.9%
     target_302: 17.9%
     target_303: 17.9%
     target_288: 17.9%
     target_286: 17.9%
     target_285: 17.9%
     target_276: 17.9%
     target_270: 17.9%

6) CONSTANT/NEAR-CONSTANT COLUMNS:
   • Constant columns (1 unique value): 0
   • Near-constant columns (≤3 unique values): 0

7) QUICK DISTRIBUTION OVERVIEW:
   Sample statistics for first 5 target columns:
        target_0   target_1   target_2   target_3   target_4
count  1787.0000  1744.0000  1831.0000  1831.0000  1631.0000
mean      0.0004    -0.0006     0.0002     0.0002    -0.0004
std       0.0120     0.0171     0.0131     0.0150     0.0150
min      -0.1238    -0.1204    -0.0673    -0.0725    -0.0813
25%      -0.0046    -0.0106    -0.0078    -0.0089    -0.0095
50%       0.0009    -0.0009     0.0003     0.0003    -0.0004
75%       0.0063     0.0091     0.0089     0.0092     0.0082
max       0.0875     0.1140     0.0478     0.0783     0.0603

================================================================================
FILE INTEGRITY CHECK COMPLETE
================================================================================

================================================================================
TRAIN LABELS ANALYSIS - COVERAGE & AVAILABILITY
================================================================================
1) PER-TARGET COVERAGE ANALYSIS:
   • Coverage statistics across 424 targets:
     - Mean coverage: 89.4%
     - Median coverage: 90.1%
     - Min coverage: 82.1%
     - Max coverage: 100.0%

   • Best covered targets (top 5):
     target_212: 100.0% (dates 0.0-1916.0)
     target_318: 100.0% (dates 0.0-1916.0)
     target_74: 95.5% (dates 0.0-1914.0)
     target_20: 95.5% (dates 0.0-1914.0)
     target_84: 95.5% (dates 0.0-1914.0)

   • Worst covered targets (bottom 5):
     target_285: 82.1% (longest gap: 8.0 days)
     target_317: 82.1% (longest gap: 8.0 days)
     target_269: 82.1% (longest gap: 8.0 days)
     target_270: 82.1% (longest gap: 8.0 days)
     target_261: 82.1% (longest gap: 8.0 days)

2) PER-DATE AVAILABILITY ANALYSIS:
   • Daily availability statistics:
     - Mean targets per day: 379.2
     - Median targets per day: 424.0
     - Min targets per day: 35
     - Max targets per day: 424

   • Worst availability days:
     Date 254.0: 35.0 targets (8.3%)
     Date 513.0: 35.0 targets (8.3%)
     Date 774.0: 35.0 targets (8.3%)
     Date 1810.0: 35.0 targets (8.3%)
     Date 1122.0: 38.0 targets (9.0%)

   • Best availability days:
     Date 9.0: 424.0 targets (100.0%)
     Date 10.0: 424.0 targets (100.0%)
     Date 11.0: 424.0 targets (100.0%)
     Date 12.0: 424.0 targets (100.0%)
     Date 13.0: 424.0 targets (100.0%)

3) DAY-OVER-DAY OVERLAP ANALYSIS:
   • Overlap statistics:
     - Mean overlap size: 348.8 targets
     - Mean Jaccard index: 0.839
     - Min Jaccard index: 0.012
     - Max Jaccard index: 1.000

   • Days with poorest overlap:
     Date 254.0: Jaccard=0.012, overlap=2.0 targets
     Date 88.0: Jaccard=0.012, overlap=4.0 targets
     Date 1385.0: Jaccard=0.012, overlap=4.0 targets
     Date 1123.0: Jaccard=0.013, overlap=4.0 targets
     Date 1381.0: Jaccard=0.014, overlap=4.0 targets

================================================================================
TRAIN LABELS ANALYSIS - DISTRIBUTION & TAILS
================================================================================
1) DISTRIBUTION STATISTICS PER TARGET:
   • Summary across 424 targets:
     - Mean return: -0.000043
     - Mean volatility (std): 0.0292
     - Mean skewness: 0.045
     - Mean kurtosis: 6.720

   • Volatility distribution:
     - Min std: 0.0092
     - Median std: 0.0262
     - Max std: 0.0750

   • Extreme skewness targets:
     target_95: 5.848
     target_165: 5.553
     target_235: 4.826
     target_85: 4.459
     target_263: 4.153

   • Extreme kurtosis targets:
     target_95: 116.114
     target_165: 89.995
     target_85: 89.203
     target_65: 87.239
     target_235: 65.862

2) ZERO MASS & TIES ANALYSIS:
   • Zero mass statistics:
     - Targets with >5% zeros: 0
     - Targets with >10% zeros: 0
     - Max zero percentage: 0.6%

   • Ties/discretization analysis:
     - Mean unique ratio: 1.000
     - Min unique ratio: 0.994
     - Targets with <50% unique values: 0

3) EXTREME-DAY DIAGNOSTICS:
   • Daily extreme statistics:
     - Mean daily median |target|: 0.0157
     - Mean daily 95th pct |target|: 0.0580
     - Overall max |target|: 0.9255

   • Top 10 spike days (by 95th percentile |target|):
     Date 564.0: p95_abs=0.4707, max_abs=0.8882
     Date 563.0: p95_abs=0.4677, max_abs=0.9255
     Date 562.0: p95_abs=0.3059, max_abs=0.9151
     Date 569.0: p95_abs=0.2931, max_abs=0.7974
     Date 568.0: p95_abs=0.2508, max_abs=0.7181
     Date 566.0: p95_abs=0.2378, max_abs=0.4989
     Date 572.0: p95_abs=0.2285, max_abs=0.5136
     Date 567.0: p95_abs=0.2263, max_abs=0.6231
     Date 574.0: p95_abs=0.2092, max_abs=0.3272
     Date 575.0: p95_abs=0.2064, max_abs=0.3084

================================================================================
TRAIN LABELS ANALYSIS - TIME-SERIES BEHAVIOR
================================================================================
1) AUTOCORRELATION ANALYSIS:
   • Analyzing 20 well-covered targets
   • Autocorrelation statistics (lag 1):
     - Mean: 0.040
     - Median: -0.028
     - Range: [-0.093, 0.722]
   • Autocorrelation statistics (lag 5):
     - Mean: 0.003
     - Median: 0.006
   • Autocorrelation statistics (lag 21):
     - Mean: 0.009
     - Median: -0.004

   • Targets with strong persistence (lag-1 > 0.1): 2
     target_212: lag-1=0.637
     target_318: lag-1=0.722

2) ROLLING STATISTICS ANALYSIS:
   • Rolling mean stability (lower = more stable):
     - Mean: 0.148
     - Most stable: target_20 (0.102)
     - Least stable: target_318 (0.218)
   • Rolling volatility stability:
     - Mean: 0.251

3) HALF-LIFE ESTIMATION (AR(1) MODEL):
   • Half-life statistics (days):
     - Mean: 1.3
     - Median: 1.5
     - Range: [0.2, 2.1]
   • AR(1) coefficient statistics:
     - Mean: 0.465
     - Stationary series: True/3

4) EXTREME SPIKE PERIOD ANALYSIS:
   • Analyzing spike period: dates 560-580
   • Days in spike period: 21
   • Most impacted targets during spike:
     target_318: 8.1x normal volatility
     target_36: 6.9x normal volatility
     target_26: 4.1x normal volatility
     target_212: 4.1x normal volatility
     target_69: 4.0x normal volatility

================================================================================
TRAIN LABELS ANALYSIS - CROSS-SECTIONAL STRUCTURE
================================================================================
1) CROSS-SECTIONAL STATISTICS PER DATE:
   • Cross-sectional mean statistics:
     - Mean of daily means: -0.000039
     - Std of daily means: 0.001772
   • Cross-sectional dispersion statistics:
     - Mean daily std: 0.0286
     - Std of daily stds: 0.0126
     - Min daily std: 0.0080
     - Max daily std: 0.1999
   • Rank resolution statistics:
     - Mean rank resolution: 1.000
     - Min rank resolution: 0.984

   • Thin dispersion days (bottom 10% std): 192
     - Threshold: 0.0190
     - Examples:
       Date 5.0: std=0.0180, 357.0 targets
       Date 6.0: std=0.0168, 356.0 targets
       Date 7.0: std=0.0181, 356.0 targets
       Date 8.0: std=0.0140, 154.0 targets
       Date 11.0: std=0.0170, 424.0 targets

2) RANK PERSISTENCE ANALYSIS:
   • Rank persistence statistics:
     - Mean rank correlation: 0.452
     - Median rank correlation: 0.475
     - Std rank correlation: 0.211
     - Min rank correlation: -0.636
     - Max rank correlation: 0.909

   • High rank persistence days (corr > 0.5): 870
   • Low rank persistence days (corr < -0.2): 18
     - Examples of rank reversals:
       Date 88.0: corr=-0.400
       Date 103.0: corr=-0.261
       Date 160.0: corr=-0.297
       Date 256.0: corr=-0.636
       Date 515.0: corr=-0.317

3) DISPERSION REGIME ANALYSIS:
   • Dispersion regime statistics:
                   cs_std       rank_resolution n_targets
                     mean count            mean      mean
dispersion_regime                                        
Low                0.0278  1899          0.9999  378.9189
Medium             0.0991    14          1.0000  403.2857
High               0.1716     4          1.0000  424.0000

4) SPIKE PERIOD CROSS-SECTIONAL IMPACT:
   • Normal period dispersion: 0.0278 ± 0.0088
   • Spike period dispersion: 0.1038 ± 0.0423
   • Dispersion increase: 3.7x