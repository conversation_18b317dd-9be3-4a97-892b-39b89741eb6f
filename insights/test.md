What’s in test.csv (verified)
Shape: 90 rows × 559 columns.

Columns: exactly the same features as train.csv plus one extra column: is_scored.

Dates: date_id 1827 → 1916 — precisely the last 90 days of the training range (train is 0 → 1916).

is_scored: all 90/90 are True (so every row in test is used for the public leaderboard).

Feature values: for a given date_id, identical to train.csv (we checked LME/JPX/FX/US samples; all matched).

Missingness by asset class (test): LME ≈ 2.22%, JPX ≈ 5.56%, FX = 0%, US_Stock ≈ 4.35% (in line with train).

Implications: the public leaderboard = last 90 days of train. If we tune on those labels, we’ll inflate public LB — so we must treat 1827–1916 as a holdout during model selection.

=== TEST.CSV COMPREHENSIVE ANALYSIS ===
Data path: ../../input/mitsui-commodity-prediction-challenge

1. BASIC STRUCTURE:
   Shape: (90, 559)
   Columns: 559
   Memory usage: 0.38 MB

2. COMPARISON WITH TRAIN.CSV:
   Train shape: (1917, 558)
   Test shape:  (90, 559)
   Row difference: -1827
   Column difference: 1

3. DATE RANGE ANALYSIS:
   Train date_id range: 0 → 1916
   Test date_id range:  1827 → 1916
   Test dates relative to train: last 90 days of training period

4. COLUMN DIFFERENCES:
   Columns unique to test: ['is_scored']
   Columns unique to train: []

5. IS_SCORED COLUMN ANALYSIS:
   Unique values: [ True]
   Value counts:
   {True: 90}
   All rows scored: True

6. FIRST FEW ROWS:
   date_id  LME_AH_Close  LME_CA_Close  LME_PB_Close  LME_ZS_Close  \
0     1827        2684.5        9190.0        1967.0        2942.0   
1     1828        2691.5        9275.0        1985.0        2963.5   
2     1829        2646.0        9284.5        1971.0        2914.0   

   JPX_Gold_Mini_Futures_Open  JPX_Gold_Rolling-Spot_Futures_Open  \
0                     13623.0                             13920.0   
1                     13640.0                             13922.0   
2                     13634.0                             13923.0   

   JPX_Gold_Standard_Futures_Open  JPX_Platinum_Mini_Futures_Open  \
0                         13618.0                          4696.0   
1                         13634.0                          4613.0   
2                         13638.0                          4647.0   

   JPX_Platinum_Standard_Futures_Open  ...  FX_CADCHF  FX_NZDCAD  FX_NZDCHF  \
0                              4692.0  ...   0.631633   0.808485   0.510666   
1                              4613.0  ...   0.633526   0.812571   0.514785   
2                              4632.0  ...   0.632156   0.811948   0.513278   

   FX_ZAREUR  FX_NOKGBP  FX_NOKCHF  FX_ZARCHF  FX_NOKJPY  FX_ZARGBP  is_scored  
0   0.051733   0.071654   0.079797   0.048828  13.631347   0.043845       True  
1   0.051802   0.071793   0.080214   0.048912  13.743387   0.043778       True  
2   0.051902   0.071630   0.080134   0.048971  13.766241   0.043774       True  

[3 rows x 559 columns]

=== FEATURE CONSISTENCY ANALYSIS ===

1. FEATURE COLUMN VERIFICATION:
   Train features: 557
   Test features:  557
   Features match: True

2. FEATURE VALUE CONSISTENCY CHECK:
   Overlapping dates: 90
   Train overlap shape: (90, 558)
   Test sorted shape: (90, 559)

   LME features verification:
     LME_AH_Close: ✓ Identical
     LME_CA_Close: ✓ Identical

   JPX features verification:
     JPX_Gold_Mini_Futures_Open: ✓ Identical
     JPX_Platinum_Mini_Futures_Open: ✓ Identical

   FX features verification:
     FX_CADCHF: ✓ Identical
     FX_NZDCAD: ✓ Identical

   US_Stock features verification:
     US_Stock_ACWI_adj_open: ✓ Identical
     US_Stock_AEM_adj_open: ✓ Identical

   Overall feature consistency: ✓ All verified features identical

=== MISSING VALUE PATTERNS ANALYSIS ===

1. ASSET CLASS BREAKDOWN:
   LME: 4 features
   JPX: 40 features
   FX: 38 features
   US_Stock: 475 features

2. MISSING VALUE ANALYSIS BY ASSET CLASS:
   LME:
     Total values: 360
     Missing values: 8
     Missing percentage: 2.22%
   JPX:
     Total values: 3,600
     Missing values: 200
     Missing percentage: 5.56%
   FX:
     Total values: 3,420
     Missing values: 0
     Missing percentage: 0.00%
   US_Stock:
     Total values: 42,750
     Missing values: 1,860
     Missing percentage: 4.35%

3. COMPARISON WITH TRAIN MISSING PATTERNS (same dates):
   LME:
     Train missing: 2.22%
     Test missing:  2.22%
     Difference: +0.00% ✓
   JPX:
     Train missing: 5.56%
     Test missing:  5.56%
     Difference: +0.00% ✓
   FX:
     Train missing: 0.00%
     Test missing:  0.00%
     Difference: +0.00% ✓
   US_Stock:
     Train missing: 4.35%
     Test missing:  4.35%
     Difference: +0.00% ✓

4. OVERALL MISSING VALUE STATISTICS:
   Test overall missing: 4.13%
   Train overall missing: 4.13%
   Difference: +0.00%

5. COMPLETELY MISSING FEATURES IN TEST:
   Features with all NaN: ['US_Stock_GOLD_adj_open', 'US_Stock_GOLD_adj_high', 'US_Stock_GOLD_adj_low', 'US_Stock_GOLD_adj_close', 'US_Stock_GOLD_adj_volume']

=== CV STRATEGY AND PUBLIC LEADERBOARD IMPLICATIONS ===

1. CRITICAL DATE ANALYSIS:
   Train data ends at: date_id 1916
   Test data spans: date_id 1827 → 1916
   Test = last 90 days of training period
   Public LB uses: ALL 90 test rows (is_scored=True for all)

2. TARGET AVAILABILITY FOR TEST DATES:
   Train labels shape: (1917, 425)
   Labels for test dates: (90, 425)
   Target columns: 424

   Sample target coverage in test period:
     target_0: 92.2% (83/90)
     target_1: 91.1% (82/90)
     target_2: 94.4% (85/90)
     target_3: 94.4% (85/90)
     target_4: 83.3% (75/90)
   Overall target coverage: 89.0%

3. CV STRATEGY IMPLICATIONS:
   🚨 CRITICAL: Public LB = train dates 1827-1916
   ✅ DO: Treat dates 1827-1916 as HOLDOUT during model selection
   ✅ DO: Use dates 0-1826 for training/validation splits
   ❌ DON'T: Tune hyperparameters on dates 1827-1916
   ❌ DON'T: Use these dates for feature selection or model comparison

4. RECOMMENDED CV DESIGN:
   Available dates for CV: 0 → 1826 (1827 days)
   Suggested approach:
     - Use dates 0-1826 for all model development
     - Create time-blocked CV within this range
     - Reserve dates 1827-1916 for final validation only
     - Apply 5-10 day purging gaps to respect max lag of 4

5. STRESS PERIOD ANALYSIS:
   Stress period: dates 562-575
   Test period: dates 1827-1916
   Overlap: None
   Stress period in CV range: ✓ Yes

6. FINAL RECOMMENDATIONS:
   📊 Model Development: Use dates 0-1826 only
   🔄 CV Folds: 4-5 time-blocked folds within available range
   ⚠️  Purging: 5-10 day gaps between train/valid in each fold
   🎯 Final Check: Single evaluation on dates 1827-1916
   🚫 Leakage Prevention: Never use test period for any tuning decisions

=== FINAL TEST SET CHARACTERISTICS ===

1. TEST DATE PATTERN ANALYSIS:
   First test date: 1827
   Last test date: 1916
   Total test days: 90
   Consecutive days: ✓ Yes

2. PROBLEMATIC DAYS IN TEST PERIOD:
   Previously identified small-N days: [254, 513, 774, 1122, 1810]
   Small-N days in test period: []
   ✓ No previously identified small-N days in test period

3. DAILY TARGET AVAILABILITY IN TEST PERIOD:
   Mean targets per day: 377.2
   Median targets per day: 424.0
   Min targets per day: 48
   Max targets per day: 424
   Std targets per day: 81.7
   Days with <200 targets: 5
     date_id 1827: 154 targets
     date_id 1847: 154 targets
     date_id 1891: 48 targets
     date_id 1892: 137 targets
     date_id 1916: 48 targets

4. COMPREHENSIVE TEST.CSV SUMMARY:
   📊 Structure: 90 rows × 559 columns
   📅 Date Range: 1827 → 1916 (last 90 days of train)
   🎯 Scoring: All 90 rows used for public leaderboard
   🔄 Features: 557 identical to train.csv
   ✅ Consistency: Perfect feature value alignment with train.csv
   📉 Missing Data: 4.13% overall (identical to train)
   🚫 Dropped Features: 5 US_Stock_GOLD features (all NaN)
   🎲 Target Coverage: 89.0% in test period

5. MODELING IMPLICATIONS:
   🏗️  CV Design: Use dates 0-1826 only (1827 days available)
   🔒 Holdout: Dates 1827-1916 reserved for final validation
   ⚡ Feature Set: Drop 5 GOLD features, use remaining 552
   📈 Target Stability: Good coverage in test period (89.0%)
   🎯 Evaluation: Expect consistent performance on public LB
   ⚠️  Leakage Risk: HIGH if test period used in model development

✅ TEST.CSV ANALYSIS COMPLETE
Ready to proceed with leak-safe model development using dates 0-1826