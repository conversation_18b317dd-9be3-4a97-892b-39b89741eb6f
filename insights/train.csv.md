# train.csv 

Historic finance data retlated to commodities such as closing prices, trading volumes, foreign exchange rates, and so on.

date_id - A single UTC date. Due to time zone and holiday differences, some entire exchanges may not have traded on a given date.

[time_series_identifier] - Each security includes a two or three letter prefix that denotes the origin of the trading activity: LME (London Mercantile Exchange), JPX (Japanese Securities Exchange), US (various US stock exchanges), and FX (foreign exchange).

🔍 Detected local environment
Loading train.csv from: ../../input/mitsui-commodity-prediction-challenge
✅ Successfully loaded train.csv
Shape: (1917, 558)

============================================================
TRAIN.CSV - BASIC OVERVIEW
============================================================
Dataset shape: (1917, 558)
Rows (time periods): 1,917
Columns (features + date_id): 558
Memory usage: 8.16 MB

Column types:
float64    557
int64        1
Name: count, dtype: int64

============================================================
DATE_ID AND TEMPORAL STRUCTURE
============================================================
Date ID column info:
  Data type: int64
  Unique values: 1917
  Min date_id: 0
  Max date_id: 1916
  Date range span: 1916 days
  Duplicate date_ids: 0

Temporal continuity:
  Expected dates in range: 1917
  Actual dates present: 1917
  Missing dates: 0

First 10 date_ids: [np.int64(0), np.int64(1), np.int64(2), np.int64(3), np.int64(4), np.int64(5), np.int64(6), np.int64(7), np.int64(8), np.int64(9)]
Last 10 date_ids: [np.int64(1907), np.int64(1908), np.int64(1909), np.int64(1910), np.int64(1911), np.int64(1912), np.int64(1913), np.int64(1914), np.int64(1915), np.int64(1916)]

============================================================
FEATURE COLUMNS ANALYSIS
============================================================
Total feature columns: 557

Feature distribution by asset class:
  LME: 4 features
  JPX: 40 features
  US_Stock: 475 features
  FX: 38 features

Sample column names by asset class:
  LME (first 5): ['LME_AH_Close', 'LME_CA_Close', 'LME_PB_Close', 'LME_ZS_Close']
  JPX (first 5): ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Rolling-Spot_Futures_Open', 'JPX_Gold_Standard_Futures_Open', 'JPX_Platinum_Mini_Futures_Open', 'JPX_Platinum_Standard_Futures_Open']
  US_Stock (first 5): ['US_Stock_ACWI_adj_open', 'US_Stock_AEM_adj_open', 'US_Stock_AG_adj_open', 'US_Stock_AGG_adj_open', 'US_Stock_ALB_adj_open']
  FX (first 5): ['FX_AUDJPY', 'FX_AUDUSD', 'FX_CADJPY', 'FX_CHFJPY', 'FX_EURAUD']

Total features breakdown:
  LME: 4
  JPX: 40
  US_Stock: 475
  FX: 38
  Total: 557

============================================================
MISSING VALUES ANALYSIS
============================================================
Overall missing value statistics:
  Total cells: 1,069,686
  Missing cells: 43,230
  Missing percentage: 4.04%

Columns with missing values:
  Columns with missing data: 519 out of 558
  Columns with no missing data: 39

Top 10 columns with most missing values:
  US_Stock_GOLD_adj_low: 1,669 (87.1%)
  US_Stock_GOLD_adj_volume: 1,669 (87.1%)
  US_Stock_GOLD_adj_open: 1,669 (87.1%)
  US_Stock_GOLD_adj_high: 1,669 (87.1%)
  US_Stock_GOLD_adj_close: 1,669 (87.1%)
  JPX_RSS3_Rubber_Futures_open_interest: 115 (6.0%)
  JPX_Platinum_Standard_Futures_Volume: 115 (6.0%)
  JPX_Platinum_Mini_Futures_Close: 115 (6.0%)
  JPX_Platinum_Standard_Futures_Close: 115 (6.0%)
  JPX_RSS3_Rubber_Futures_Close: 115 (6.0%)

Missing values by asset class:
  LME: 200/7,668 (2.61%)
  JPX: 4,600/76,680 (6.00%)
  US_Stock: 38,430/910,575 (4.22%)
  FX: 0/72,846 (0.00%)

============================================================
DATA DISTRIBUTION ANALYSIS
============================================================

LME Asset Class Summary:
  Number of features: 4
  Valid data points: 7,468
  Mean: 3754.1366
  Median: 2495.2500
  Std: 2536.7694
  Min: 1462.0000
  Max: 10889.0000
  Range: 9427.0000
  1st percentile: 1619.3350
  99th percentile: 10032.9650
  Sample columns detailed stats:
    LME_AH_Close: mean=2245.250, std=400.329, range=[1462.000, 3849.000]
    LME_CA_Close: mean=7886.749, std=1515.500, range=[4630.000, 10889.000]
    LME_PB_Close: mean=2087.633, std=184.817, range=[1585.500, 2681.000]

JPX Asset Class Summary:
  Number of features: 40
  Valid data points: 72,080
  Mean: 7918.2046
  Median: 4583.0000
  Std: 13335.3792
  Min: 61.0000
  Max: 163269.0000
  Range: 163208.0000
  1st percentile: 163.3000
  99th percentile: 77694.4600
  Sample columns detailed stats:
    JPX_Gold_Mini_Futures_Open: mean=7502.445, std=2833.110, range=[4171.000, 15720.000]
    JPX_Gold_Rolling-Spot_Futures_Open: mean=7561.661, std=2890.202, range=[4216.000, 15984.000]
    JPX_Gold_Standard_Futures_Open: mean=7502.554, std=2833.227, range=[4171.000, 15715.000]

US_Stock Asset Class Summary:
  Number of features: 475
  Valid data points: 872,145
  Mean: 1538567.1928
  Median: 64.2576
  Std: 5593758.7670
  Min: 1.1738
  Max: 322806886.0000
  Range: 322806884.8262
  1st percentile: 4.7413
  99th percentile: 26503672.9600
  Sample columns detailed stats:
    US_Stock_ACWI_adj_open: mean=84.937, std=18.230, range=[49.298, 124.160]
    US_Stock_AEM_adj_open: mean=53.215, std=16.949, range=[27.918, 125.773]
    US_Stock_AG_adj_open: mean=8.453, std=3.186, range=[4.228, 23.558]

FX Asset Class Summary:
  Number of features: 38
  Valid data points: 72,846
  Mean: 22.6431
  Median: 1.0668
  Std: 46.7692
  Min: 0.0395
  Max: 207.7030
  Range: 207.6635
  1st percentile: 0.0460
  99th percentile: 171.1502
  Sample columns detailed stats:
    FX_AUDJPY: mean=85.998, std=9.370, range=[62.955, 109.112]
    FX_AUDUSD: mean=0.697, std=0.044, range=[0.574, 0.812]
    FX_CADJPY: mean=93.541, std=11.792, range=[74.973, 118.678]

============================================================
DISTRIBUTION VISUALIZATIONS
============================================================
Distribution Insights:
- LME: Metal prices show normal-ish distributions
- JPX: Futures data with varying scales
- US_Stock: Highly skewed (shown in log scale) - likely includes volume data
- FX: Currency rates with different scales (some are ratios, others are yen rates)

============================================================
EXPLAINING THE VISUALIZATIONS
============================================================

LME Asset Class - Colors represent:
  Pink/Red: LME_AH_Close
  Yellow/Orange: LME_CA_Close
  Green: LME_PB_Close

JPX Asset Class - Colors represent:
  Pink/Red: JPX_Gold_Mini_Futures_Open
  Yellow/Orange: JPX_Gold_Rolling-Spot_Futures_Open
  Green: JPX_Gold_Standard_Futures_Open

US_Stock Asset Class - Colors represent:
  Pink/Red: US_Stock_ACWI_adj_open
  Yellow/Orange: US_Stock_AEM_adj_open
  Green: US_Stock_AG_adj_open

FX Asset Class - Colors represent:
  Pink/Red: FX_AUDJPY
  Yellow/Orange: FX_AUDUSD
  Green: FX_CADJPY

========================================
FX ASSET CLASS INVESTIGATION
========================================
All FX columns (38):
   1. FX_AUDJPY
   2. FX_AUDUSD
   3. FX_CADJPY
   4. FX_CHFJPY
   5. FX_EURAUD
   6. FX_EURGBP
   7. FX_EURJPY
   8. FX_EURUSD
   9. FX_GBPAUD
  10. FX_GBPJPY
  11. FX_GBPUSD
  12. FX_NZDJPY
  13. FX_NZDUSD
  14. FX_USDCHF
  15. FX_USDJPY
  16. FX_ZARJPY
  17. FX_ZARUSD
  18. FX_NOKUSD
  19. FX_NOKEUR
  20. FX_CADUSD
  21. FX_AUDNZD
  22. FX_EURCHF
  23. FX_EURCAD
  24. FX_AUDCAD
  25. FX_GBPCHF
  26. FX_EURNZD
  27. FX_AUDCHF
  28. FX_GBPNZD
  29. FX_GBPCAD
  30. FX_CADCHF
  31. FX_NZDCAD
  32. FX_NZDCHF
  33. FX_ZAREUR
  34. FX_NOKGBP
  35. FX_NOKCHF
  36. FX_ZARCHF
  37. FX_NOKJPY
  38. FX_ZARGBP

First 3 FX columns data summary:

FX_AUDJPY:
  Count: 1917
  Range: [62.9547, 109.1124]
  Mean: 85.9984
  Sample values: [87.933498, 88.13073, 88.709278, 88.971278, 88.79715]

FX_AUDUSD:
  Count: 1917
  Range: [0.5741, 0.8121]
  Mean: 0.6969
  Sample values: [0.783393, 0.782779, 0.786472, 0.787461, 0.784808]

FX_CADJPY:
  Count: 1917
  Range: [74.9731, 118.6775]
  Mean: 93.5411
  Sample values: [89.764583, 89.731507, 90.306741, 91.079905, 91.13646]

FX Range Analysis:
  FX_AUDJPY: range = 46.1577 (from 62.9547 to 109.1124)
  FX_AUDUSD: range = 0.2380 (from 0.5741 to 0.8121)
  FX_CADJPY: range = 43.7044 (from 74.9731 to 118.6775)

FX Currency Pair Groupings:
JPY pairs (vs Japanese Yen): 9 pairs
USD pairs (vs US Dollar): 8 pairs
EUR pairs (vs Euro): 7 pairs
Other pairs: 14 pairs

Sample from each group:
  JPY Pairs: ['FX_AUDJPY', 'FX_CADJPY', 'FX_CHFJPY']
  USD Pairs: ['FX_AUDUSD', 'FX_EURUSD', 'FX_GBPUSD']
  EUR Pairs: ['FX_EURAUD', 'FX_EURGBP', 'FX_NOKEUR']
  Other Pairs: ['FX_GBPAUD', 'FX_AUDNZD', 'FX_AUDCAD']

# Cell 9: Better FX Visualization with Separate Scales
print("="*60)
print("IMPROVED FX VISUALIZATION")
print("="*60)

# Create separate plots for different FX types
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Group FX pairs by type
fx_cols = [col for col in feature_cols if col.startswith('FX')]

# JPY pairs (higher values ~60-200)
jpy_pairs = [col for col in fx_cols if 'JPY' in col]
usd_pairs = [col for col in fx_cols if 'USD' in col and 'JPY' not in col]
eur_pairs = [col for col in fx_cols if 'EUR' in col and 'JPY' not in col and 'USD' not in col]
other_pairs = [col for col in fx_cols if col not in jpy_pairs + usd_pairs + eur_pairs]

fx_groups = [
    ('JPY Pairs', jpy_pairs[:3]),
    ('USD Pairs', usd_pairs[:3]), 
    ('EUR Pairs', eur_pairs[:3]),
    ('Other Pairs', other_pairs[:3])
]

for i, (group_name, pairs) in enumerate(fx_groups):
    ax = axes[i//2, i%2]
    
    for pair in pairs:
        if pair in train_df.columns:
            data = train_df[pair].dropna()
            if len(data) > 0:
                ax.hist(data, bins=30, alpha=0.6, label=pair.replace('FX_', ''), density=True)
    
    ax.set_title(f'{group_name} Distributions')
    ax.set_xlabel('Exchange Rate')
    ax.set_ylabel('Density')
    ax.legend()
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print the grouping logic
print(f"\nFX Currency Pair Groupings:")
print(f"JPY pairs (vs Japanese Yen): {len(jpy_pairs)} pairs")
print(f"USD pairs (vs US Dollar): {len(usd_pairs)} pairs") 
print(f"EUR pairs (vs Euro): {len(eur_pairs)} pairs")
print(f"Other pairs: {len(other_pairs)} pairs")

print(f"\nSample from each group:")
for group_name, pairs in fx_groups:
    print(f"  {group_name}: {pairs[:3]}")

Volatility Analysis (30-day rolling standard deviation):
  LME (LME_CA_Close):
    Average volatility: 199.5290
    Max volatility: 821.8793
    Min volatility: 42.6762
    Volatility range: 779.2030
  JPX (JPX_Gold_Mini_Futures_Open):
    Average volatility: 135.7312
    Max volatility: 572.8760
    Min volatility: 17.1190
    Volatility range: 555.7570
  US_Stock (US_Stock_ACWI_adj_open):
    Average volatility: 1.7455
    Max volatility: 8.8120
    Min volatility: 0.4501
    Volatility range: 8.3620
  FX (FX_EURUSD):
    Average volatility: 0.0098
    Max volatility: 0.0259
    Min volatility: 0.0031
    Volatility range: 0.0229


============================================================
COMPLETE FEATURE INVENTORY - ALL 557 FEATURES
============================================================
Total features to analyze: 557

1. LME FEATURES (4):
   LME_AH_Close
   LME_CA_Close
   LME_PB_Close
   LME_ZS_Close

2. JPX FEATURES (40) - Pattern Analysis:
   JPX Products: ['Gold', 'Platinum', 'RSS3']
   JPX Fields: ['Mini_Futures_Close', 'Mini_Futures_High', 'Mini_Futures_Low', 'Mini_Futures_Open', 'Mini_Futures_Volume', 'Mini_Futures_open_interest', 'Mini_Futures_settlement_price', 'Rolling-Spot_Futures_Close', 'Rolling-Spot_Futures_High', 'Rolling-Spot_Futures_Low', 'Rolling-Spot_Futures_Open', 'Rolling-Spot_Futures_Volume', 'Rolling-Spot_Futures_open_interest', 'Rolling-Spot_Futures_settlement_price', 'Rubber_Futures_Close', 'Rubber_Futures_High', 'Rubber_Futures_Low', 'Rubber_Futures_Open', 'Rubber_Futures_Volume', 'Rubber_Futures_open_interest', 'Rubber_Futures_settlement_price', 'Standard_Futures_Close', 'Standard_Futures_High', 'Standard_Futures_Low', 'Standard_Futures_Open', 'Standard_Futures_Volume', 'Standard_Futures_open_interest']
   First 10 JPX columns: ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Rolling-Spot_Futures_Open', 'JPX_Gold_Standard_Futures_Open', 'JPX_Platinum_Mini_Futures_Open', 'JPX_Platinum_Standard_Futures_Open', 'JPX_RSS3_Rubber_Futures_Open', 'JPX_Gold_Mini_Futures_High', 'JPX_Gold_Rolling-Spot_Futures_High', 'JPX_Gold_Standard_Futures_High', 'JPX_Platinum_Mini_Futures_High']

3. FX FEATURES (38):
   Unique currencies involved: ['AUD', 'CAD', 'CHF', 'EUR', 'GBP', 'JPY', 'NOK', 'NZD', 'USD', 'ZAR']
   All FX pairs: ['AUDJPY', 'AUDUSD', 'CADJPY', 'CHFJPY', 'EURAUD', 'EURGBP', 'EURJPY', 'EURUSD', 'GBPAUD', 'GBPJPY', 'GBPUSD', 'NZDJPY', 'NZDUSD', 'USDCHF', 'USDJPY', 'ZARJPY', 'ZARUSD', 'NOKUSD', 'NOKEUR', 'CADUSD', 'AUDNZD', 'EURCHF', 'EURCAD', 'AUDCAD', 'GBPCHF', 'EURNZD', 'AUDCHF', 'GBPNZD', 'GBPCAD', 'CADCHF', 'NZDCAD', 'NZDCHF', 'ZAREUR', 'NOKGBP', 'NOKCHF', 'ZARCHF', 'NOKJPY', 'ZARGBP']

4. US_STOCK FEATURES (475) - Pattern Analysis:
   Unique stock symbols: 95
   Stock fields: ['adj_close', 'adj_high', 'adj_low', 'adj_open', 'adj_volume']
   First 20 symbols: ['ACWI', 'AEM', 'AG', 'AGG', 'ALB', 'AMP', 'BCS', 'BKR', 'BND', 'BNDX', 'BP', 'BSV', 'CAT', 'CCJ', 'CLF', 'COP', 'CVE', 'CVX', 'DE', 'DVN']
   Sample US_Stock columns: ['US_Stock_ACWI_adj_open', 'US_Stock_AEM_adj_open', 'US_Stock_AG_adj_open', 'US_Stock_AGG_adj_open', 'US_Stock_ALB_adj_open', 'US_Stock_AMP_adj_open', 'US_Stock_BCS_adj_open', 'US_Stock_BKR_adj_open', 'US_Stock_BND_adj_open', 'US_Stock_BNDX_adj_open']

============================================================
SUMMARY:
LME: 4 features (4 metals)
JPX: 40 features (3 products)
FX: 38 features (10 currencies)
US_Stock: 475 features (95 symbols)
Total: 557 features