Great start. Here’s a **critical read** of your EDA and what we should do next before modeling.

---

## What your EDA tells us (and why it matters)

1. **Feature imbalance (US‑stock heavy).**

* 475 US‑stock fields vs only 4 LME, 40 JPX, 38 FX. Models will default to equity signals unless we **re‑weight features** or engineer **commodity‑centric factors** (e.g., metals term structure, FX triangular spreads). This matters because targets include **commodity and pair‑wise series**; we don’t want equity noise to dominate.

2. **Missingness is concentrated, not uniform.**

* Overall ≈4% missing, but extremes (e.g., `US_Stock_GOLD_*` ≈87% missing). Treat tickers/series with **low coverage as separate regimes**: either drop, or impute only **within‑series forward‑fill capped** (no cross‑series leakage).
* Note: your LME block lists “valid data points: 7,468” but 4×1917 = **7,668**; let’s double‑check that minor discrepancy.

3. **Scale heterogeneity is huge.**

* FX JPY crosses \~60–120; EURUSD \~0.6–1.0; volumes and some US fields explode to 3e8. We should compute **per‑series log‑returns / log‑diffs** and **robust z‑scores** (median/IQR) rather than level values. <PERSON><PERSON><PERSON> (our metric) is rank‑based per day, but **stable ranking needs stable scaling** upstream.

4. **Calendar/closure effects are real.**

* 1917 contiguous `date_id`, but exchanges observe different holidays → **entire blocks per exchange are NA**. We’ll need **per‑exchange masks** and to evaluate **coverage per target per day** (the daily Spearman only uses targets present that day).

5. **FX structure = free quality checks & features.**

* With 38 FX pairs, we can compute **triangle parity residuals** (e.g., EURUSD×USDJPY − EURJPY). Large residuals flag data errors or create **arb‑residual features** that often carry signal into commodity returns.

6. **Volatility profiles differ by asset class.**

* Your 30‑day vol snapshot shows far larger absolute vols in metals and JPX than in FX; after we switch to returns, compare **vol‑of‑vol** and **vol spikes around global events** (will guide robust loss and online update cadence).

---

## Data risks / leakage we must rule out now

* **Target alignment:** Targets come with **instrument mappings and required lags**. If we compute returns/features at date t using any value that depends on t (or later) while the target is defined at t+lag, we leak. We need to **join features to targets using `target_pairs` and its lag map** and verify no look‑ahead.
* **Imputation leakage:** Forward‑fill must be **strictly within series up to t**, never borrowing other symbols or future days.

---

## Concrete next EDA tasks (high‑impact, \~1–2 hours)

**Please share** `train_labels.csv` and `target_pairs.csv` (and the label\_lags files). I’ll wire these checks:

1. **Coverage & integrity dashboard (per target).**

* Start/end date, % non‑missing, effective lag, active days histogram, exchange membership.

2. **Alignment audit.**

* For each target, confirm that the **feature snapshot used at prediction time** precedes the target by its required lag. We’ll print any offenders.

3. **Signal scouting (leak‑free).**

* Compute **rank‑IC per day** between simple, legal features and each target:

  * same‑asset **1–5 day momentum** (log‑return),
  * **cross‑sectional z‑scores** of those momenta within asset groups (LME/JPX/FX/US miners/energy),
  * **FX triangle residuals**,
  * **commodity‑equity linkers** (e.g., Cu miners vs LME copper).
* Summarize as **Sharpe of daily rank‑IC** to mirror the competition metric.

4. **Series health check.**

* Drop list for series with **<50% coverage** or **>3 structural breaks** (Pelt/diff tests), unless they map directly to a target we must keep.

5. **Calendar heatmaps.**

* Date × exchange missingness; date × target presence. This will guide **time‑blocked CV** construction.

If you want, I can run this audit for you and produce the tables/plots.

---

## Immediate preprocessing decisions (proposed)

* **Represent everything in returns space:**

  * Prices, FX rates → log‑returns; volumes/open interest → log‑diffs and **z‑scores within series**.
* **Per‑day cross‑sectional normalization:**

  * Before modeling, **z‑score within asset group per day**, then later **convert predictions to per‑day ranks** (ties handled stably).
* **Imputation:** forward‑fill within series with **max gap cap** (e.g., 10 days); otherwise leave NaN and **mask** (give models a “missing” indicator).
* **Feature pruning:** drop series with **coverage <50%** unless target‑critical; drop obviously redundant OHLC if we model on returns (keep close/settlement + range).

---

## Industry‑grounded feature ideas to test (after the audit)

* **Metals ↔ miners linkage:** rolling beta of miners (AEM, FCX, etc.) to LME metals; residual (alpha) as a predictor for metal‑linked targets.
* **FX pass‑through:** USD strength (broad USD index built from FX pairs) as a driver for **USD‑denominated commodity returns**; parity residuals as stress indicator.
* **Term‑structure / carry (JPX):** settlement − nearby futures (or open interest shifts) as a proxy for **convenience yield**.
* **Cross‑asset stress:** equity vol (e.g., ACWI range), credit/EM proxies (BND, BNDX) as macro pressure features.

---

### What I need from you to proceed

1. Upload `train_labels.csv`, `target_pairs.csv`, and the **label\_lags** files.
2. Green‑light for me to run a quick **audit notebook** and return:

   * target coverage table,
   * alignment report,
   * first pass rank‑IC heatmap (daily Spearman Sharpe) for the simple features above.

Once we confirm alignment and see which families carry signal, we’ll lock **CV splits that mirror the API** and build the first **rank‑aware multi‑target baseline** with an **online‑update hook**. Sound good?

---

# train_lables.csv

Excellent work — this is the exact depth we need. Here’s a **critical read of your results** and the **next EDA moves** so we turn these statistics into an evaluation‑faithful plan.

---

## What your analysis *implies* for modeling

1. **Daily cross‑section is usable most of the time.**
   Median **424 targets/day**, mean **379/day**, and **rank resolution ≈ 1.0** mean the metric won’t be bottlenecked by ties on most days. But the **small‑N days** (as low as 35 targets) will make Spearman **high‑variance**; we should **shrink** predictions toward a prior (e.g., the previous‑day rank) when `n_targets` is small.

2. **There’s real, tradable persistence — but short‑lived.**
   Mean **rank‑persistence ≈ 0.45** (t vs t−1) is sizable, yet **AR(1) half‑life ≈ 1–2 days**. This points to **one‑day momentum ranks** (with rapid decay) as a strong baseline, plus **online updates** to track drift. Reversal logic likely pays only on **identified reversal days** (see below).

3. **Regimes are a thing (spike window dates 562–575).**
   Cross‑sectional dispersion **jumps \~3.7×**, with multiple rank reversals around that region. This suggests:

* A **stress regime** where dispersion widens and the optimal model (or post‑processing) differs.
* We should **quarantine a “stress fold”** in CV to test robustness and consider **robust losses** / **winsorized targets** for training.

4. **Coverage is uneven across time.**
   Per‑target mean coverage ≈ **89%**, but per‑day availability can collapse (35–40 targets). Those days likely correspond to one or more exchanges being closed. **Unweighted daily Sharpe** (competition metric) means **low‑N days can dominate variance**. We should:

* Track **n\_targets/day** and **dispersion** at inference.
* Adjust blending strength (momentum vs neutral) by **n\_targets** and **dispersion**.

5. **Two suspiciously persistent targets (212, 318).**
   Lag‑1 autocorr **0.64–0.72** is unusual for return‑like series. Before we rely on them for training signal, we must verify their **definitions and lags** via `target_pairs.csv`. If they’re pairwise constructions, confirm directionality is stable.

---

## Validations / clarifications (important before we proceed)

* **Do not map spike dates to calendar events yet.** We need the **date\_id → calendar date** map before inferring “COVID/war/…”. For now, treat **562–575** as a generic **stress regime**.
* **Your autocorr sample used 20 targets.** Good for a first pass; next compute these **group‑wise** once we have the target taxonomy (LME / JPX / FX / US‑linked / pairs vs singles). Persistence may be concentrated in specific groups.

---

## High‑value next EDA on `train_labels.csv` (no code here, but specific outputs to produce)

1. **Small‑N day anatomy.**
   For the worst days (e.g., date\_id **254, 513, 774, 1122, 1810**):

* List which targets are present and which asset classes they belong to (once we have the map).
* Compute **rank‑persistence** and **dispersion** on those days vs neighbors.
* Output: a mini table per date with `n_targets`, dispersion, rank‑persistence, and asset‑class mix.

2. **Rank‑persistence vs dispersion & n\_targets.**
   Correlate daily **rank‑persistence(t,t−1)** with **cross‑sectional std** and **n\_targets**.

* If persistence collapses in **high‑dispersion** or **low‑N** regimes, we’ll condition our meta‑blend on these variables.

3. **Top/bottom cohort stability.**
   Track **turnover** of top‑decile and bottom‑decile targets day‑to‑day (intersection size / decile size).

* Gives a direct sense of how stable the edges are → useful for choosing **shrinkage** of predicted ranks.

4. **Outlier diagnostics for the stress window (562–575).**

* Per‑day: 95th percentile |target|, share of targets with |target| > 3× their median absolute deviation.
* Output: table/plot to decide **winsor levels** and whether to separate training by regime.

5. **Per‑target stationarity checks (screen for oddballs).**

* List targets with **>3 change‑points** or **unit‑root‑like** behavior. These may need **group‑specific models** or exclusion.

6. **Upper‑bound realism.**

* Create a **noise ceiling proxy**: add tiny noise ε to the day’s targets and compute Spearman(target, target+ε) across days; summarize its mean/std.
* This tells us if very thin dispersion days set a hard ceiling on achievable daily Spearman.

---

## What we’ll do as soon as you share `target_pairs.csv`

* **Target taxonomy.** Map each `target_k` to instrument(s), market (LME/JPX/FX/US), and **effective lag**. Recompute your key stats **by group**, because modeling and online updates will be **group‑specific**.
* **Antisymmetry tests for pairs.** If both A−B and B−A exist, verify near‑perfect negatives; flag any drift or sign flips.
* **Within‑group rank dynamics.** Redo **rank‑persistence** and **dispersion** within each group; the global 0.45 may mask very different behavior.

---

## CV & inference design we can lock in now (based on your findings)

* **Blocked, purged time CV** with a dedicated **stress fold** (dates 562–575) held out once and included in training in another fold to test robustness both ways.
* **Evaluation metric in CV** = **Sharpe of daily Spearman**, computed on each fold, **unweighted by n\_targets** to mirror the competition.
* **Meta‑policy at inference:** compute per‑day `n_targets` and dispersion; apply **rank‑shrinkage** toward prior‑day ranks when `n_targets` is small or dispersion is thin.

---

## Baseline ingredients we can prep (once labels↔features are aligned)

* **Momentum‑rank baseline:** predict **ŷ\_ranks(t) = ranks(y(t−1))**, legal via label lags; calibrate a **shrinkage λ(n\_targets, dispersion)**.
* **Regime switch:** a simple classifier from yesterday’s **dispersion / turnover / outlier share** to choose between **momentum** and **neutral/mean‑reversion** ranks.
* **Group‑wise heads:** separate heads per (LME, JPX, FX, US‑linked, pairs), blended into the final 424‑vector per day, then **per‑day rank transform** before submit.

---

## Concrete asks from you

1. **Share `target_pairs.csv`** (and, if available now, the **label\_lags** files).
2. From the “next EDA” list above, please produce:

   * the **small‑N day anatomy** for the 5 worst dates,
   * the **rank‑persistence vs dispersion & n\_targets** correlations, and
   * the **top/bottom decile turnover** summary.
3. If you can, include a quick table for **targets 212 and 318**: coverage, lag, group, and basic distribution — I want to sanity‑check those unusually high autocorrs before we lean on them.

Once we have the taxonomy and those three summaries, I’ll lock the **CV split spec** and outline the **first submission baseline** (including the online‑update hook and rank‑shrinkage policy).

This is excellent progress. Here’s a **critical read + concrete plan** to turn this EDA into an evaluation‑faithful baseline and CV.

---

## What your latest findings mean (and what to fix)

**1) Target design is 99% “pairs.”**

* 420/424 targets are **A − B** constructions; only **2 FX singles** (ZARUSD, NOKEUR).
* Lags are evenly split (1–4), so **horizon matters**: we should model **per‑lag** or at least give the model a lag‑indicator.
  **Implication:** A **lower‑dimensional “asset forecaster → target mapper”** is natural: predict the *underlying* asset returns, then linearly map to the 424 targets using the known ±1 coefficients.

**2) Alignment audit = 1.000 correlations across all 424.**

* This *can* be correct (targets are deterministic formulas), but it’s worth **two sanity checks** to rule out a silent bug:

  * **Noise test:** add tiny noise ε to your recomputed series; the correlation with the label should drop measurably.
  * **Holdout build:** recompute a subset using **only pre‑lag data** in a held‑out time window; confirm 1.0 still holds after masking post‑t info.

**3) Two “sticky” singles (targets 212 & 318).**

* High lag‑1 autocorr on **FX\_ZARUSD (lag=3)** and **FX\_NOKEUR (lag=4)** is unusual for return‑like targets; keep an eye on these.
  **Implication:** Build **per‑group heads** (FX vs LME/JPX vs US‑linked) and **per‑lag calibration**—don’t let two sticky series dominate a global model.

**4) Small‑N days are real (as low as 35 targets).**

* These days will explode variance of daily Spearman.
  **Policy:** **Shrink predictions** toward prior‑day ranks when `n_targets` is small (and during **low‑dispersion** days).

**5) Stress regime (562–575) is \~3.7× dispersion.**

* Expect **rank reversals** around these dates.
  **Policy:** Make a dedicated **“stress fold”** in CV and consider **winsorizing labels** during training.

**6) FX triangle residuals are tiny under normal conditions.**

* Your residuals are ≈1e‑6—good as **data integrity** and **stress flags**, not a standalone signal. Track **residual volatility**; it often spikes on stress days.

**7) Tiny inconsistency to resolve.**

* Log shows “Created 0 momentum features” despite announcing momentum creation. Re‑check that step so we aren’t silently skipping the most important baseline.

---

## Modeling blueprint (grounded in your EDA)

### A) CV & metric fidelity

* **Blocked, purged time CV**, 4 folds + **one dedicated stress fold** (562–575) held out in at least one fold.
* **Fold metric = Sharpe of daily Spearman** (unweighted by `n_targets`, like the competition).
* **Diagnostics per fold:** average `n_targets`, dispersion, rank‑persistence—so we know where models win/lose.

### B) Two‑stage architecture (strong, cheap, online‑friendly)

1. **Underlying‑asset forecaster (multi‑task, per‑lag heads).**

   * Targets: the set of **unique assets** that appear in `target_pairs` (much fewer than 424) at horizons 1–4.
   * Inputs: your **normalized log‑return features** + **group flags** (FX/LME/JPX/US) + **yesterday’s ranks** of the *same asset group* + **dispersion/turnover features** from labels.
   * Models: start with **multi‑output Ridge/ElasticNet** and a **compact MLP/TabM**; train **per‑lag** or with a lag embedding.
   * Loss: **rank proxy** (e.g., pairwise hinge/logistic) or **MSE on standardized returns** + **rank consistency regularizer**.

2. **Linear mapping to competition targets.**

   * Use the known **±1 coefficients** to map asset predictions to the **424 A−B targets**.
   * **Per‑day post‑processing:** convert to **per‑day ranks**; apply **shrinkage toward prior‑day ranks** when `n_targets` or dispersion is low.

**Why this helps:**

* Enforces **antisymmetry** and **pair structure** by construction.
* Cuts the problem from 424 outputs to **\~(assets × lags)**—faster training, easier **online updates**.
* Naturally supports **per‑lag behavior** the EDA flagged.

### C) Meta‑policy & regimes

* **Switching/weighting:** small classifier/regressor on **yesterday’s dispersion, turnover, outlier share, n\_targets** to interpolate between **momentum** and **neutral / light‑reversion** predictions.
* **Stress policy:** during dates flagged like 562–575, **increase winsorization**, widen uncertainty bands, and **reduce momentum weight**.

### D) Online learning plan (API‑safe)

* **Daily update** after labels arrive:

  * **Linear head warm‑update** (one or two passes over the last 60–120 days) per lag;
  * Update **feature scalers** with exponential decay (no full re‑fit);
  * Recompute **meta‑policy** thresholds from rolling stats.
* Keep total update time << 60s; pre‑load models in the un‑timed first call.

---

## Specific next steps I recommend (you run, then share summaries)

1. **Sanity checks on alignment** (two quick tests above).
2. **Unique‑asset inventory and mapping matrix.**

   * List the **unique assets** in `target_pairs` and build the **sparse mapping M** where `y = M · r_future` (per lag). Report asset count by group and by lag.
3. **Fix the momentum feature step** and rerun **rank‑IC** on:

   * **Yesterday’s target ranks** (by group),
   * **Asset‑level 1‑day and 5‑day momentum** (legal with lags),
   * **Dispersion/turnover indicators** (yesterday).
     Provide: top‑20 ICs and **Sharpe of daily IC** (mean/std across days), not just mean IC.
4. **Small‑N day anatomy** for the five worst dates (254, 513, 774, 1122, 1810):

   * `n_targets`, dispersion, rank‑persistence, asset‑class mix.
   * I’ll translate that into a **shrinkage schedule λ(n\_targets, dispersion)**.
5. **Stress fold definition**: confirm exact inclusive date\_ids for the stress block (562–575) and share dispersion/persistence vs adjacent windows.

---

## Baselines we will implement next (in order)

1. **Pure label‑momentum ranker:** ŷ(t) = ranks(y(t−1)) with **adaptive shrinkage**; this sets a hard baseline.
2. **Asset‑forecaster → pair‑mapper (Ridge):** per‑lag multi‑output Ridge on asset returns → mapped to 424 → per‑day ranks.
3. **Compact MLP/TabM** replacement for Ridge to capture mild nonlinearity; same mapper + post‑processing.
4. **Meta‑blend** (momentum vs forecaster) conditioned on **n\_targets / dispersion**.
5. **Online updates** on 2–4.

Everything above is **fast, robust, and deployable** within the 1‑minute daily SLA.

---

### One final caution

* Keep **all transformations time‑legal**: per‑day cross‑sectional normalization is fine (uses only day‑t cross‑section), but momentum/rolling features must **respect each target’s lag** and use **information available strictly before prediction time** in your mock‑API loop.

If you send back (1) the mapping matrix stats, (2) the fixed momentum ICs with Sharpe, and (3) the small‑N day anatomy, I’ll lock the CV spec and sketch the exact feature list + training recipe for baseline #2.

I loaded your actual files (`train.csv`, `train_labels.csv`, `target_pairs.csv`) and independently validated the key findings. Here’s what I can confirm, what’s new, and exactly what we should do next.

---

## What I validated (numbers match your EDA)

* **Shapes & schema.** `train_labels.csv` = 1,917 rows × 424 targets (plus `date_id`). `target_pairs.csv` = 424 rows with lags in {1,2,3,4} split evenly (106 each). `train.csv` = 1,917 × 558.
* **Coverage.** Mean target coverage ≈ 89.4%; min ≈ 82.1%; max = 100%. Per‑day available targets: mean ≈ 379, median = 424, min = 35, max = 424.
* **Rank persistence & dispersion.** Mean daily cross‑section Spearman(t vs t−1) ≈ **0.452** (std ≈ 0.210; min ≈ −0.636; max ≈ 0.909). Mean daily cross‑section std ≈ **0.0281** (min ≈ 0.0051; max ≈ 0.200).
* **Stress window.** Dates **562–575** show 4.35× higher dispersion vs overall (your 3.7× was method‑choice; either way: “large”).
* **Small‑N days.** Worst days (254, 513, 774, 1122, 1810) have **35–38** targets only.
* **Two sticky FX singles.** `target_212` (FX\_ZARUSD, lag 3) lag‑1 autocorr ≈ **0.637**; `target_318` (FX\_NOKEUR, lag 4) ≈ **0.723**.

---

## New, high‑leverage structural insight (this is big)

**The 424 targets are a linear transform of \~106 underlying assets per lag, and the transform is invertible.**

* From `target_pairs.csv` there are **106 unique assets** appearing in the pair strings (FX, LME, JPX, US\_Stock).
* For each lag ℓ ∈ {1,2,3,4}, you can form a **106×106 signed mapping matrix** Mℓ where each target row has +1 for asset A and −1 for asset B (or +1 for a single).
* I computed Mℓ for each lag: **full rank (rank = 106)** with condition numbers \~77–95.
* **Implication:** Instead of modeling 424 targets directly, we can model **asset‑level returns at each lag** (a 106‑dimensional problem) and map to targets via **yℓ = Mℓ · rℓ**. That enforces pair antisymmetry by construction, simplifies online updates, and makes per‑lag behavior explicit.

This supports the two‑stage design we discussed (asset forecaster → target mapper) and justifies **per‑lag heads**.

---

## Anatomy of the worst “small‑N” days (why our meta‑policy matters)

* **date\_id 254, 513, 774, 1810:** n\_targets = 35, dispersion ≈ 0.008–0.014; class mix ≈ **33 “FX+JPX” pairs + 2 FX singles**; rank‑persistence is either **very high (\~0.80)** or **undefined** if overlap is too small.
* **date\_id 1122:** n\_targets = 38; dispersion ≈ 0.041; mix is **JPX+US**/FX+JPX/FX plus 2 US singles; rank‑persistence slightly negative (\~−0.05).
  **Takeaway:** these days are dominated by **JPX/FX**; we should **shrink** predictions toward prior‑day ranks when `n_targets` is small or dispersion is thin.

---

## One discrepancy you should fix before modeling

Your log shows “Created 0 momentum features” even though momentum generation ran. That’s likely a pipeline bug (e.g., overwrite or naming filter). Momentum ranks are the most informative baseline here—let’s correct that step.

---

## About the “perfect alignment = 1.000” flag

I tried to reproduce targets by **L‑day log‑return spreads** (A vs B) and correlations hovered near zero, which means the **competition’s label formula is not simple L‑day log‑return differences on levels**. Your alignment job likely used the **intended formula** (as given by the organizers) and came out 1.000 across all 424—good. To be absolutely safe:

* **Noise test:** add tiny ε to your recomputed labels; corr should drop slightly.
* **Holdout rebuild:** recompute labels on a held‑out window using only **pre‑lag** data; expect corr \~1.000 again.
  If both pass, we trust your alignment module.

---

## What we should build now (with your data realities baked in)

### 1) Evaluation‑faithful CV

* **Blocked, purged time CV**, 4 folds + a **dedicated stress fold** (562–575) held out in one configuration and included in another.
* **Fold metric:** **Sharpe of daily Spearman** (unweighted by n\_targets).
* **Per‑fold diagnostics:** mean `n_targets`, dispersion, rank‑persistence, plus lag mix—so we know which regimes each model handles.

### 2) Two‑stage baseline (fast, online‑friendly)

* **Stage A (asset forecaster):** multi‑output Ridge/ElasticNet (and a compact MLP/TabM) to predict the **106 asset returns**, **per lag** (either separate heads or a lag embedding). Inputs: your normalized log‑return features; group flags; previous‑day group ranks; simple regime features (yesterday’s dispersion, top/bottom decile turnover, `n_targets`).
* **Stage B (target mapper):** apply **yℓ = Mℓ · rℓ** for each lag; concatenate into the 424‑vector and **per‑day rank** it before submit.
* **Meta‑blend:** interpolate momentum vs forecaster outputs using a small regressor on **(n\_targets, dispersion, turnover)**.
* **Online updates:** daily warm‑update the linear heads over the last 60–120 days and refresh regime features (well inside the 60‑second SLA).

### 3) Post‑processing aligned to the metric

* **Per‑day rank transform** (ties safe), with **adaptive shrinkage** toward prior‑day ranks when `n_targets` is small or dispersion is low.

---

## Concrete to‑dos (sequential)

1. **Fix momentum feature creation** and re‑report the top‑20 **rank‑ICs** + **Sharpe of daily IC** for: prior‑day target ranks (by group), 1‑day asset momentum, 5‑day momentum, dispersion/turnover.
2. **Export the mapping matrices** stats: confirm (106×106, full rank) per lag in your environment (you’ll use them at inference).
3. **Define the CV folds** explicitly (date ranges), with one **“stress fold”** spanning 562–575.
4. **Implement Baseline #1** (pure label‑momentum ranks with shrinkage) to set a floor.
5. **Implement Baseline #2** (asset‑forecaster → target‑mapper with Ridge). Measure out‑of‑fold **Sharpe(daily Spearman)** and compare to #1 across folds and regimes.

Once you send (1) the fixed momentum ICs and (3) the exact fold date ranges, I’ll lock the feature list and hyperparameters for Baseline #2 and outline the online‑update hooks so you can ship a first submission quickly.


---

# test.csv

What your test results imply (key adjustments)
Public LB == last 90 train days (1827–1916), all scored. We must treat this as a strict holdout: no hyper‑param tuning, no feature selection, no threshold fitting on these dates.

Small‑N days in public exist (e.g., 1891 and 1916 have ~48 targets). Expect high variance in daily Spearman. Our inference policy must shrink ranks toward a prior more aggressively on such days—but at inference we won’t know n_targets (labels are unavailable). So shrinkage must be driven by feature‑side proxies (e.g., per‑day cross‑sectional dispersion of feature returns) and not by label counts.

---

# test_label_lag_*.csv

Fantastic—this is exactly the level of rigor we need. Your results give us a **clean, deterministic arrival schedule** and a **realistic picture of coverage**. Below is a **concrete online‑learning plan** you can implement straight away, plus guardrails for low‑rank/low‑coverage days.

---

## What your analysis locks in

* **Arrival offsets (constant):**
  lag‑1: +2 days, lag‑2: +3, lag‑3: +4, lag‑4: +5 (via `date_id − label_date_id`).
  Meaning: on public day `P`, the newest labels you may legally use are for `label_date_id = P − (1+lag_offset)`.
* **Coverage:** \~89% per lag overall; some days drop to 9–15 targets. No zero‑coverage days.
* **Consistency:** 100% match vs `train_labels` → safe to use for updates.
* **Mapping feasibility:** with missing targets, **\~19–22% of days per lag are low‑rank** for direct inversion; we must support **regularized or target‑space updates** (see below).

---

## Online‑update policy (per day P)

1. **Ingest newly arrived labels (by lag).**
   For each lag ℓ ∈ {1,2,3,4}, read rows where `date_id == P`. These rows reveal labels for `label_date_id = P − offset(ℓ)` (offsets = 2,3,4,5).
   Store `(X[label_date_id], yℓ[label_date_id], maskℓ[label_date_id])` in a rolling buffer of length `W` (e.g., 60–120 days).

2. **Choose the update route (asset‑space vs target‑space):**

   **A) Asset‑space recover → update:**

   * If the day’s observed target subset for lag ℓ yields **full rank** (or near‑full with numeric tolerance) for the sub‑matrix `Mℓ_sub`, recover asset returns `rℓ_hat` by solving
     minimize ‖Mℓ\_sub r − yℓ\_obs‖², with **Tikhonov** regularization if needed:
     solution = (MᵀM + αI)⁻¹ Mᵀ y (α ≈ 1e−3…1e−2).
   * Use `(X[label_date_id], rℓ_hat)` to **SGD‑update** the lag‑ℓ head.

   **B) Target‑space direct update (works even when low‑rank):**

   * Define model outputs in asset space `r̂ℓ = fℓ(X)`. Loss on observed targets:
     L = ‖Mℓ\_sub r̂ℓ − yℓ\_obs‖².
     Gradient wrt `r̂ℓ` is `∇ = Mℓ_subᵀ (Mℓ_sub r̂ℓ − yℓ_obs)`.
   * Backprop this gradient through the lag‑ℓ head (linear or MLP).
   * This avoids explicit inversion and is **robust on low‑coverage days**.

   **Rule of thumb:** prefer **target‑space updates** as the default; fall back to asset‑space recovery if you specifically want refreshed targets for diagnostics.

3. **Weighting & selection for the update minibatch.**

   * **Recency weight:** w\_time(d) = exp(−d/τ) with τ ≈ 30–60 days (d = days since label\_date\_id).
   * **Coverage weight:** w\_cov = min(1, n\_obs/106), clipped below at 0.2 for stability.
   * **Lag weight:** lighter weight for longer delays (e.g., w\_lag = {1.0, 0.9, 0.8, 0.7}).
   * **Use 1–2 passes** over the weighted buffer; skip lag/day if `n_obs < n_min` (e.g., 20).

4. **Scaler maintenance.**
   Update robust‑scaler running stats with EMA (feature‑only, label‑free). Keep this step O(#features).

5. **Budget guard (≤60 s total).**

   * Always update **linear heads first** (fast).
   * Update the **compact MLP** only if time remains.
   * If time is tight, **skip** lags with `n_obs < n_min`, or do **one pass** only.

---

## Inference/post‑processing on day P

* Predict per lag: `r̂ℓ(P)` → map to targets `ŷℓ(P) = Mℓ r̂ℓ(P)` → concatenate 4 blocks.
* **Per‑day rank transform** across 424 outputs.
* **Adaptive shrinkage** toward prior‑day ranks:
  λ(P) = clip(a + b · D\_feat(P), 0, 1), where `D_feat(P)` is a **feature‑only dispersion proxy** (e.g., median absolute z‑move across FX/LME/JPX).
  Use λ learned on train (0–1826) via CV; **do not** fit λ on public days.

---

## Thresholds & defaults (start here, tune on CV)

* Rolling window `W`: 90 days (try 60/120 in sensitivity).
* Ridge α for asset‑space recovery: 1e−3 → 1e−2.
* `n_min` for updating a lag on a day: 20 targets (try 15–30).
* Recency half‑life: 30 days (τ ≈ 43.3).
* Lag weights: {1.0, 0.9, 0.8, 0.7}.
* Winsorize labels used for updates at **±5× MAD** per target (stabilizes spike days).
* Skip updating on days flagged as **condition‑poor** (condition number > 1e5 for asset‑space solve) or **extreme low coverage**.

---

## Function‑level checklist (drop into the notebook)

* `ingest_new_labels(P) → dict{lag → (label_date_id, y_obs, mask)}`
  Validates offsets, returns only rows with `date_id == P`.

* `select_update_rows(lag, buffer) → list of indices`
  Applies coverage/recency/lag weights; enforces `n_min`.

* `target_space_update_step(lag, X, y_obs, mask)`
  Computes `M_sub`, residuals, gradient `M_subᵀ(M_sub r̂ − y_obs)`, and does one optimizer step.

* `asset_space_recover(lag, y_obs, mask) → r_recovered or None`
  Uses `(M_subᵀM_sub + αI)⁻¹ M_subᵀ y_obs`; returns None if ill‑conditioned.

* `update_linear_head(lag, batch)` and `update_mlp_head(lag, batch)`
  One or two passes; respect wall‑clock budget.

* `rank_and_shrink(ŷ_day, features_day) → final 424‑vector`
  Per‑day ranks + λ from feature‑dispersion proxies.

* `time_budget_guard(start_time)`
  Decides to stop MLP updates / skip sparse lags when time is close to the cap.

---

## Guardrails (based on your findings)

* **First public day (1827):** updates are possible but **sparse** (each lag \~38–39 targets). Use target‑space updates with strong regularization.
* **Low‑coverage cluster (around 1891–1894, 1916–1919 by `date_id` in lag files):** expect 9–15 targets for some lags; **weight down or skip** those updates.
* **Mapping low‑rank (\~19–22% days):** do **not** require asset recovery; rely on target‑space loss.
* **Never** read labels with `date_id > P` (strict arrival rule you verified).
* **Reproducibility:** log per‑day counts, offsets, condition numbers, time spent, and whether each lag updated.

---

## Quick offline validations to run now

* **Offset replay:** ensure the ingest returns exactly the `(lag, label_date_id)` pairs you listed for each P.
* **With/without updates:** simulate 1827→1916 and track daily Spearman on a holdout (train) mirror to confirm uplift.
* **Latency:** worst‑case per‑day timing with all lags updating; ensure ≪ 60 s.
* **Stability:** turn off asset recovery entirely and verify target‑space updates alone are stable; then add recovery as an optional improvement.

---

If you want, I can turn this into a **step‑by‑step implementation plan** (file layout, artifact names, and the exact order of calls inside `predict()`), but you’re ready to code with the bullets above. Once your first run is ready, share logs for: per‑day update counts, time, and any days skipped; I’ll help tighten thresholds and weights.
