================================================================================
TARGET TAXONOMY & ALIGNMENT AUDIT
================================================================================
Target pairs structure:
     target  lag                                               pair
0  target_0    1                              US_Stock_VT_adj_close
1  target_1    1               LME_PB_Close - US_Stock_VT_adj_close
2  target_2    1                        LME_CA_Close - LME_ZS_Close
3  target_3    1                        LME_AH_Close - LME_ZS_Close
4  target_4    1     LME_AH_Close - JPX_Gold_Standard_Futures_Close
5  target_5    1  LME_ZS_Close - JPX_Platinum_Standard_Futures_C...
6  target_6    1                        LME_PB_Close - LME_AH_Close
7  target_7    1              LME_ZS_Close - US_Stock_VYM_adj_close
8  target_8    1  US_Stock_IEMG_adj_close - JPX_Gold_Standard_Fu...
9  target_9    1                           FX_AUDJPY - LME_PB_Close

Target pairs columns: ['target', 'lag', 'pair']
Unique lags: [np.int64(1), np.int64(2), np.int64(3), np.int64(4)]

📊 TARGET TAXONOMY SUMMARY:
Total targets: 424
Asset class distribution:
  FX: 2 targets
  US: 2 targets
  PAIR: 420 targets

Lag distribution:
  Lag 1: 106 targets
  Lag 2: 106 targets
  Lag 3: 106 targets
  Lag 4: 106 targets

🔍 SUSPICIOUS TARGET ANALYSIS:
target_212:
  Lag: 3 days
  Asset class: FX
  Pair info: FX_ZARUSD
  Is pair: False
target_318:
  Lag: 4 days
  Asset class: FX
  Pair info: FX_NOKEUR
  Is pair: False

================================================================================
FEATURE ASSET CLASS ANALYSIS
================================================================================
Total feature columns: 557
📊 FEATURE DISTRIBUTION BY ASSET CLASS:
  LME: 4 features (0.7%)
  JPX: 40 features (7.2%)
  FX: 38 features (6.8%)
  US_Stock: 475 features (85.3%)

🚨 IMBALANCE ANALYSIS:
US Stock dominance: 85.3% of all features

📋 SAMPLE FEATURES BY CLASS:

LME (showing 4/4):
  - LME_AH_Close
  - LME_CA_Close
  - LME_PB_Close
  - LME_ZS_Close

JPX (showing 5/40):
  - JPX_Gold_Mini_Futures_Open
  - JPX_Gold_Rolling-Spot_Futures_Open
  - JPX_Gold_Standard_Futures_Open
  - JPX_Platinum_Mini_Futures_Open
  - JPX_Platinum_Standard_Futures_Open

FX (showing 5/38):
  - FX_AUDJPY
  - FX_AUDUSD
  - FX_CADJPY
  - FX_CHFJPY
  - FX_EURAUD

US_Stock (showing 5/475):
  - US_Stock_ACWI_adj_open
  - US_Stock_AEM_adj_open
  - US_Stock_AG_adj_open
  - US_Stock_AGG_adj_open
  - US_Stock_ALB_adj_open

🔍 OHLC PATTERN ANALYSIS:
OHLC field counts:
  _Open: 6 features
  _High: 6 features
  _Low: 6 features
  _Close: 10 features
  _adj_close: 95 features
Total OHLC-related features: 123/557 (22.1%)

📈 DATA AVAILABILITY BY ASSET CLASS (sample check):
  LME: 100.0% available (sample of 4)
  JPX: 100.0% available (sample of 10)
  FX: 100.0% available (sample of 10)
  US_Stock: 100.0% available (sample of 10)

================================================================================
TARGET-FEATURE ALIGNMENT AUDIT (LEAK DETECTION)
================================================================================
🔍 VERIFYING TARGET CALCULATION ALIGNMENT USING EXACT FORMULA...
Checking alignment for all 424 targets...

📋 target_0 (lag=1): US_Stock_VT_adj_close

📋 target_1 (lag=1): LME_PB_Close - US_Stock_VT_adj_close
   Assets: LME_PB_Close - US_Stock_VT_adj_close
   Alignment: ✅ PASS (corr=1.000000)

📋 target_50 (lag=1): US_Stock_SLB_adj_close - JPX_Gold_Standard_Futures_Close
   Assets: US_Stock_SLB_adj_close - JPX_Gold_Standard_Futures_Close
   Alignment: ✅ PASS (corr=1.000000)

📋 target_100 (lag=1): US_Stock_SCCO_adj_close - LME_AH_Close
   Assets: US_Stock_SCCO_adj_close - LME_AH_Close
   Alignment: ✅ PASS (corr=1.000000)

📋 target_212 (lag=3): FX_ZARUSD
   Single asset: FX_ZARUSD
   Alignment: ✅ PASS (corr=1.000000)

📋 target_318 (lag=4): FX_NOKEUR
   Single asset: FX_NOKEUR
   Alignment: ✅ PASS (corr=1.000000)

📋 target_400 (lag=4): FX_NOKJPY - LME_CA_Close
   Assets: FX_NOKJPY - LME_CA_Close
   Alignment: ✅ PASS (corr=1.000000)

📊 COMPREHENSIVE ALIGNMENT AUDIT RESULTS:
Total targets checked: 424
✅ Alignment PASSED: 424
❌ Alignment FAILED: 0
⚠️  Insufficient data: 0
🔍 Missing assets: 0

✅ NO LEAKAGE DETECTED - All targets properly aligned!

================================================================================
VERIFICATION: DID WE REALLY CHECK ALL TARGETS?
================================================================================
Total targets in target_pairs.csv: 424
Total targets in target_taxonomy: 424
Total targets in alignment_results: 424

Targets missing from results: 0
Extra targets in results: 0

📊 DETAILED STATUS BREAKDOWN:
  PASS: 424 targets

🔍 SAMPLE OF ACTUAL RESULTS (first 10):
  target_0: PASS (corr=1.000000, n=1787)
  target_1: PASS (corr=1.000000, n=1744)
  target_2: PASS (corr=1.000000, n=1831)
  target_3: PASS (corr=1.000000, n=1831)
  target_4: PASS (corr=1.000000, n=1631)
  target_5: PASS (corr=1.000000, n=1631)
  target_6: PASS (corr=1.000000, n=1831)
  target_7: PASS (corr=1.000000, n=1744)
  target_8: PASS (corr=1.000000, n=1587)
  target_9: PASS (corr=1.000000, n=1831)

📈 CORRELATION STATISTICS:
  Valid correlations: 424
  Mean correlation: 1.000000
  Min correlation: 1.000000
  Max correlation: 1.000000
  Std correlation: 0.000000
  Perfect correlations (1.0): 424/424
⚠️  SUSPICIOUS: All correlations are exactly 1.0!
This suggests either:
  1. Perfect alignment (good)
  2. Bug in calculation (bad)
  3. Targets are identical to calculated values (expected)

================================================================================
SERIES HEALTH CHECK & COVERAGE ANALYSIS
================================================================================
Analyzing 557 feature series...
📊 OVERALL HEALTH SUMMARY:
Total series analyzed: 557
✅ Healthy series: 539
⚠️  Series with issues: 18

📈 COVERAGE STATISTICS:
Mean coverage: 96.0%
Median coverage: 96.7%
Min coverage: 12.9%
Max coverage: 100.0%

🚨 LOW COVERAGE SERIES (<50%): 5
Worst offenders:
  US_Stock_GOLD_adj_open: 12.9% (US_Stock)
  US_Stock_GOLD_adj_high: 12.9% (US_Stock)
  US_Stock_GOLD_adj_low: 12.9% (US_Stock)
  US_Stock_GOLD_adj_close: 12.9% (US_Stock)
  US_Stock_GOLD_adj_volume: 12.9% (US_Stock)

🔒 CONSTANT SERIES: 0
🔒 NEAR-CONSTANT SERIES (≤3 values): 0

📋 HEALTH BY ASSET CLASS:
  LME: 4 series, 100.0% healthy, 97.4% avg coverage
  JPX: 40 series, 100.0% healthy, 94.0% avg coverage
  FX: 38 series, 100.0% healthy, 100.0% avg coverage
  US_Stock: 475 series, 96.2% healthy, 95.8% avg coverage

💡 RECOMMENDED FOR DROPPING: 5 series
Top candidates:
  US_Stock_GOLD_adj_open: 12.9% coverage (LOW_COVERAGE, LONG_GAPS)
  US_Stock_GOLD_adj_high: 12.9% coverage (LOW_COVERAGE, LONG_GAPS)
  US_Stock_GOLD_adj_low: 12.9% coverage (LOW_COVERAGE, LONG_GAPS)
  US_Stock_GOLD_adj_close: 12.9% coverage (LOW_COVERAGE, LONG_GAPS)
  US_Stock_GOLD_adj_volume: 12.9% coverage (LOW_COVERAGE, LONG_GAPS)

================================================================================
FX TRIANGLE PARITY ANALYSIS & ARBITRAGE FEATURES
================================================================================
Found 38 FX pairs:
  FX_AUDJPY
  FX_AUDUSD
  FX_CADJPY
  FX_CHFJPY
  FX_EURAUD
  FX_EURGBP
  FX_EURJPY
  FX_EURUSD
  FX_GBPAUD
  FX_GBPJPY
  ... and 28 more

💱 FX MARKET STRUCTURE:
Total currency pairs: 38
Unique currencies: 10
Currencies: ['AUD', 'CAD', 'CHF', 'EUR', 'GBP', 'JPY', 'NOK', 'NZD', 'USD', 'ZAR']

🔍 SEARCHING FOR TRIANGULAR ARBITRAGE OPPORTUNITIES...
Found 76 triangular arbitrage opportunities:

📐 Triangle 1: NZD-AUD-CHF
   Pairs: FX_AUDNZD, FX_AUDCHF, FX_NZDCHF
   Sample rates: 0.967034 × 0.675297 × 1.531311 = 1.000000
   Parity residual: 0.00000005
   Residual stats: mean=0.00000058, std=0.00000041, max=0.00000214

📐 Triangle 2: NZD-AUD-GBP
   Pairs: FX_AUDNZD, FX_GBPAUD, FX_GBPNZD
   Sample rates: 0.967034 × 0.531772 × 1.944613 = 1.000000
   Parity residual: 0.00000028
   Residual stats: mean=0.00000028, std=0.00000020, max=0.00000095

📐 Triangle 3: NZD-AUD-CAD
   Pairs: FX_AUDNZD, FX_AUDCAD, FX_NZDCAD
   Sample rates: 0.967034 × 0.933502 × 1.107753 = 1.000000
   Parity residual: 0.00000000
   Residual stats: mean=0.00000044, std=0.00000031, max=0.00000152

📐 Triangle 4: NZD-AUD-USD
   Pairs: FX_AUDNZD, FX_AUDUSD, FX_NZDUSD
   Sample rates: 0.967034 × 0.735600 × 1.405778 = 1.000000
   Parity residual: 0.00000011
   Residual stats: mean=0.00000046, std=0.00000035, max=0.00000185

📐 Triangle 5: NZD-AUD-EUR
   Pairs: FX_AUDNZD, FX_EURAUD, FX_EURNZD
   Sample rates: 0.967034 × 0.622614 × 1.660884 = 1.000000
   Parity residual: 0.00000000
   Residual stats: mean=0.00000029, std=0.00000020, max=0.00000094

💡 TRIANGLE ARBITRAGE INSIGHTS:
✅ Found 76 potential arbitrage triangles
📊 Created 5 triangle residual features
🎯 These residuals can serve as:
   1. Data quality indicators (large residuals = data errors)
   2. Arbitrage opportunity signals
   3. Market stress indicators
   4. Additional features for commodity prediction

✅ All major currencies present in dataset

================================================================================
SCALE NORMALIZATION & LOG-RETURNS CONVERSION
================================================================================
🔍 ANALYZING CURRENT SCALE HETEROGENEITY...
📊 CURRENT SCALE RANGES BY ASSET CLASS:

LME:
  LME_CA_Close:
    Range: 4630.00 to 10889.00
    Mean: 7886.75, Std: 1515.50
  LME_PB_Close:
    Range: 1585.50 to 2681.00
    Mean: 2087.63, Std: 184.82

JPX:
  JPX_Gold_Standard_Futures_Close:
    Range: 4174.00 to 15779.00
    Mean: 7507.67, Std: 2838.38
  JPX_Platinum_Standard_Futures_Close:
    Range: 2180.00 to 5422.00
    Mean: 3750.70, Std: 670.89

FX:
  FX_EURUSD:
    Range: 0.96 to 1.25
    Mean: 1.12, Std: 0.06
  FX_USDJPY:
    Range: 102.68 to 161.61
    Mean: 124.21, Std: 18.24
  FX_GBPJPY:
    Range: 125.97 to 207.70
    Mean: 159.65, Std: 20.70

US_Stock:
  US_Stock_ACWI_adj_close:
    Range: 49.78 to 124.43
    Mean: 84.95, Std: 18.23
  US_Stock_AEM_adj_close:
    Range: 28.06 to 123.05
    Mean: 53.22, Std: 16.99
  US_Stock_FCX_adj_close:
    Range: 5.02 to 54.08
    Mean: 27.72, Std: 13.52

🚨 SCALE HETEROGENEITY PROBLEM:
Smallest range: 0.29
Largest range: 11605.00
Range ratio: 39767x difference!

🔄 CONVERTING ALL SERIES TO LOG-RETURNS...
📋 FEATURE CATEGORIZATION:
  Price-like features: 446
  Volume-like features: 101
  Other features: 10

⚙️  CREATING LOG-RETURNS DATASET...
✅ CONVERSION COMPLETED:
  Price features → log-returns: 446
  Volume features → log-diffs: 101
  Other features kept raw: 10
  Errors: 0
  Total features in log-returns dataset: 557

📊 LOG-RETURNS SCALE ANALYSIS:
  LME_AH_Close_logret...: mean=0.000026, std=0.013254, range=[-0.0731, 0.0604]
  LME_CA_Close_logret...: mean=0.000069, std=0.012607, range=[-0.0807, 0.0687]
  LME_PB_Close_logret...: mean=-0.000179, std=0.013727, range=[-0.0735, 0.0631]
  LME_ZS_Close_logret...: mean=-0.000162, std=0.015724, range=[-0.0648, 0.0727]
  JPX_Gold_Mini_Futures_Open_log...: mean=0.000538, std=0.008267, range=[-0.0543, 0.0594]

✅ SCALE IMPROVEMENT:
  Log-returns std ratio: 2.3x (much better than 39767x for levels)
  Typical log-return std: 0.0129

💡 NEXT STEPS:
  1. ✅ Converted to log-returns space
  2. 🔄 Apply robust z-scores (median/IQR) per series
  3. 🔄 Per-day cross-sectional normalization within asset groups
  4. 🔄 Convert predictions to per-day ranks

================================================================================
ROBUST Z-SCORES & CROSS-SECTIONAL NORMALIZATION
================================================================================
🔄 STEP 1: APPLYING ROBUST Z-SCORES PER SERIES...
Processing features...
✅ ROBUST Z-SCORE RESULTS:
  Features processed: 557
  Constant series detected: 0
  Errors: 0

🔄 STEP 2: PER-DAY CROSS-SECTIONAL NORMALIZATION...
📊 FEATURE GROUPS FOR CROSS-SECTIONAL NORMALIZATION:
  LME: 4 features
  JPX: 40 features
  FX: 38 features
  US_Stock: 475 features
  Processing LME group (4 features)...
  Processing JPX group (40 features)...
  Processing FX group (38 features)...
  Processing US_Stock group (475 features)...
✅ CROSS-SECTIONAL NORMALIZATION RESULTS:
  Asset groups processed: 4
  Total features processed: 557

📊 FINAL NORMALIZED FEATURE ANALYSIS:
  LME_AH_Close_logret_rz_cs...: mean=0.0173, std=1.0162, range=[-1.73, 1.73]
  LME_CA_Close_logret_rz_cs...: mean=0.0127, std=0.9748, range=[-1.73, 1.73]
  LME_PB_Close_logret_rz_cs...: mean=-0.0203, std=1.0574, range=[-1.73, 1.73]
  LME_ZS_Close_logret_rz_cs...: mean=-0.0097, std=0.9488, range=[-1.73, 1.73]
  JPX_Gold_Mini_Futures_Open_logret_r...: mean=-0.0506, std=0.8946, range=[-2.49, 2.60]

✅ FINAL SCALE HOMOGENEITY:
  Normalized std ratio: 1.37x
  Median normalized std: 0.9066

💡 NORMALIZATION PIPELINE COMPLETED:
  1. ✅ Raw levels → Log-returns (39,767x → 2.3x scale improvement)
  2. ✅ Robust z-scores per series (median/IQR)
  3. ✅ Cross-sectional normalization within asset groups
  4. 🔄 Ready for rank-based prediction and modeling

Dataset shapes:
  Original: (1917, 558)
  Log-returns: (1917, 558)
  Final normalized: (1917, 558)

================================================================================
SIGNAL SCOUTING & RANK-IC ANALYSIS (LEAK-FREE)
================================================================================
🔍 COMPUTING LEAK-FREE SIGNAL ANALYSIS...
📊 ANALYSIS SCOPE:
  Features: 557
  Targets: 424
  Time periods: 1917

⚙️  CREATING MOMENTUM FEATURES...
  Creating momentum for LME: 4 series
  Creating momentum for JPX: 24 series
  Creating momentum for FX: 38 series
  Creating momentum for US_Stock: 380 series
  Created 0 momentum features
  Total features with momentum: 557

📈 COMPUTING DAILY RANK INFORMATION COEFFICIENTS...
  Sampling 30 features and 30 targets
    Processing date 1/1917...
    Processing date 201/1917...
    Processing date 401/1917...
    Processing date 601/1917...
    Processing date 801/1917...
    Processing date 1001/1917...
    Processing date 1201/1917...
    Processing date 1401/1917...
    Processing date 1601/1917...
    Processing date 1801/1917...
  Valid days for analysis: 1633

📊 RANK-IC SUMMARY ANALYSIS...
📋 TOP FEATURE-TARGET CORRELATIONS:
Found 100 valid feature-target pairs

Top 10 strongest signals:
   1. LME_ZS_Close_logret_rz_cs           → target_3   | IC=+0.0715 (n=1569)
   2. JPX_Platinum_Standard_Futures_...   → target_0   | IC=-0.0666 (n=1552)
   3. JPX_Platinum_Mini_Futures_Open...   → target_0   | IC=-0.0661 (n=1552)
   4. LME_ZS_Close_logret_rz_cs           → target_2   | IC=+0.0623 (n=1569)
   5. JPX_Platinum_Standard_Futures_...   → target_8   | IC=-0.0606 (n=1385)
   6. JPX_Platinum_Mini_Futures_Open...   → target_7   | IC=+0.0605 (n=1552)
   7. JPX_Platinum_Standard_Futures_...   → target_7   | IC=+0.0568 (n=1552)
   8. JPX_Platinum_Mini_Futures_Open...   → target_8   | IC=-0.0541 (n=1385)
   9. LME_PB_Close_logret_rz_cs           → target_9   | IC=+0.0488 (n=1569)
  10. JPX_Gold_Standard_Futures_Open...   → target_8   | IC=-0.0481 (n=1385)

📈 SIGNAL STRENGTH BY ASSET CLASS:
  LME       :  40 signals, mean=0.0260, max=0.0715
  JPX       :  60 signals, mean=0.0219, max=0.0666

💡 SIGNAL SCOUTING INSIGHTS:
  🎯 Strongest signal: 0.0715
  📊 Median signal strength: 0.0198
  📈 Total feature-target relationships analyzed: 100
  ✅ Ready for baseline model development

🔄 NEXT: Ready for baseline momentum-rank model and CV setup

================================================================================
CALENDAR HEATMAPS & COVERAGE ANALYSIS
================================================================================
🗓️  ANALYZING TEMPORAL PATTERNS IN DATA COVERAGE...
📊 TEMPORAL COVERAGE ANALYSIS:
  Date range: 0 to 1916
  Total periods: 1917

📈 LME TEMPORAL COVERAGE:
  Features: 4
  Mean daily coverage: 95.6%
  Coverage range: 0.0% - 100.0%
  Days with <90% coverage: 85
  Worst coverage days: [0, 63, 64, 65, 89] (coverage: ['0.0%', '0.0%', '0.0%', '0.0%', '0.0%'])

📈 JPX TEMPORAL COVERAGE:
  Features: 40
  Mean daily coverage: 90.4%
  Coverage range: 0.0% - 100.0%
  Days with <90% coverage: 207
  Worst coverage days: [0, 1, 2, 4, 5] (coverage: ['0.0%', '0.0%', '25.0%', '0.0%', '25.0%'])

📈 FX TEMPORAL COVERAGE:
  Features: 38
  Mean daily coverage: 99.9%
  Coverage range: 0.0% - 100.0%
  Days with <90% coverage: 1
  Worst coverage days: [0] (coverage: ['0.0%'])

📈 US_Stock TEMPORAL COVERAGE:
  Features: 475
  Mean daily coverage: 92.4%
  Coverage range: 0.0% - 100.0%
  Days with <90% coverage: 129
  Worst coverage days: [0, 9, 10, 34, 35] (coverage: ['0.0%', '0.0%', '0.0%', '0.0%', '0.0%'])

🎯 TARGET AVAILABILITY PATTERNS:
  Mean targets per day: 379.2
  Target count range: 35 - 424
  Mean target coverage: 89.4%

🔍 SMALL-N TARGET DAYS (<100 targets):
  Number of small-N days: 28
  Worst 10 days:
    Day 254 (date_id=254): 35 targets (8.3%)
    Day 513 (date_id=513): 35 targets (8.3%)
    Day 774 (date_id=774): 35 targets (8.3%)
    Day 1810 (date_id=1810): 35 targets (8.3%)
    Day 1122 (date_id=1122): 38 targets (9.0%)
    Day 1380 (date_id=1380): 40 targets (9.4%)
    Day 62 (date_id=62): 48 targets (11.3%)
    Day 103 (date_id=103): 48 targets (11.3%)
    Day 336 (date_id=336): 48 targets (11.3%)
    Day 361 (date_id=361): 48 targets (11.3%)

🚨 STRESS REGIME ANALYSIS (dates 562-575):
  Stress period length: 14 days
  Mean targets during stress: 403.3
  Mean coverage during stress: 95.1%
  vs Overall mean targets: 379.2
  vs Overall mean coverage: 89.4%
  Stress/Overall target ratio: 1.06

📅 TEMPORAL PATTERN ANALYSIS:
  7-day cycle patterns (day 0 = start):
    Day 0: 378.1 avg targets (274 days)
    Day 1: 375.7 avg targets (274 days)
    Day 2: 380.8 avg targets (274 days)
    Day 3: 383.3 avg targets (274 days)
    Day 4: 375.6 avg targets (274 days)
    Day 5: 376.2 avg targets (274 days)
    Day 6: 384.6 avg targets (273 days)
  30-day cycle patterns:
    Days 0-4: 370.9 avg targets
    Days 5-9: 380.2 avg targets
    Days 10-14: 375.8 avg targets
    Days 15-19: 377.0 avg targets
    Days 20-24: 374.3 avg targets
    Days 25-29: 376.5 avg targets

💡 CALENDAR ANALYSIS INSIGHTS:
  📊 Asset class coverage is generally high (>90%)
  🎯 Target availability varies significantly by day
  🔍 28 days have <100 targets (high variance days)
  🚨 Stress regime shows normal target patterns
  📅 Weekly/monthly patterns detected for CV design

✅ EDA ANALYSIS COMPLETED:
  1. ✅ Target-feature alignment verified (no leakage)
  2. ✅ Series health check completed (5 series to drop)
  3. ✅ FX triangle arbitrage features identified (76 triangles)
  4. ✅ Scale normalization pipeline implemented
  5. ✅ Signal scouting completed (strongest IC=0.0715)
  6. ✅ Calendar patterns analyzed for CV design

🚀 READY FOR MODELING PHASE!