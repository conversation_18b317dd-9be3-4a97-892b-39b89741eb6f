=== LAGGED TEST LABELS INTEGRITY & SCHEMA ANALYSIS ===
Data path: ../../input/mitsui-commodity-prediction-challenge/lagged_test_labels

--- ANALYZING LAG 1 FILE ---
1. BASIC STRUCTURE:
   Shape: (90, 108)
   Expected: (90, 108) - 90 rows, 106 targets + date_id + label_date_id
   Columns: 108
   Memory: 0.07 MB
   Required columns present: ✓
2. TARGET COLUMN VERIFICATION:
   Target columns found: 106
   Expected range: target_0 to target_105 (106 targets)
   First few targets: ['target_0', 'target_1', 'target_2']
   Last few targets: ['target_103', 'target_104', 'target_105']
   Target naming: ✓ Perfect match
3. DATA TYPES:
   date_id dtype: int64
   label_date_id dtype: int64
   Numeric target columns: 106/106
4. UNIQUENESS CHECK:
   Total rows: 90
   Unique (date_id, label_date_id) pairs: 90
   Duplicates: ✓ None
5. SAMPLE DATA:
   First 3 rows (key columns + first 3 targets):
 date_id  label_date_id  target_0  target_1  target_2
    1829           1827       NaN       NaN  0.017868
    1830           1828  0.002560 -0.004592 -0.001776
    1831           1829  0.005346 -0.014539  0.019542

--- ANALYZING LAG 2 FILE ---
1. BASIC STRUCTURE:
   Shape: (90, 108)
   Expected: (90, 108) - 90 rows, 106 targets + date_id + label_date_id
   Columns: 108
   Memory: 0.07 MB
   Required columns present: ✓
2. TARGET COLUMN VERIFICATION:
   Target columns found: 106
   Expected range: target_106 to target_211 (106 targets)
   First few targets: ['target_106', 'target_107', 'target_108']
   Last few targets: ['target_209', 'target_210', 'target_211']
   Target naming: ✓ Perfect match
3. DATA TYPES:
   date_id dtype: int64
   label_date_id dtype: int64
   Numeric target columns: 106/106
4. UNIQUENESS CHECK:
   Total rows: 90
   Unique (date_id, label_date_id) pairs: 90
   Duplicates: ✓ None
5. SAMPLE DATA:
   First 3 rows (key columns + first 3 targets):
 date_id  label_date_id  target_106  target_107  target_108
    1830           1827         NaN         NaN   -0.012551
    1831           1828    0.004287   -0.027723   -0.012212
    1832           1829    0.011360   -0.036678   -0.010981

--- ANALYZING LAG 3 FILE ---
1. BASIC STRUCTURE:
   Shape: (90, 108)
   Expected: (90, 108) - 90 rows, 106 targets + date_id + label_date_id
   Columns: 108
   Memory: 0.07 MB
   Required columns present: ✓
2. TARGET COLUMN VERIFICATION:
   Target columns found: 106
   Expected range: target_212 to target_317 (106 targets)
   First few targets: ['target_212', 'target_213', 'target_214']
   Last few targets: ['target_315', 'target_316', 'target_317']
   Target naming: ✓ Perfect match
3. DATA TYPES:
   date_id dtype: int64
   label_date_id dtype: int64
   Numeric target columns: 106/106
4. UNIQUENESS CHECK:
   Total rows: 90
   Unique (date_id, label_date_id) pairs: 90
   Duplicates: ✓ None
5. SAMPLE DATA:
   First 3 rows (key columns + first 3 targets):
 date_id  label_date_id  target_212  target_213  target_214
    1831           1827    0.000778   -0.019080   -0.023775
    1832           1828    0.005852   -0.022221   -0.019123
    1833           1829   -0.013472    0.004279   -0.017279

--- ANALYZING LAG 4 FILE ---
1. BASIC STRUCTURE:
   Shape: (90, 108)
   Expected: (90, 108) - 90 rows, 106 targets + date_id + label_date_id
   Columns: 108
   Memory: 0.07 MB
   Required columns present: ✓
2. TARGET COLUMN VERIFICATION:
   Target columns found: 106
   Expected range: target_318 to target_423 (106 targets)
   First few targets: ['target_318', 'target_319', 'target_320']
   Last few targets: ['target_421', 'target_422', 'target_423']
   Target naming: ✓ Perfect match
3. DATA TYPES:
   date_id dtype: int64
   label_date_id dtype: int64
   Numeric target columns: 106/106
4. UNIQUENESS CHECK:
   Total rows: 90
   Unique (date_id, label_date_id) pairs: 90
   Duplicates: ✓ None
5. SAMPLE DATA:
   First 3 rows (key columns + first 3 targets):
 date_id  label_date_id  target_318  target_319  target_320
    1832           1827    0.001082   -0.020023    0.064526
    1833           1828   -0.001296   -0.015281    0.041474
    1834           1829   -0.002375   -0.021250    0.041091

=== SUMMARY ===
All 4 lag files loaded successfully
Ready for arrival schedule and coverage analysis...

=== ARRIVAL SCHEDULE & OFFSET ANALYSIS ===

--- LAG 1 ARRIVAL SCHEDULE ---
1. OFFSET STATISTICS:
   Min offset: 2
   Max offset: 2
   Median offset: 2.0
   Mean offset: 2.00
   Std offset: 0.000
   Unique offsets: [np.int64(2)]
   Constant offset: ✓ Yes
   → Labels for lag 1 arrive exactly 2 days after label_date_id
2. DATE RANGE ANALYSIS:
   date_id range: 1829 → 1918
   label_date_id range: 1827 → 1916
   Coverage span: 90 days
3. SAMPLE ARRIVAL PATTERN:
 date_id  label_date_id  offset
    1829           1827       2
    1830           1828       2
    1831           1829       2
    1832           1830       2
    1833           1831       2
4. SEQUENCE COMPLETENESS:
   Expected date_ids: 90
   Actual date_ids: 90
   Missing date_ids: None

--- LAG 2 ARRIVAL SCHEDULE ---
1. OFFSET STATISTICS:
   Min offset: 3
   Max offset: 3
   Median offset: 3.0
   Mean offset: 3.00
   Std offset: 0.000
   Unique offsets: [np.int64(3)]
   Constant offset: ✓ Yes
   → Labels for lag 2 arrive exactly 3 days after label_date_id
2. DATE RANGE ANALYSIS:
   date_id range: 1830 → 1919
   label_date_id range: 1827 → 1916
   Coverage span: 90 days
3. SAMPLE ARRIVAL PATTERN:
 date_id  label_date_id  offset
    1830           1827       3
    1831           1828       3
    1832           1829       3
    1833           1830       3
    1834           1831       3
4. SEQUENCE COMPLETENESS:
   Expected date_ids: 90
   Actual date_ids: 90
   Missing date_ids: None

--- LAG 3 ARRIVAL SCHEDULE ---
1. OFFSET STATISTICS:
   Min offset: 4
   Max offset: 4
   Median offset: 4.0
   Mean offset: 4.00
   Std offset: 0.000
   Unique offsets: [np.int64(4)]
   Constant offset: ✓ Yes
   → Labels for lag 3 arrive exactly 4 days after label_date_id
2. DATE RANGE ANALYSIS:
   date_id range: 1831 → 1920
   label_date_id range: 1827 → 1916
   Coverage span: 90 days
3. SAMPLE ARRIVAL PATTERN:
 date_id  label_date_id  offset
    1831           1827       4
    1832           1828       4
    1833           1829       4
    1834           1830       4
    1835           1831       4
4. SEQUENCE COMPLETENESS:
   Expected date_ids: 90
   Actual date_ids: 90
   Missing date_ids: None

--- LAG 4 ARRIVAL SCHEDULE ---
1. OFFSET STATISTICS:
   Min offset: 5
   Max offset: 5
   Median offset: 5.0
   Mean offset: 5.00
   Std offset: 0.000
   Unique offsets: [np.int64(5)]
   Constant offset: ✓ Yes
   → Labels for lag 4 arrive exactly 5 days after label_date_id
2. DATE RANGE ANALYSIS:
   date_id range: 1832 → 1921
   label_date_id range: 1827 → 1916
   Coverage span: 90 days
3. SAMPLE ARRIVAL PATTERN:
 date_id  label_date_id  offset
    1832           1827       5
    1833           1828       5
    1834           1829       5
    1835           1830       5
    1836           1831       5
4. SEQUENCE COMPLETENESS:
   Expected date_ids: 90
   Actual date_ids: 90
   Missing date_ids: None

=== CROSS-LAG ARRIVAL COMPARISON ===
Lag | Constant Offset | Offset Value | Date Range | Label Range
----|-----------------|--------------|------------|------------
 1  |      Yes       |      2      | 1829-1918 | 1827-1916
 2  |      Yes       |      3      | 1830-1919 | 1827-1916
 3  |      Yes       |      4      | 1831-1920 | 1827-1916
 4  |      Yes       |      5      | 1832-1921 | 1827-1916

=== ONLINE LEARNING IMPLICATIONS ===
Based on arrival schedule analysis:
  Lag 1: Labels arrive 2 days after label_date_id
    → On day D, can update model with labels from day D-2
  Lag 2: Labels arrive 3 days after label_date_id
    → On day D, can update model with labels from day D-3
  Lag 3: Labels arrive 4 days after label_date_id
    → On day D, can update model with labels from day D-4
  Lag 4: Labels arrive 5 days after label_date_id
    → On day D, can update model with labels from day D-5

✅ Arrival schedule analysis complete
Next: Coverage and availability analysis...

=== COVERAGE & AVAILABILITY ANALYSIS ===

--- LAG 1 COVERAGE ANALYSIS ---
1. OVERALL COVERAGE:
   Total possible values: 9,540
   Available values: 8,503
   Missing values: 1,037
   Overall coverage: 89.1%
2. DAILY COVERAGE STATISTICS:
   Mean targets per day: 94.5
   Median targets per day: 106.0
   Min targets per day: 10
   Max targets per day: 106
   Std targets per day: 25.4
3. LOW COVERAGE DAYS (<50% coverage):
   Count: 8
   Days (date_id → label_date_id, targets available):
     1829.0 → 1827.0: 38.0/106 (35.8%)
     1848.0 → 1846.0: 38.0/106 (35.8%)
     1849.0 → 1847.0: 38.0/106 (35.8%)
     1892.0 → 1890.0: 10.0/106 (9.4%)
     1893.0 → 1891.0: 10.0/106 (9.4%)
     1894.0 → 1892.0: 37.0/106 (34.9%)
     1917.0 → 1915.0: 10.0/106 (9.4%)
     1918.0 → 1916.0: 10.0/106 (9.4%)
4. ZERO COVERAGE DAYS (no-update days):
   ✓ No zero coverage days

--- LAG 2 COVERAGE ANALYSIS ---
1. OVERALL COVERAGE:
   Total possible values: 9,540
   Available values: 8,458
   Missing values: 1,082
   Overall coverage: 88.7%
2. DAILY COVERAGE STATISTICS:
   Mean targets per day: 94.0
   Median targets per day: 106.0
   Min targets per day: 9
   Max targets per day: 106
   Std targets per day: 26.1
3. LOW COVERAGE DAYS (<50% coverage):
   Count: 9
   Days (date_id → label_date_id, targets available):
     1830.0 → 1827.0: 38.0/106 (35.8%)
     1848.0 → 1845.0: 38.0/106 (35.8%)
     1850.0 → 1847.0: 38.0/106 (35.8%)
     1892.0 → 1889.0: 9.0/106 (8.5%)
     1893.0 → 1890.0: 31.0/106 (29.2%)
     1894.0 → 1891.0: 9.0/106 (8.5%)
     1895.0 → 1892.0: 31.0/106 (29.2%)
     1917.0 → 1914.0: 9.0/106 (8.5%)
     1919.0 → 1916.0: 9.0/106 (8.5%)
4. ZERO COVERAGE DAYS (no-update days):
   ✓ No zero coverage days

--- LAG 3 COVERAGE ANALYSIS ---
1. OVERALL COVERAGE:
   Total possible values: 9,540
   Available values: 8,461
   Missing values: 1,079
   Overall coverage: 88.7%
2. DAILY COVERAGE STATISTICS:
   Mean targets per day: 94.0
   Median targets per day: 106.0
   Min targets per day: 15
   Max targets per day: 106
   Std targets per day: 25.3
3. LOW COVERAGE DAYS (<50% coverage):
   Count: 9
   Days (date_id → label_date_id, targets available):
     1831.0 → 1827.0: 39.0/106 (36.8%)
     1848.0 → 1844.0: 39.0/106 (36.8%)
     1851.0 → 1847.0: 39.0/106 (36.8%)
     1892.0 → 1888.0: 15.0/106 (14.2%)
     1893.0 → 1889.0: 34.0/106 (32.1%)
     1895.0 → 1891.0: 15.0/106 (14.2%)
     1896.0 → 1892.0: 34.0/106 (32.1%)
     1917.0 → 1913.0: 15.0/106 (14.2%)
     1920.0 → 1916.0: 15.0/106 (14.2%)
4. ZERO COVERAGE DAYS (no-update days):
   ✓ No zero coverage days

--- LAG 4 COVERAGE ANALYSIS ---
1. OVERALL COVERAGE:
   Total possible values: 9,540
   Available values: 8,526
   Missing values: 1,014
   Overall coverage: 89.4%
2. DAILY COVERAGE STATISTICS:
   Mean targets per day: 94.7
   Median targets per day: 106.0
   Min targets per day: 14
   Max targets per day: 106
   Std targets per day: 25.5
3. LOW COVERAGE DAYS (<50% coverage):
   Count: 9
   Days (date_id → label_date_id, targets available):
     1832.0 → 1827.0: 39.0/106 (36.8%)
     1848.0 → 1843.0: 24.0/106 (22.6%)
     1852.0 → 1847.0: 39.0/106 (36.8%)
     1892.0 → 1887.0: 14.0/106 (13.2%)
     1893.0 → 1888.0: 35.0/106 (33.0%)
     1896.0 → 1891.0: 14.0/106 (13.2%)
     1897.0 → 1892.0: 35.0/106 (33.0%)
     1917.0 → 1912.0: 14.0/106 (13.2%)
     1921.0 → 1916.0: 14.0/106 (13.2%)
4. ZERO COVERAGE DAYS (no-update days):
   ✓ No zero coverage days

=== CROSS-LAG AVAILABILITY MATRIX ===
Analysis of how many labels are available by lag for each public day

Sample availability matrix (first 10 days):
Day    | Lag1 | Lag2 | Lag3 | Lag4 | Total
-------|------|------|------|------|------
1827 |   38 |   38 |   39 |   39 |  154
1828 |  106 |  106 |  106 |  106 |  424
1829 |  106 |  106 |  106 |  106 |  424
1830 |  106 |  106 |  106 |  106 |  424
1831 |  106 |  106 |  106 |  106 |  424
1832 |  106 |  106 |  106 |  106 |  424
1833 |  106 |  106 |  106 |  106 |  424
1834 |  106 |  106 |  106 |  106 |  424
1835 |  106 |  106 |  106 |  106 |  424
1836 |  106 |  106 |  106 |  106 |  424

=== COVERAGE SUMMARY BY LAG ===
Lag | Overall Coverage | Mean Daily | Min Daily | Max Daily | Low Coverage Days
----|------------------|------------|-----------|-----------|------------------
 1  |       89.1%     |     94.5   |     10    |    106    |         8
 2  |       88.7%     |     94.0   |      9    |    106    |         9
 3  |       88.7%     |     94.0   |     15    |    106    |         9
 4  |       89.4%     |     94.7   |     14    |    106    |         9

✅ Coverage and availability analysis complete
Next: Consistency verification with train_labels...

=== CONSISTENCY VERIFICATION WITH TRAIN_LABELS ===
Train labels loaded: (1917, 425)

--- LAG 1 CONSISTENCY CHECK ---
1. CONSISTENCY STATISTICS:
   Total comparisons: 8,503
   Exact matches: 8,503
   Exact match rate: 100.000%
   Maximum difference: 0.0000000000
   Mean absolute difference: 0.0000000000
   Median absolute difference: 0.0000000000
   95th percentile difference: 0.0000000000
   ✓ No significant differences found
2. SAMPLE COMPARISON (first 3 rows, first 3 targets):
   date_id → label_date_id | Target | Lag Value | Train Value | Difference
   -----------------------|--------|-----------|-------------|------------
   1829.0 → 1827.0     | target_0 |       nan |        nan | Both NaN
   1829.0 → 1827.0     | target_1 |       nan |        nan | Both NaN
   1829.0 → 1827.0     | target_2 |  0.017868 |   0.017868 | 0.00e+00
   1830.0 → 1828.0     | target_0 |  0.002560 |   0.002560 | 0.00e+00
   1830.0 → 1828.0     | target_1 | -0.004592 |  -0.004592 | 0.00e+00
   1830.0 → 1828.0     | target_2 | -0.001776 |  -0.001776 | 0.00e+00
   1831.0 → 1829.0     | target_0 |  0.005346 |   0.005346 | 0.00e+00
   1831.0 → 1829.0     | target_1 | -0.014539 |  -0.014539 | 0.00e+00
   1831.0 → 1829.0     | target_2 |  0.019542 |   0.019542 | 0.00e+00

--- LAG 2 CONSISTENCY CHECK ---
1. CONSISTENCY STATISTICS:
   Total comparisons: 8,458
   Exact matches: 8,458
   Exact match rate: 100.000%
   Maximum difference: 0.0000000000
   Mean absolute difference: 0.0000000000
   Median absolute difference: 0.0000000000
   95th percentile difference: 0.0000000000
   ✓ No significant differences found
2. SAMPLE COMPARISON (first 3 rows, first 3 targets):
   date_id → label_date_id | Target | Lag Value | Train Value | Difference
   -----------------------|--------|-----------|-------------|------------
   1830.0 → 1827.0     | target_106 |       nan |        nan | Both NaN
   1830.0 → 1827.0     | target_107 |       nan |        nan | Both NaN
   1830.0 → 1827.0     | target_108 | -0.012551 |  -0.012551 | 0.00e+00
   1831.0 → 1828.0     | target_106 |  0.004287 |   0.004287 | 0.00e+00
   1831.0 → 1828.0     | target_107 | -0.027723 |  -0.027723 | 0.00e+00
   1831.0 → 1828.0     | target_108 | -0.012212 |  -0.012212 | 0.00e+00
   1832.0 → 1829.0     | target_106 |  0.011360 |   0.011360 | 0.00e+00
   1832.0 → 1829.0     | target_107 | -0.036678 |  -0.036678 | 0.00e+00
   1832.0 → 1829.0     | target_108 | -0.010981 |  -0.010981 | 0.00e+00

--- LAG 3 CONSISTENCY CHECK ---
1. CONSISTENCY STATISTICS:
   Total comparisons: 8,461
   Exact matches: 8,461
   Exact match rate: 100.000%
   Maximum difference: 0.0000000000
   Mean absolute difference: 0.0000000000
   Median absolute difference: 0.0000000000
   95th percentile difference: 0.0000000000
   ✓ No significant differences found
2. SAMPLE COMPARISON (first 3 rows, first 3 targets):
   date_id → label_date_id | Target | Lag Value | Train Value | Difference
   -----------------------|--------|-----------|-------------|------------
   1831.0 → 1827.0     | target_212 |  0.000778 |   0.000778 | 0.00e+00
   1831.0 → 1827.0     | target_213 | -0.019080 |  -0.019080 | 0.00e+00
   1831.0 → 1827.0     | target_214 | -0.023775 |  -0.023775 | 0.00e+00
   1832.0 → 1828.0     | target_212 |  0.005852 |   0.005852 | 0.00e+00
   1832.0 → 1828.0     | target_213 | -0.022221 |  -0.022221 | 0.00e+00
   1832.0 → 1828.0     | target_214 | -0.019123 |  -0.019123 | 0.00e+00
   1833.0 → 1829.0     | target_212 | -0.013472 |  -0.013472 | 0.00e+00
   1833.0 → 1829.0     | target_213 |  0.004279 |   0.004279 | 0.00e+00
   1833.0 → 1829.0     | target_214 | -0.017279 |  -0.017279 | 0.00e+00

--- LAG 4 CONSISTENCY CHECK ---
1. CONSISTENCY STATISTICS:
   Total comparisons: 8,526
   Exact matches: 8,526
   Exact match rate: 100.000%
   Maximum difference: 0.0000000000
   Mean absolute difference: 0.0000000000
   Median absolute difference: 0.0000000000
   95th percentile difference: 0.0000000000
   ✓ No significant differences found
2. SAMPLE COMPARISON (first 3 rows, first 3 targets):
   date_id → label_date_id | Target | Lag Value | Train Value | Difference
   -----------------------|--------|-----------|-------------|------------
   1832.0 → 1827.0     | target_318 |  0.001082 |   0.001082 | 0.00e+00
   1832.0 → 1827.0     | target_319 | -0.020023 |  -0.020023 | 0.00e+00
   1832.0 → 1827.0     | target_320 |  0.064526 |   0.064526 | 0.00e+00
   1833.0 → 1828.0     | target_318 | -0.001296 |  -0.001296 | 0.00e+00
   1833.0 → 1828.0     | target_319 | -0.015281 |  -0.015281 | 0.00e+00
   1833.0 → 1828.0     | target_320 |  0.041474 |   0.041474 | 0.00e+00
   1834.0 → 1829.0     | target_318 | -0.002375 |  -0.002375 | 0.00e+00
   1834.0 → 1829.0     | target_319 | -0.021250 |  -0.021250 | 0.00e+00
   1834.0 → 1829.0     | target_320 |  0.041091 |   0.041091 | 0.00e+00

=== OVERALL CONSISTENCY SUMMARY ===
Lag | Total Comparisons | Exact Matches | Match Rate | Max Difference | Significant Diffs
----|-------------------|---------------|------------|----------------|------------------
 1  |         8,503    |       8,503  |   100.00%  |     0.00e+00   |         0
 2  |         8,458    |       8,458  |   100.00%  |     0.00e+00   |         0
 3  |         8,461    |       8,461  |   100.00%  |     0.00e+00   |         0
 4  |         8,526    |       8,526  |   100.00%  |     0.00e+00   |         0

=== DATA INTEGRITY ASSESSMENT ===
✅ EXCELLENT: All lags show >99.9% exact match rate with train_labels
✅ Data integrity is confirmed - lagged labels are consistent with ground truth

✅ Consistency verification complete
Next: Missingness patterns and distribution analysis...

=== ASSET MAPPING FEASIBILITY & COMPREHENSIVE SUMMARY ===
Target pairs loaded: (424, 3)
Columns: ['target', 'lag', 'pair']
First few rows:
     target  lag                                  pair
0  target_0    1                 US_Stock_VT_adj_close
1  target_1    1  LME_PB_Close - US_Stock_VT_adj_close
2  target_2    1           LME_CA_Close - LME_ZS_Close
Potential asset columns: []

1. ASSET MAPPING MATRIX ANALYSIS:
   ⚠️ Could not identify asset columns - using target count approximation

--- LAG 1 MAPPING FEASIBILITY ---
   Target pairs for lag 1: 106
   Solvable days: 72/90 (80.0%)
   Low-rank days: 18
   Sample low-rank days (date_id → label_date_id, available targets):
     1829.0 → 1827.0: 38/106 targets
     1844.0 → 1842.0: 68/106 targets
     1845.0 → 1843.0: 68/106 targets
     1848.0 → 1846.0: 38/106 targets
     1849.0 → 1847.0: 38/106 targets

--- LAG 2 MAPPING FEASIBILITY ---
   Target pairs for lag 2: 106
   Solvable days: 71/90 (78.9%)
   Low-rank days: 19
   Sample low-rank days (date_id → label_date_id, available targets):
     1830.0 → 1827.0: 38/106 targets
     1844.0 → 1841.0: 72/106 targets
     1846.0 → 1843.0: 72/106 targets
     1848.0 → 1845.0: 38/106 targets
     1850.0 → 1847.0: 38/106 targets

--- LAG 3 MAPPING FEASIBILITY ---
   Target pairs for lag 3: 106
   Solvable days: 71/90 (78.9%)
   Low-rank days: 19
   Sample low-rank days (date_id → label_date_id, available targets):
     1831.0 → 1827.0: 39/106 targets
     1844.0 → 1840.0: 69/106 targets
     1847.0 → 1843.0: 69/106 targets
     1848.0 → 1844.0: 39/106 targets
     1851.0 → 1847.0: 39/106 targets

--- LAG 4 MAPPING FEASIBILITY ---
   Target pairs for lag 4: 106
   Solvable days: 73/90 (81.1%)
   Low-rank days: 17
   Sample low-rank days (date_id → label_date_id, available targets):
     1832.0 → 1827.0: 39/106 targets
     1844.0 → 1839.0: 70/106 targets
     1848.0 → 1843.0: 24/106 targets
     1852.0 → 1847.0: 39/106 targets
     1853.0 → 1848.0: 70/106 targets

=== COMPREHENSIVE ONLINE LEARNING SUMMARY ===

2. ARRIVAL SCHEDULE (CONFIRMED CONSTANTS):
   Lag 1: Labels arrive 2 days after label_date_id
     → On day D, update with labels from day D-2
   Lag 2: Labels arrive 3 days after label_date_id
     → On day D, update with labels from day D-3
   Lag 3: Labels arrive 4 days after label_date_id
     → On day D, update with labels from day D-4
   Lag 4: Labels arrive 5 days after label_date_id
     → On day D, update with labels from day D-5

3. COVERAGE PATTERNS:
   Overall coverage: ~89% across all lags
   Daily target availability: 94-95 targets per day on average
   Low coverage days per lag: 8-9 days (mostly around dates 1827, 1847, 1891-1892, 1916)
   No zero-coverage days: All days provide some update signal

4. DATA INTEGRITY:
   ✅ Perfect consistency: 100% exact match with train_labels across all lags
   ✅ No data corruption or drift detected
   ✅ Safe for online learning updates

5. ASSET MAPPING FEASIBILITY:
   Lag 1: 80.0% of days are solvable
     → 18 days may need regularized least squares
   Lag 2: 78.9% of days are solvable
     → 19 days may need regularized least squares
   Lag 3: 78.9% of days are solvable
     → 19 days may need regularized least squares
   Lag 4: 81.1% of days are solvable
     → 17 days may need regularized least squares

✅ LAGGED TEST LABELS ANALYSIS COMPLETE
📋 Ready for online learning implementation with predictable, high-quality data