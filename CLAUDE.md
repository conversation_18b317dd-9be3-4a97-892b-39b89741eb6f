# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains resources and solutions for the **MITSUI&CO. Commodity Prediction Challenge** - a machine learning competition focused on predicting commodity returns using historical data from multiple global financial markets (LME, JPX, US Stock, and Forex markets).

### Competition Details
- **Objective**: Predict future commodity returns using price-difference series derived from asset pairs
- **Evaluation Metric**: Rank correlation Sharpe ratio (mean Spearman rank correlation / standard deviation)
- **Target**: 424 target columns representing log returns or differences between financial instrument pairs
- **Data Sources**: London Metal Exchange (LME), Japan Exchange Group (JPX), US Stock markets, and Forex (FX)
- **Timeline**: Training phase through Oct 2025, forecasting phase through Jan 2026

## Repository Structure

### Core Directories
- `src/`: Main source code directory
  - `utils/`: Utility functions for metrics and submission handling
  - `algo/`: Algorithm implementations (currently empty)
  - `eda/`: Exploratory data analysis (currently empty)
- `info/`: Competition documentation
- `plan/`: Strategic planning and analysis documents
- `past/`: Historical competition solutions and analysis from Jane Street, Optiver, and JPX competitions

### Key Files

#### src/utils/ - Critical Competition Infrastructure
- **`target_calculation.py`**: **CRITICAL** - Shows how targets are calculated from features
  - Formula: `target = log(A[t+lag+1]/A[t+1]) - log(B[t+lag+1]/B[t+1])`
  - Targets are **relative log-returns between asset pairs** over lag periods
  - **Look-ahead structure**: Uses prices from t+1 to t+lag+1 (intentional future prediction)
  - **Example**: LME Copper vs Cameco stock with 4-day lag
  - **Key for preventing data leakage**: Features at time t must NOT include info from t+1 onwards

- **`metric_example.py`**: Implementation of the rank correlation Sharpe ratio evaluation metric
  - Daily Spearman rank correlation between predictions and targets
  - Sharpe ratio = mean(daily_correlations) / std(daily_correlations)
  - Handles missing targets (only uses available targets per day)
  - **Critical**: Uses `rank(method='average')` for tie handling

- **`submission_example.py`**: Template for Kaggle evaluation API submission
  - Real-time inference server setup for competition evaluation
  - Receives batched test data with different lag files (1-4 days)
  - Must return predictions within 1 minute per batch
  - Returns 424 target predictions per timestep

#### Other Key Files
- `info/competition.md`: Complete competition description and requirements
- `info/data.md`: Dataset structure and file descriptions
- `info/insights.md`: Strategic analysis and ML advisory content for commodity prediction

## Development Approach

### Data Science Workflow
This project follows quantitative finance and data science best practices:
- Work on one file at a time for optimization rather than creating multiple versions
- Always examine outputs of implemented code before writing next iteration
- Never write conclusions before seeing actual results
- Follow "bacterial coding" philosophy: small, modular, self-contained functions that can be easily copied/reused

### Key Technical Constraints
- **Inference Requirements**: Must return predictions within 1 minute per batch (except first batch)
- **Model Loading**: Must complete within 15 minutes of notebook start
- **Performance**: Polars DataFrame recommended over Pandas for performance
- **Target Format**: 424 target columns (target_0 to target_423)
- **Data Handling**: Handle null values using SOLUTION_NULL_FILLER (-999999) for trading halts/holidays

### Evaluation Metric Implementation
The competition uses a custom Sharpe ratio variant:
- Calculates Spearman rank correlation between predictions and targets per day
- Returns mean correlation divided by standard deviation
- Handles multi-target scenarios with null value management
- Implementation available in `src/utils/metric_example.py:8`

### Submission Format
Use the Kaggle evaluation API pattern from `src/utils/submission_example.py`:
- Implement `predict()` function taking test data and 4 lagged label batches
- Return single-row DataFrame with 424 target predictions
- Handle both Pandas and Polars DataFrame returns
- Integration with `MitsuiInferenceServer` for evaluation

## ML Strategy Insights

### Domain Knowledge
- Commodity markets are event-driven and non-stationary (supply shocks, geopolitical events)
- Cross-market dependencies exist between metals (LME), currencies (FX), and equities (US/JPX)
- Price-difference series capture arbitrage and hedging signals
- 1-4 day lags enable "nowcasting" similar to FX trading patterns

### Historical Competition Learnings
Based on analysis in `plan/overall.md`:
- Focus on ranking performance rather than absolute accuracy
- Online learning crucial for handling market regime shifts
- Multi-target learning with shared representations for efficiency
- Feature engineering emphasizing technical indicators and cross-asset relationships
- Ensemble approaches combining transformers, GRUs, and gradient boosting

## File Index
*Last updated: July 25, 2025*

### Core Application Files

**Source Code (`src/`)**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/src/utils/metric_example.py`: Implementation of rank correlation Sharpe ratio evaluation metric with `rank_correlation_sharpe_ratio()` function (line 8) and `score()` function (line 38) that handles multi-target predictions, null values via SOLUTION_NULL_FILLER (-999999), and Spearman rank correlation computation for daily scoring
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/src/utils/submission_example.py`: Kaggle evaluation API template with `predict()` function structure for inference server setup, handles 424 target columns, processes test data and 4 lagged label batches, includes MitsuiInferenceServer integration with 1-minute response constraint and 15-minute model loading limit
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/src/algo/`: Empty directory for algorithm implementations
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/src/eda/`: Empty directory for exploratory data analysis

### Competition Documentation (`info/`)

**Core Competition Files**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/info/competition.md`: Complete competition description for MITSUI&CO. Commodity Prediction Challenge including objective (predict commodity returns using LME, JPX, US Stock, FX data), evaluation metric (rank correlation Sharpe ratio), timeline (training through Oct 2025, forecasting through Jan 2026), and prize structure ($20k-$5k from 1st-15th place)
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/info/data.md`: Dataset structure documentation covering train.csv (historic finance data), test.csv (mock test set), train_labels.csv (424 targets), lagged_test_labels (4 lag files), target_pairs.csv (input details for target calculations), and file schemas with date_id, time_series_identifier (LME/JPX/US/FX prefixes)
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/info/insights.md`: Strategic ML advisory content and domain knowledge for commodity prediction including analysis of event-driven non-stationary markets, cross-market dependencies, price-difference series for arbitrage/hedging signals, emphasis on ranking vs absolute accuracy, online learning for regime shifts, and multi-target learning strategies
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/info/paths.md`: Platform-specific file path reference for MITSUI commodity prediction challenge data, providing absolute paths for both Kaggle (/kaggle/input/mitsui-commodity-prediction-challenge/) and Google Colab (/content/drive/MyDrive/commodity-prices/input/) environments, including paths to all dataset files (train.csv, test.csv, target files, lagged labels) and kaggle_evaluation module components

### Historical Competition Solutions (`past/`)

**Jane Street Market Prediction (2020)**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Market Prediction/summary.md`: Overview of 2020 Jane Street competition solutions including 10th place GBM+MDN approach, 1st place supervised autoencoder+MLP, and TabNet adaptation for multi-label prediction tasks
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Market Prediction/Yirun's Solution (1st place).md`: Detailed 1st place solution using supervised autoencoder with MLP, 5-fold purged time-series CV, multi-label classification approach, and techniques to prevent data leakage in autoencoder training
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Market Prediction/10th Place Solution.md`: GBM-based approach with Mixture Density Networks for probabilistic regression
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Market Prediction/3rd place solution.md`: Third place methodology and implementation details
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Market Prediction/tabnet.md`: TabNet implementation for attention-based tabular neural networks
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Market Prediction/Jane Street Market Prediction.md`: Competition overview and problem description

**Jane Street Real-Time Market Data Forecasting (2024-2025)**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Real-Time Market Data Forecasting/summary.md`: Summary of 2024-2025 Jane Street real-time forecasting competition featuring ensemble methods (NN+XGBoost+TabM+Ridge) and GRU-based online learning approaches for forecasting responder_6 metric
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Real-Time Market Data Forecasting/[Private LB 8th] solution.md`: 8th place private leaderboard solution using time-series GRU with online learning, daily incremental updates, auxiliary targets, and real-time adaptation techniques
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Real-Time Market Data Forecasting/Ensemble (XGB+NN+TabM+Ridge).md`: Multi-model ensemble approach combining XGBoost, neural networks, TabNet, and Ridge regression
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Real-Time Market Data Forecasting/[Public LB 12th] Competition Wrap-up.md`: 12th place public leaderboard solution analysis and post-competition insights
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Jane Street Real-Time Market Data Forecasting/Jane Street Real-Time Market Data Forecasting.md`: Competition overview for real-time market forecasting challenge

**Optiver Trading at the Close (2023)**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Optiver - Trading at the Close/summary.md`: Summary of Optiver closing auction prediction competition featuring 6th place seq2seq transformers+GRU with online learning and 9th place XGBoost with extensive feature engineering and post-processing
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Optiver - Trading at the Close/1stplace.md`: First place solution methodology (appears to be empty or corrupted)
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Optiver - Trading at the Close/6thplace.md`: 6th place solution using sequence-to-sequence transformers and GRU with online learning
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Optiver - Trading at the Close/9thplace.md`: 9th place XGBoost solution with feature engineering and post-processing
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/Optiver - Trading at the Close/Optiver - Trading at the Close.md`: Competition overview for Nasdaq closing auction prediction

**JPX Tokyo Stock Exchange Prediction (2022)**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/jpx/summary.md`: Summary of JPX stock ranking competition featuring 4th place LightGBM ranker with grouped CV and 8th place sector-specific modeling approach for Japanese stock return prediction
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/jpx/4th.md`: 4th place solution details (appears to be empty or corrupted)
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/jpx/8th.md`: 8th place solution using sector-specific LightGBM models
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/past/jpx/JPX Tokyo Stock Exchange Prediction.md`: Competition overview for Japanese stock return ranking

### Strategic Planning (`plan/`)

**Analysis and Strategy Documents**
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/plan/overall.md`: Comprehensive strategic analysis of the MITSUI commodity prediction challenge including deep problem breakdown, connections to past competitions (Jane Street, Optiver, JPX), multi-target time-series forecasting approach, feature engineering strategies, modeling recommendations (transformers/GRUs with online learning), and iterative development plan emphasizing data exploration before model implementation
- `/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/commodity-prices/plan/eda_plan.md`: Structured exploratory data analysis roadmap covering 7 phases: dataset integrity, temporal patterns, asset grouping, feature exploration, target/pair analysis, lagged labels dynamics, and metric diagnostics, with emphasis on understanding commodity market non-stationarity, cross-asset dependencies, and preparation for multi-task learning approaches

## Testing and Validation

### No Standard Testing Framework
This repository does not contain standard testing infrastructure (no pytest, unittest, or package.json files found). Testing should focus on:
- Metric validation using provided evaluation functions
- Time-series cross-validation with proper temporal splits
- Simulation of online learning scenarios with lagged labels

### Validation Strategy
- Use time-based splits (last 90 days as validation per competition phases)
- Avoid data leakage with strict temporal ordering
- Test online adaptation capabilities for forecasting phase
