Absolutely. Below is a **comprehensive, single-file pipeline** that covers **training, validation, artifact prep, and the submission-time `predict()` loop with online updates**. It is consistent with everything we established (random seeds present; MLP explicitly optional), and it is written so you can lift it directly into a single notebook/script.

I’ll include (1) a **clear data contract**, (2) **end-to-end control flow**, (3) **function-level responsibilities with invariants**, (4) **math where needed (no \$\$)**, (5) **time-budget guards**, and (6) **acceptance tests**.

---

## 0) Single source-of-truth configuration (top-of-file)

* Reproducibility and toggles

  * GLOBAL\_SEED = 2025
  * N\_SEEDS = 2  (only used if MLP\_TOGGLE = True)
  * MLP\_TOGGLE = False  (v1 ships Ridge only; keep code path but disabled)

* Data ranges and lags

  * PUBLIC\_HOLDOUT = \[1827, 1916] inclusive (never used for tuning)
  * MAX\_LAG = 4
  * LAG\_OFFSETS = {1: 2, 2: 3, 3: 4, 4: 5}  (from your arrival audit)

* CV & purging

  * PURGE\_GAP = 10 days
  * FOLDS = 4 blocked folds over date\_id ∈ \[0, 1826]

* Features & preprocessing

  * DROP\_FEATURES = the 5 `US_Stock_GOLD_*` columns (all-NaN in test)
  * FFILL\_CAP = 10 days (within-series forward fill cap)
  * WINSOR\_K\_MAD = 5 (clip at ±5× MAD)
  * Group masks: LME, JPX, FX, US\_Stock (used for per-day, within-group normalization)

* Models (Stage-A asset forecasters; per lag)

  * Ridge L2 grid: RIDGE\_ALPHAS = \[3e−3, 1e−2, 3e−2]; default 1e−2
  * If MLP\_TOGGLE=True later:

    * MLP\_ARCH = \[256, 128], dropout 0.10, AdamW (lr 1e−3, wd 1e−4), epochs 40

* Post-processing (rank + shrinkage)

  * Shrinkage form: λ = clip(a + b·D\_feat, 0, 1)
  * SHRINK\_A\_INIT = 0.15, SHRINK\_B\_INIT = 0.25  (fit a,b on CV only)

* Online learning (submission phase)

  * ROLL\_UPDATE\_WINDOW = 90 days
  * N\_MIN\_UPDATE = 20 targets (skip target-space update if fewer observed)
  * RECENCY\_HALF\_LIFE = 30 days (weights for recent labels)
  * LAG\_WEIGHTS = {1: 1.0, 2: 0.9, 3: 0.8, 4: 0.7}
  * TIME\_BUDGET\_PER\_DAY = 60 seconds (first call is un-timed warm-init)

---

## 1) Data contracts (files and columns)

* `train.csv` (1917 × 558): `date_id` + 557 feature columns
* `train_labels.csv` (1917 × 425): `date_id` + 424 target columns `target_0 … target_423`
* `target_pairs.csv` (424 × 3): columns = \[`target`, `lag` ∈ {1..4}, `pair` string like “A − B” or “A”]
* `test.csv` (90 × 559): `date_id` ∈ \[1827..1916], identical features + `is_scored`
* `test_labels_lag_k.csv` (each 90 × 108): columns = `date_id`, `target_block` (106 columns), `label_date_id`

  * Offsets you measured are **constant**: date\_id − label\_date\_id = {2, 3, 4, 5} for lags 1..4.

**Invariant:** we never use any info from PUBLIC\_HOLDOUT for tuning; in submission, we only ingest lagged labels whose `date_id` equals “today” (the API rule).

---

## 2) Top-level control flow (one file)

1. **Config & Seeding** — set all RNGs using GLOBAL\_SEED.
2. **Load I/O** — read train/pairs; optionally test (only for local public-mirror sanity).
3. **Build Mappings** — parse `pair` strings → 106-asset universe; build signed matrices M\_lag (106×106) and stable solvers.
4. **Preprocess Features** — levels → returns; robust z-scores; per-day within-group norm; ffill with caps; missing flags; winsorize. Compute feature-side dispersion proxy D\_feat(t).
5. **Prepare Asset-Space Labels** — for t ∈ \[0..1826], per lag: solve regularized least squares to get r\_lag(t); standardize per asset.
6. **CV & Metric** — 4 blocked folds over \[0..1826], purge 10 days; metric = Sharpe of daily Spearman (after mapping to 424, rank, shrinkage).
7. **Train Stage-A Models** — per lag: Ridge grid (and optional MLP). Produce OOF predictions → map to targets → per-day rank → score.
8. **Fit Shrinkage Policy** — on CV only, fit (a, b) in λ = clip(a + b·D\_feat, 0, 1).
9. **Final Fit** — refit chosen Ridge per lag on full \[0..1826]; save artifacts in memory and /kaggle/working.
10. **(Optional) Public-Mirror Check** — evaluate once on \[1827..1916]; do not tune.
11. **Submission `predict()`** — warm-init, then per day: preprocess → Stage-A infer → map → rank+shrink → emit → online update via target-space gradient steps within time budget.
12. **Main Switch** — MODE="TRAIN" runs 1–10; MODE="SUBMIT" only exposes `predict()` with warm-init state.

---

## 3) Function-level responsibilities, invariants, and pseudo-interfaces

### 3.1 Config & seeding

* `set_global_seed(seed: int) -> None`
  Sets numpy/python/ML RNGs to ensure reproducibility.
  **Invariant:** must be called before any model init or CV split.

### 3.2 I/O

* `load_train() -> pd.DataFrame`
* `load_labels() -> pd.DataFrame`
* `load_pairs() -> pd.DataFrame`
* `load_test() -> Optional[pd.DataFrame]` (guarded for local sims)
* `save_artifact(name: str, obj: Any)` / `load_artifact(name: str) -> Any`
  **Invariant:** in submission, artifacts should load from the working dir without writing.

### 3.3 Target mapping and solvers

* `parse_asset_universe(pairs_df) -> List[str]`
  Extracts canonical asset names (≈106). Sorted list defines column order for r\_lag.
* `build_M_by_lag(pairs_df, assets) -> Dict[int, np.ndarray]`
  For each lag, build a 106×106 matrix M\_lag with entries in {−1, 0, +1}; rows align to the 106 targets of that lag; columns to assets.
* `build_solver(M: np.ndarray) -> Callable`
  Returns a function that solves min\_r ‖W(Mr − y)‖² + α‖r‖², with W a diagonal mask for present targets and α a small Tikhonov (e.g., 1e−3..1e−2).
  **Invariant:** never assume full rank; always allow regularization.

### 3.4 Preprocessing

* `infer_feature_groups(columns) -> Dict[str, List[int]]`
  Build boolean masks for LME/JPX/FX/US\_Stock by name prefix rules.
* `to_returns(df: pd.DataFrame) -> pd.DataFrame`
  Transform price-like to log-returns, volume/open-interest to log-diffs; leave pure indicators as is.
* `fit_robust_scalers(df_ret[0..1826]) -> scalers`
  Median/IQR per series; store to artifacts.
* `transform_robust(df_ret, scalers) -> Z`
* `group_normalize_per_day(Z, masks) -> Z_cs`
  For each day and group, z-score the group cross-section (center + scale).
* `ffill_capped(Z_cs, cap=10) -> Z_ff` + `add_missing_flags(Z_cs)`
* `winsorize(Z_ff, k=MAD*5) -> Z_clean`
* `compute_D_feat(Z_clean, masks, t) -> float`
  Feature-side dispersion proxy for day t, e.g., median absolute move across FX/LME/JPX groups.
  **Invariant:** all transforms are time-legal (no future leakage).

### 3.5 Asset-space labels

* `split_targets_by_lag(labels_df) -> Dict[int, List[str]]`
  Map each lag to its 106 target columns.
* `recover_asset_labels_per_lag(labels_df, M_lag, solver_lag, t in [0..1826]) -> r_lag(t)`
  Solve regularized least squares with masks for missing targets.
* `standardize_assets(r_lag[0..1826]) -> (r_std, means, stds)`
  Store means/stds for later de-standardization if needed.
  **Invariant:** all label ops for training are within \[0..1826] only.

### 3.6 CV splitter & metric

* `build_blocked_folds(start=0, end=1826, n_folds=4, purge=10) -> List[(train_idx, valid_idx)]`
* `daily_spearman(y_pred, y_true, mask) -> float`
  Per day t: compute Spearman rank correlation across **available** targets (mask).
* `fold_score(daily_rhos: List[float]) -> float`
  Sharpe of daily Spearman: mean(daily\_rho) / std(daily\_rho).
  **Invariant:** unweighted by n\_targets (matches competition).

### 3.7 Models (Stage-A per lag)

* `train_ridge_per_lag(X, r_lag, folds, alphas) -> (best_alpha, models_by_fold)`
  Multi-output Ridge; choose alpha by fold Sharpe (after Stage-B mapping + rank + shrinkage).
* `optional_train_mlp_per_lag(...) -> models` (if MLP\_TOGGLE=True)
  Keep identical data contracts; average predictions later.

**Evaluation inside folds (to mirror the metric):**
For each fold and lag ℓ:

1. Fit model on train; predict r̂\_lag on valid.
2. Map to targets per day: ŷ\_lag = M\_lag · r̂\_lag.
3. Concatenate 4 lags into 424-vector.
4. Per day, **rank** predictions across available targets.
5. Apply **shrinkage** using a provisional λ (SHRINK\_A\_INIT, SHRINK\_B\_INIT) with D\_feat(t).
6. Compute daily Spearman vs true labels; record Sharpe.
   After folds, **fit (a,b) properly** (see next section), then recompute fold scores with learned λ to finalize selection.

### 3.8 Shrinkage policy fitting

* `fit_shrinkage_lambda(CV_records) -> (a, b)`
  Inputs: for each valid day, tuple (D\_feat(t), daily Spearman if using λ, etc.).
  Optimize (a, b) in λ = clip(a + b·D\_feat, 0, 1) to maximize overall Sharpe across folds.
  Fix (a,b) and **re-score** folds (this freezes policy before holdout).

**Interpretation:** high feature-dispersion days often reward lower shrinkage (lean into the model); thin-dispersion days benefit from higher shrinkage toward prior-day ranks.

### 3.9 Final fit (0–1826) and artifacts

* Refit Ridge per lag on full \[0..1826] with chosen alphas.
* Persist artifacts in memory and to the working directory:

  * feature\_list, scalers, group\_masks
  * M\_lag matrices, solver handles (or params to rebuild fast)
  * ridge\_lag models (and MLP if later enabled)
  * shrinkage\_policy = {a, b}
  * meta.json: seeds, hyper-params, fold windows, etc.

### 3.10 Optional public-mirror check (no tuning)

* Evaluate once on \[1827..1916] for sanity (compute our metric).
* Do not persist anything learned here.

---

## 4) Submission `predict()` (single-file, API-faithful)

The function is called **once per day** with that day’s row of `test.csv` (features) while the grader holds state across calls.

### 4.1 Warm-init (first call; not timed)

* Load artifacts (or reuse in-memory from same notebook session).
* Set seeds (GLOBAL\_SEED).
* Initialize:

  * Per-lag model objects (Ridge; MLP later if toggled).
  * Per-lag **label buffers** as ring buffers with capacity ROLL\_UPDATE\_WINDOW.
  * Running feature stats for the **EMA scalers** used in feature-side proxies.
  * A cache for **prior-day predicted ranks** (for shrinkage blend).

**Invariant:** No heavy computation here beyond loading/allocating; first call is un-timed, so build everything you need.

### 4.2 Per-day call (for day P)

**Inputs:** `test_row` (all features for date\_id = P).
**Outputs:** 424-vector predictions for that day.

1. **Preprocess**

   * Transform test\_row to log-returns/log-diffs (as applicable) → robust scale (using stored scalers) → per-day within-group norm → missing flags.
   * Compute the **feature-side dispersion proxy** D\_feat(P) (label-free).

2. **Infer Stage-A per lag**

   * For ℓ in {1..4}, predict r̂\_ℓ(P) with Ridge (ensemble across folds if you saved more than one per lag).
   * If MLP\_TOGGLE later True, combine with MLP per predefined weights (e.g., 0.6 Ridge, 0.4 MLP).

3. **Map to 424 targets and rank**

   * ŷ\_ℓ(P) = M\_ℓ · r̂\_ℓ(P); concatenate lags → 424 predictions.
   * **Per-day rank transform** across the 424 predictions; ties are stable.

4. **Adaptive shrinkage (label-free)**

   * λ(P) = clip(a + b·D\_feat(P), 0, 1) using frozen (a,b).
   * Prior ranks = yesterday’s **predicted ranks** cache; use label ranks only if they have already arrived legally via lag files (not required).
   * Final ranks = (1 − λ)·rank\_model + λ·rank\_prior.
   * **Emit** final ranks for day P.

5. **Online update (within TIME\_BUDGET\_PER\_DAY)**

   * Read new rows from each `test_labels_lag_k.csv` where `date_id == P` (constant offsets you measured guarantee legality).
   * For lag ℓ:

     * Let observed subset be indices S with size n\_obs.
     * If n\_obs < N\_MIN\_UPDATE, skip.
     * **Target-space update (preferred):**

       * Let M\_sub be M\_ℓ restricted to rows S.
       * Residual: e = M\_sub r̂\_ℓ(X(label\_date\_id)) − y\_obs\_S.
       * Gradient wrt asset predictions: g = M\_subᵀ e.
       * Do 1–2 lightweight optimizer steps on the lag-ℓ **linear head** using (X(label\_date\_id), g), with weights:
         w\_time(d) = exp(−d / τ), τ from RECENCY\_HALF\_LIFE; w\_cov = min(1, n\_obs/106).
       * Winsorize y\_obs at ±5× MAD before computing residuals.
     * **Optional asset-space recovery** (only if M\_sub well-conditioned): solve r̂\_recovered via Tikhonov and run one linear update step toward r̂\_recovered.
   * Update EMA scalers from features only.
   * If wall-clock approaches 60 s, skip remaining updates (MLP updates are always optional; do those only if time allows).

6. **Logging (stdout)**

   * Print compact line: date\_id=P, λ, D\_feat, updated lags, n\_obs per lag, elapsed\_time.

**Safety guards:**

* Never ingest labels with `date_id > P`.
* If any solver is ill-conditioned or labels are too sparse, skip that lag/day update.
* If preprocessing fails or returns NaNs, fall back to **previous day ranks** (or uniform ranks on day 1827).

---

## 5) Mathematical details (compact)

* **Per-day Spearman**: For day t with available targets set S, Spearman ρ is the Pearson correlation between the ranks of prediction and ranks of truth over S.

* **Sharpe of daily Spearman**: Let ρ\_t be daily Spearman across the competition horizon of a fold. Score = mean(ρ\_t) / std(ρ\_t).

* **Target mapping**: For a given lag ℓ, the 106-vector of target values y\_ℓ(t) is given by y\_ℓ(t) = M\_ℓ r\_ℓ(t), where r\_ℓ(t) is the 106-vector of asset returns for that horizon. We predict r̂\_ℓ and compute ŷ\_ℓ = M\_ℓ r̂\_ℓ.

* **Target-space update gradient**: On observed subset S, loss L = ‖M\_sub r̂\_ℓ − y\_obs‖². The gradient w\.r.t. asset predictions is ∇ = M\_subᵀ (M\_sub r̂\_ℓ − y\_obs).

* **Shrinkage**: final\_ranks = (1 − λ)·rank\_model + λ·rank\_prior, with λ = clip(a + b·D\_feat, 0, 1).

---

## 6) Time & memory budget guidance

* **First call**: load artifacts, pre-allocate numpy arrays, and build ring buffers; do any light JIT.
* **Per day**: aim for < 300 ms for inference + mapping + ranking; keep update loop 5–20 s.
* **Skip MLP** in v1 to guarantee timing; enable later only if spare time is consistent.

---

## 7) Failure modes & fallbacks

* If **no labels** arrive for any lag or **n\_obs < N\_MIN\_UPDATE**, skip updates.
* If mapping matrix slice is ill-conditioned, skip asset-recovery and stick to target-space update (or skip entirely).
* If per-day preprocessing produces NaNs after filling (should not happen), emit **rank\_prior**; if no prior, emit uniform ranks.

---

## 8) Acceptance tests (run before first public submit)

1. **Leak-safety**: shift labels by +1 day in CV; fold score must collapse.
2. **Metric fidelity**: compute CV metric and verify stability across folds; no accidental weighting by n\_targets.
3. **Public-mirror dry-run** (local): iterate P = 1827..1916 using your measured offsets; ensure wall-time ≪ 60 s/day and no illegal label reads.
4. **Stress robustness**: score on 562–575 (inside training range) to confirm no blow-ups with winsorization.
5. **Small-coverage resilience**: synthesize low-coverage days; target-space updates remain stable and bounded.

---

## 9) Roadmap for v1.1 (once v1 lands)

* Turn `MLP_TOGGLE=True`, use N\_SEEDS=2, average with Ridge (0.6/0.4).
* Add **group-wise heads** (per asset class) if we observe class-specific gains.
* Enrich feature-side dispersion proxy D\_feat(P) with FX triangle residual volatility (already computed in your EDA).
* Tune λ policy form (piecewise linear or logistic in D\_feat) if warranted by CV.

---

### Quick TL;DR

* One file, one config block, one `predict()` with state.
* Train on 0–1826 only; never tune on 1827–1916.
* Stage-A models predict **asset returns per lag**, Stage-B maps to 424 targets, then per-day **rank + adaptive shrinkage**.
* Online updates use **only** today’s lagged labels (`date_id == P`), primarily via **target-space gradient steps**, respecting your verified offsets (2/3/4/5 days).
* Seeds are fixed; MLP is explicitly **off** in v1 but supported by the same single file.

If you want a **filled code scaffold** (function signatures + docstrings arranged exactly in this order) in one file, say the word and I’ll provide it.
