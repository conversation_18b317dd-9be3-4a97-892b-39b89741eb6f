Perfect—here’s a **tight, critical checklist** for exploring `train_labels.csv` so we learn exactly what the targets look like before touching models. No code; just what to compute and why it matters for this competition’s daily‑Spearman metric.

---

## 1) File integrity & granularity

* Shape, dtypes, uniqueness: confirm one row per `date_id`.
* Column schema: count of target columns (expect 424) and any extras.
* Non‑finite values: count NaN/±Inf per column; list columns with constant or near‑constant values.

**Why:** catches broken targets early; constant/near‑constant series break rank correlations and can create ties.

---

## 2) Coverage & availability (per target and per day)

* For each target: start\_date, end\_date, % non‑missing, length of the longest missing streak.
* For each date: number of available targets; min/median/max across the sample.
* Day‑over‑day **overlap set** size and **Jaccard index** between date t and t−1 (same targets present both days).

**Why:** the daily Spearman uses only targets available that day; unstable coverage or low overlap degrades usable cross‑sectional signal and affects CV design.

---

## 3) Distribution & tails (per target)

* Summary stats: mean, std, skew, kurtosis; percentiles (1, 5, 50, 95, 99).
* **Zero mass & ties:** share of exact zeros and the number of unique values per target; flag any discretization or rounding.
* **Extreme‑day diagnostics:** for each date, median(|target|) and 95th percentile(|target|); list top 10 spike days.

**Why:** Spearman is rank‑based, but lots of ties or discretized targets crush usable rank resolution; spike days drive metric variance and suggest robust loss or winsorized training targets.

---

## 4) Time‑series behavior (per target)

* Autocorrelation: ACF at lags 1, 5, 21 with confidence bands.
* Rolling stats: 60‑day rolling mean, std; change‑point tests for structural breaks.
* **Half‑life** approximation from AR(1) fit where applicable.

**Why:** tells us whether simple momentum/reversal exists and how frequently we should **update online** (high drift → shorter update cadence).

---

## 5) Cross‑sectional structure (per day)

* For each date: cross‑sectional mean, std, skew, and number of unique ranks across available targets.
* **Rank persistence:** Spearman between the cross‑section of targets at t and t−1 (matching the overlap set).
* **Dispersion stability:** distribution of cross‑sectional std over time; identify regimes with thin dispersion (harder days).

**Why:** this mirrors the competition metric more directly—if cross‑sectional ranks persist day‑to‑day, momentum‑style models will work; thin dispersion days cap the achievable score.

---

## 6) Cross‑target dependencies (over time)

* Correlation matrix across targets (Pearson and Spearman) over their common active periods.
* Factor structure: PCA on the target panel; variance explained by the first k factors; cluster targets by loadings.

**Why:** identifies shared drivers (e.g., “USD” or “metals” factors). If a few factors dominate, simple factor models + rank post‑processing may be strong and fast for online updates.

---

## 7) Alignment with `target_pairs` (do this once you join the map)

* Target taxonomy: single‑asset returns vs pair‑difference series; asset‑class tags (LME/JPX/US/FX).
* Effective **lag/horizon** per target (from the map); verify that label timing is consistent over the whole sample (no mid‑series flips).
* Group‑level stats: all summaries in sections 2–5 repeated within each asset class and within “pair vs single” groups.

**Why:** different groups will demand different features and may justify **group‑wise models** or losses.

---

## 8) Sanity checks specific to pairwise targets

* Directionality: confirm that pair definitions are consistent (e.g., A−B does not silently switch to B−A mid‑history).
* Antisymmetry: if both A−B and B−A exist, verify they are near‑perfect negatives; flag deviations.
* Net‑sum behavior: check if certain pair groups tend to sum to ≈0 cross‑sectionally on a given day.

**Why:** catches mapping errors and suggests post‑processing (e.g., enforcing zero‑sum within a group).

---

## 9) Metric realism & CV implications

* **Daily score volatility:** compute a proxy series: for each date, Spearman(targets, targets + ε) with small noise to estimate an upper‑bound stability; track its std over time.
* Public‑phase mimic: if the last \~90 dates will be used in public LB, inspect coverage, dispersion, rank persistence specifically in that window vs the earlier sample.

**Why:** helps choose **time‑blocked CV** that actually tracks LB and sets expectations on achievable Sharpe of daily Spearman.

---

## 10) Quality flags & exclusions

* Produce a candidate **drop list**: targets with <50% coverage, >80% ties on many days, frequent change‑points, or pathological rounding.
* Keep a **must‑keep list**: targets crucial for business relevance (e.g., core metals), even if noisy—note they may require group‑specific treatment.

**Why:** trimming hopeless series improves model stability and training time without hurting metric if those series rarely appear.

---

### Deliverables I suggest you produce

* **Coverage table** (targets × key stats) sorted by % availability and start date.
* **Calendar heatmaps** (date × target availability; date × cross‑sectional dispersion).
* **Rank‑persistence plot** (time series of daily cross‑section Spearman(t vs t−1)).
* **Factor map** (PCA scree + clustered heatmap of loadings).
* **Issue log** (targets with discretization/ties, sign flips, or structural breaks).

Once you have these, share the summaries (tables/plots or key numbers). I’ll use them to pin down: (1) CV splits, (2) group‑wise modeling strategy, and (3) whether we prioritize momentum vs reversal vs factor‑based ranks.
