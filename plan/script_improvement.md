太好了，这版已经把我们要的“最强版”框架搭起来了 👍。我通读了整份脚本——下面给出**按优先级排序的改动建议**，分为「**必须立即修复（阻断或明显影响得分）**」、「**高收益一致性改进（与设计目标对齐、可显著提分/稳分）**」、「**鲁棒性与效率提升**」。每条都给出**问题 → 为什么 → 怎么改 → 自检点**，并附上关键代码片段（可直接粘贴替换）。

---

## 一、必须立即修复（阻断/明显影响得分）

### A1. 预处理被“二次取差”（returns-of-returns）

* **问题**：`preprocess_fit()` 里先 `_to_returns()` 得到 `df_ret`，随后又把 `df_ret` 传给 `preprocess_transform()`，而 `preprocess_transform()` 在检测到含 `date_id` 时**再次** `_to_returns()`。这会把对数收益再做一次差分，信号被严重削弱甚至失真。
* **怎么改**：给 `preprocess_transform()` 增加参数 `already_returns: bool=False`；当传入的是 `df_ret` 时设为 `True`，从而跳过 `_to_returns()`。
* **改动示例**：

  ```python
  # --- in FeaturePreprocessor ---
  def preprocess_fit(self, train_df: pd.DataFrame) -> pd.DataFrame:
      ...
      df_ret = self._to_returns(df_clean)
      self.scalers = self._fit_robust_scalers(df_ret, (0, 1826))
      self.group_masks = self._infer_feature_groups(self.feature_list)
      self.fitted = True
      df_transformed = self.preprocess_transform(df_ret, already_returns=True)  # ← 关键
      ...

  def preprocess_transform(self, df: pd.DataFrame, already_returns: bool = False) -> pd.DataFrame:
      ...
      if 'date_id' in df_clean.columns:
          feature_cols = ['date_id'] + self.feature_list
          df_clean = df[feature_cols].copy()
      else:
          df_clean = df[self.feature_list].copy()
      # 仅当不是 returns 时才转换
      df_ret = df_clean if already_returns else self._to_returns(df_clean)
      ...
  ```
* **自检**：随便抽 3 列（如 `FX_EURUSD`、`LME_CA_Close`、`US_Stock_ACWI_adj_close`），检查训练阶段输出的均值约 0、标准差合理（\~1e-2 到 1e-1），不会接近 0。

---

### A2. CV 折索引与样本行**未对齐**（导致 CV 实际取错行）

* **问题**：`train_stage_a_models()` 里先构造 `X_lag, y_lag`（按 `r_std_by_lag[lag].keys()` 遍历字典顺序），再把 `folds`（里面是**日期 id**）直接当成**位置索引**去切分 `X/y`。实际索引不匹配，CV 形同虚设。
* **怎么改**：显式构建 **date\_id→position** 映射，并把折的 date\_id 转为相应的位置索引。
* **改动示例**：

  ```python
  # after building X_lag, y_lag, also keep the date order
  date_list = sorted(list(r_std_by_lag[lag].keys()))
  X_lag, y_lag = [], []
  for t in date_list:
      row = X_processed[X_processed['date_id'] == t]
      if len(row) > 0:
          X_lag.append(row.iloc[0][feature_cols].values)
          y_lag.append(r_std_by_lag[lag][t])
  X_lag = np.array(X_lag); y_lag = np.array(y_lag)
  pos_of_date = {d:i for i,d in enumerate(date_list)}

  # 在 DualModelLag.train() 前，把 folds 映射
  mapped_folds = []
  for tr_ids, va_ids in folds:
      tr_pos = np.array([pos_of_date[d] for d in tr_ids if d in pos_of_date], dtype=int)
      va_pos = np.array([pos_of_date[d] for d in va_ids if d in pos_of_date], dtype=int)
      mapped_folds.append((tr_pos, va_pos))

  results = dual_model.train(X_lag, y_lag, mapped_folds)
  ```
* **自检**：打印任意一个 fold 的 `valid_idx` 的实际日期范围与我们构建的时间段一致。

---

### A3. Stage‑A 模型的选型标准仍是**资产空间 MSE**，没有在**目标空间 Spearman**上选模

* **问题**：Ridge 的网格搜索仅比较 `-MSE(y_valid, y_pred)`，与最终评分不一致；MLP 也只看自身早停，未把“资产→目标→秩→日内 Spearman”纳入。
* **怎么改**：在 `DualModelLag.train()` 中注入一个**目标空间评估回调**（由 pipeline 提供）：给定 `(X_valid, y_valid_std)`，做

  1. 反标准化至资产原空间；
  2. `M_lag @ r̂` 得到该 lag 的目标预测；
  3. 与**对应日期**的真实目标（从 `train_labels` 取该 lag 的 106 列）组合成 424 维（四个 lag 聚合我们放在 pipeline 层做也可，最低限度先就 lag 内 106 维评价）；
  4. 计算**当日 Spearman**，取日均/Sharpe(ρ) 作为折内分数。
* **最小落地**（先在**单 lag 106 维**上进行）：

  * 在 `DualModelLag.train()` 里，给 `train()` 增加参数 `eval_cb: Callable(X_valid, y_valid_std)->float`，对每个 alpha 的每个 fold 取 `eval_cb` 返回的 Sharpe(ρ) 作为分数；最后选择 mean(Sharpe) 最大的 alpha。
* **自检**：打印“Ridge alpha on lag k chosen by target‑space Sharpe=…”，且与 MSE 选出的 alpha 有差异时能看到分数提升。

> 注：如果你希望“一步到位 424 维评分”，可以把四个 lag 同日的预测在外层聚合后再评估；先做“单 lag 106 维”也能把方向修正过来，已是显著改进。

---

### A4. `ingest_arrived_labels()` 的权重计算有误 & 覆盖统计需按当日可用数

* **问题**：现在的 `weight = LAG_WEIGHTS[lag] * (len(target_cols)/106)` 恒等于基权重（因为 `len(target_cols)==106`），没有体现“**当日可用目标比例**”。并且 `coverage_by_lag[lag]` 是**可用目标数**，很好，但融合时没用到。
* **怎么改**：用 `n_available/106` 作为这个 lag 的当日强度；融合多个 lag 的观测时做权重平均。
* **改动示例**（函数中替换权重段）：

  ```python
  # after counting n_available for this lag
  strength = n_available / 106.0
  weight = Config.LAG_WEIGHTS[lag] * strength
  ...
  # 将 (value, weight) 按 target 汇总加权平均保持不变
  ```
* **自检**：打印 `coverage_by_lag` 与各 lag 的 `strength`，低覆盖日（如 1891/1892/1916）自动调低该 lag 的影响。

---

### A5. `preprocess_transform()` 的缺失标记在 **ffill 之后**生成，不能反映原始缺失

* **问题**：`_add_missing_flags()` 用 `df_filled[col].isna()` 生成旗标，已经过 ffill，很多缺失被填掉，旗标丢失。
* **怎么改**：在 ffill 前先保存原始缺失掩码 `orig_nan_mask = df_norm.isna()`，ffill 后用 `orig_nan_mask[col]` 生成 `_missing` 旗标。
* **改动示例**：

  ```python
  def preprocess_transform(...):
      ...
      df_scaled = self._transform_robust(...)
      df_norm = self._group_normalize_per_day(df_scaled, self.group_masks)
      orig_nan_mask = df_norm.isna()                     # ← 保存
      df_filled = self._ffill_capped(df_norm, Config.FFILL_CAP)
      df_with_flags = self._add_missing_flags(df_filled, orig_nan_mask)  # ← 传入
      ...

  def _add_missing_flags(self, df_filled: pd.DataFrame, orig_nan_mask: pd.DataFrame) -> pd.DataFrame:
      df_with_flags = df_filled.copy()
      for col in self.feature_list:
          flag_col = f"{col}_missing"
          df_with_flags[flag_col] = orig_nan_mask[col].astype(int) if col in orig_nan_mask else 1
      return df_with_flags
  ```
* **自检**：核对一个早期起始日（很多列为 NaN）的 `_missing` 旗标应为 1；后续天即使被 ffill 填充，旗标仍为 1。

---

### A6. 文件末尾存在**语法错误**

* **问题**：最后一行：

  ```python
  print(f"Local gateway test failed: {e}"). 这是我更新后的脚本
  ```

  `.` 后的中文不在注释内，Python 语法错误。
* **怎么改**：改成注释：

  ```python
  print(f"Local gateway test failed: {e}")  # 这是我更新后的脚本
  ```
* **自检**：脚本本地可直接运行，`__main__` 分支不报错。

---

## 二、高收益一致性改进（提分/稳分核心）

### B1. 目标侧在线更新**要真正用起来**

* **问题**：`OnlineUpdater` 已有 `prior_ranks` 和 `update_target_side()`，但在 `predict_single()` 中只是把 `ingest_arrived_labels` 的结果直接覆盖 `prior_ranks`，没有做 rank‑calibration/EMA 融合。
* **怎么改**：

  1. 在 `ingest_arrived_labels()` 内返回 `obs_dict: {target->value}`（当前你只返回了融合后的 `prior_ranks`），或新增函数 `collect_y_obs(date_id, lag_batches)`；
  2. 在 `predict_single()` 开头调用 `self.online_updater.update_target_side(date_id, y_obs)`，令其用 **EMA** 融合到 `prior_ranks`，并缓存历史用于随后校准；
  3. `process_stage_b()` 的 `prior_ranks` 从 `self.online_updater.get_prior_ranks()` 获取（你已经这样做）。
* **自检**：日志显示“target‑side update: n\_avail=…，EMA α=…”，且 `prior_ranks` 在连续几天中平滑变化。

### B2. 资产侧在线更新（RLS/滚动再拟合）在预测循环中**落地调用**

* **问题**：你实现了 `update_asset_side()`，但 `predict_single()` 内**没有调用**；也未把恢复的 `r_true` 转成对 Ridge 的递推。
* **怎么改**（最小闭环）：

  1. 在 `predict_single()` 的末尾，根据四个 lag 的当日观测，构造每个 lag 的 `y_obs_lag` 和 `mask_lag`（就是 `label_lags_k_batch` 当日行的 106 列）；
  2. 用 `solver = self.solvers_by_lag[lag]` 反解 `r_true_lag = solver(y_obs, mask)`；
  3. 把 `feature_vector`（当天 x）与 `r_true_lag_std = (r_true_lag - asset_means[lag]) / asset_stds[lag]` 放入一个**简单的递推**：维护 `S=XᵀX` 与 `T=XᵀY`，周期性（如每 14 天）重分解，得到新系数；若时间不够，就只缓存，下一天再批量更新。
* **自检**：打印“asset‑side online update: lag=…, samples=…, time=…ms”；公共 90 天回放总时长远小于约束（60s/天）。

### B3. `process_stage_b()` 先 rank 再 shrink 是对的，但**确定性打散**的触发条件过严

* **问题**：浮点 shrink 后几乎不会“完全相等”，`tie` 很少触发。
* **怎么改**：对**差距极小（|Δ|<1e‑9）**的项也视为 tie；或直接在**rank 前**对 `model_ranks` 做一个极微的、确定性的扰动（不改变排序稳定性）。
* **改动示例**：

  ```python
  # before final rankdata
  eps = Config.TIE_EPSILON
  for i in range(len(final_ranks)):
      final_ranks[i] += eps * deterministic_hash(date_id, i)
  final_ranks = stats.rankdata(final_ranks, method='ordinal').astype(float)
  ```
* **自检**：多次运行结果字节级一致，且不出现大范围相对次序跳变。

### B4. `compute_D_feat` 的尺度需要**标准化**

* **问题**：`D_feat` 直接使用 MAD 的中位数，单位不一、未归一。
* **怎么改**：在训练时统计 `D_feat` 的历史分布（0..1826），保存其分位数或上界 `D_max`，推理时用 `D_feat / D_max` 裁剪至 \[0,1]；你在 `compute_alpha()` 里除以 2.0 是个固定近似，建议替换为数据驱动。
* **自检**：打印 `D_feat_norm` 的均值在 0.3\~0.7，不会长期贴边。

---

## 三、鲁棒性与效率

### C1. `ingest_arrived_labels()` 未使用的 `target_date` 变量

* **问题**：定义了 `target_date = date_id - offset`，但未使用。
* **改**：去掉或用于校验 `label_date_id`（可断言：`current_row['label_date_id'] == target_date`，若存在列名差异要先判断）。

### C2. `target_cols` 过滤中使用了 `col != 'target_date_id'`（拼写）

* **问题**：实际列名是 `label_date_id`；虽不影响筛选（因为我们用 `startswith('target_')`），但建议清理。
* **改**：把那段写成：

  ```python
  target_cols = [c for c in lag_df.columns if c.startswith('target_')]
  ```

### C3. 预处理的逐行分组归一 `_group_normalize_per_day()` 复杂度高

* **建议**：可用向量化（groupby‑apply）优化；短期考虑到行数 \~1900，能跑就先不动，作为后续提速项。

### C4. MLP 的 dropout 配置未生效

* **问题**：`sklearn.MLPRegressor` 没有 Dropout；`Config.MLP_DROPOUT` 无法应用。
* **建议**：保留参数作为备注，但说明“仅在换成 PyTorch/TF 版本时生效”。

---

## 关键位置的最小补丁清单（可直接落地）

1. **修复二次取差**（见 A1 代码块）。
2. **修复 CV 索引映射**（见 A2 代码块）。
3. **权重按当日覆盖**（见 A4 代码块）。
4. **缺失旗标在 ffill 前生成**（见 A5 代码块）。
5. **预测循环内调用在线更新**（B1/B2，伪代码）：

   ```python
   # in predict_single(), Step 1 后：
   y_obs_map = {}  # {target: value} from four lag batches of THIS day
   for lag in [1,2,3,4]:
       lag_df = lag_batches[lag].to_pandas()
       row = lag_df[lag_df['date_id']==date_id]
       if len(row)==0: continue
       row = row.iloc[0]
       for c in [c for c in lag_df.columns if c.startswith('target_')]:
           v = row[c]
           if not pd.isna(v):
               y_obs_map[c] = float(np.clip(v, -5, 5))
   # 目标侧在线更新（rank-calibration/EMA）
   self.online_updater.update_target_side(date_id, y_obs_map)

   # ……最终得到 final_ranks 之后，再做资产侧在线更新（若覆盖充足且有时间）
   for lag in [1,2,3,4]:
       lag_df = lag_batches[lag].to_pandas()
       row = lag_df[lag_df['date_id']==date_id]
       if len(row)==0: continue
       row = row.iloc[0]
       targets_lag = self.targets_by_lag[lag]
       y_obs = np.array([row.get(t, np.nan) for t in targets_lag], dtype=float)
       mask = ~np.isnan(y_obs)
       if mask.sum() >= Config.N_MIN_UPDATE:
           r_true = self.solvers_by_lag[lag](np.clip(y_obs, -5, 5), mask)
           if r_true is not None:
               # TODO: 把 (feature_vector, r_true_std) 喂给递推岭统计量
               pass
   ```

---

很好，这个日志把**一处致命 Bug**暴露得很清楚：
**`matmul` 维度不匹配（size 1 vs 106）**出现在推理阶段把资产预测映射到目标时（`M_lag @ r_pred`）。根因是**单样本推理**时，`Ridge/MLP` 返回的形状是 `(1, n_assets)`，而映射矩阵是 `(n_targets=106, n_assets)`，导致 `(106,106) @ (1,106)` 维度对不上。

下面按“**先能跑通、再提分**”的顺序给出修复与关键改动。

---

## 一、立刻修复：推理形状统一为一维向量

### 1) `DualModelLag.predict` 保证单样本输出是 `(n_assets,)`

**为什么**：Sklearn 多输出回归对单样本返回 `(1, n_outputs)`；我们需要 `(n_outputs,)`。
**怎么改**：在 `predict()` 里把 `ridge_pred/mlp_pred` 都 `squeeze` 成一维，再融合。

```python
class DualModelLag:
    ...
    def predict(self, X: np.ndarray) -> np.ndarray:
        X = np.nan_to_num(X, nan=0.0)
        if X.ndim == 1:
            X = X.reshape(1, -1)

        ridge_pred = self.ridge.predict(X)
        if ridge_pred.ndim == 2 and ridge_pred.shape[0] == 1:
            ridge_pred = ridge_pred[0]

        if self.mlp is not None:
            try:
                mlp_pred = self.mlp.predict(X)
                if mlp_pred.ndim == 2 and mlp_pred.shape[0] == 1:
                    mlp_pred = mlp_pred[0]
                final_pred = (self.fusion_weights[0] * ridge_pred +
                              self.fusion_weights[1] * mlp_pred)
            except Exception as e:
                print(f"Warning: MLP prediction failed for lag {self.lag}: {e}, using Ridge only")
                final_pred = ridge_pred
        else:
            final_pred = ridge_pred

        # 最终再确保一维
        return np.asarray(final_pred).reshape(-1)
```

### 2) `process_stage_b()` 再双重保险：`r_pred` 强制扁平

**为什么**：即便上一步做了，这里再兜一层底，防止上游遗漏。
**怎么改**：

```python
def process_stage_b(...):
    ...
    for lag in [1, 2, 3, 4]:
        if lag in asset_predictions and lag in M_by_lag:
            M_lag = M_by_lag[lag]                    # (106, n_assets)
            r_pred = np.asarray(asset_predictions[lag]).reshape(-1)  # (n_assets,)
            y_lag = M_lag @ r_pred                   # (106,)
            target_predictions.extend(y_lag)
        else:
            n_targets_lag = M_by_lag.get(lag, np.zeros((106, 106))).shape[0]
            target_predictions.extend([0.0] * n_targets_lag)
    ...
```

**复跑期望**：不再出现“size 1 is different from 106”的连续 Warning；每个 `date_id` 能顺利产出 1×424 的预测行。

---

## 二、立即合并的几处“隐形扣分项”（会显著稳分/提分）

> 这些是我上一个答复里标成“必须”的 A1–A5；建议一并打掉，避免你在修形状后，又踩到其他坑。

### A1. **二次取差**（returns-of-returns）

* **问题**：`preprocess_fit()` 已 `_to_returns()`，`preprocess_transform()` 又 `_to_returns()`（因带 `date_id`）。
* **修复**：给 `preprocess_transform()` 增参 `already_returns=False`；在 fit 阶段传 `True`，推理阶段传 `False`（推理输入是原始水平价量）。

  ```python
  df_transformed = self.preprocess_transform(df_ret, already_returns=True)

  def preprocess_transform(self, df, already_returns=False):
      ...
      df_ret = df_clean if already_returns else self._to_returns(df_clean)
      ...
  ```

### A2. **CV 折索引与样本错位**

* **问题**：折里用的是**日期 id**，但 `X_lag/y_lag` 是按 `dict.keys()` 顺序堆出来的**位置索引**；导致 CV 其实没按时间。
* **修复**：构建 `date_list`，用 `{date_id→pos}` 映射把折的日期 id 转为位置索引，送入 `DualModelLag.train()`（见我上条消息给的代码段）。
* **价值**：选到真正泛化更好的超参，而不是“看起来稳定”的 0.03。

### A3. **用目标空间 Spearman 选模**（而不是资产空间 MSE）

* **问题**：现在 Ridge 网格用 `-MSE` 选；不对齐比赛指标。
* **修复**：在 `DualModelLag.train()` 引入 `eval_cb` 回调，使每个 fold 的验证：`r̂_std → 反标准化 → M_lag @ r̂ → （仅本 lag 的 106 目标）→ 当日 Spearman → 折内 Sharpe(ρ)`；按 Sharpe(ρ) 选 alpha。
* **价值**：通常能明显抬升线上稳定性与分数。

### A4. **lag 动量融合权重未体现“当日覆盖度”**

* **修复**：`ingest_arrived_labels()` 中使用 `strength = n_available / 106.0`，并把 `weight = LAG_WEIGHTS[lag] * strength` 融到目标的加权平均。
* **价值**：在 1891/1892/1916 等**小样本日**自动加大 `prior_ranks` 比重，稳住成绩。

### A5. **缺失旗标在 ffill 之后生成**

* **修复**：在 ffill 前缓存 `orig_nan_mask = df_norm.isna()`，用它生成 `_missing` 列（而不是用填充后的）。
* **价值**：模型能“知道这列本来缺失”，更鲁棒。

---

## 三、建议顺手改的小点（提升稳定与可复现）

1. **确定性打散**：在最终 rank 前给每个目标加极小哈希扰动（`+ 1e-6 * hash(date_id, target_idx)`），避免浮点近似下的大组 ties。
2. **`ingest_arrived_labels` 列名小误**：过滤目标列时只用 `startswith('target_')`，别排 `'target_date_id'`（实际列是 `label_date_id`）。
3. **末尾语法错误**：把

   ```python
   print(f"Local gateway test failed: {e}"). 这是我更新后的脚本
   ```

   改为

   ```python
   print(f"Local gateway test failed: {e}")  # 这是我更新后的脚本
   ```

---

## 四、复跑时你应该看到的关键日志（健康形态）

* 训练：

  * `Built 4 time-blocked folds with 10-day purge`
  * `Training models for lag k... Ridge alpha chosen by target-space Sharpe: ...`（你实现回调后）
* 推理（公共 90 天本地回放）：

  * **不再**有 matmul 维度 warning；
  * 每日打印：`date_id=..., n_avail=..., alpha(shrink)=..., D_feat=...`；
  * 低覆盖日（1891/1892/1916）`alpha` 明显更大（更靠 prior\_ranks）。


