Here’s a **focused checklist** to analyze each `test_labels_lag_*.csv` locally. No code—just what to compute and why it matters for our online‑update loop and leak‑safety.

---

### 1) Integrity & schema

* Confirm **shape** = 90 rows × 108 cols (expect: `date_id`, 106 targets for that lag, `label_date_id`).
* Verify **column naming** matches the expected target block per lag:

  * lag‑1 → `target_0…target_105`
  * lag‑2 → `target_106…target_211`
  * lag‑3 → `target_212…target_317`
  * lag‑4 → `target_318…target_423`
* Check **dtypes** are numeric for targets; `date_id` and `label_date_id` are integers.
* Look for **duplicates / missing rows** by `(date_id, label_date_id)` key; assert uniqueness.

### 2) Arrival schedule (offsets)

* Compute **offset = date\_id − label\_date\_id** per row; summarize **min/median/max** for each lag.

  * Expect a **constant offset** per lag (e.g., +2 for lag‑1, +5 for lag‑4, whatever your files show).
* Flag any **jitter** (offset changes across days), which would require a more defensive ingest routine.

### 3) Coverage & availability

* Per lag, per `date_id`: count **non‑missing** labels across the 106 targets.
* Identify **days with zero or very low coverage**; list them (these are “no‑update” days).
* Cross‑lag **availability matrix:** for each `date_id` (public day P), how many labels you can ingest **by lag**—this determines how much online learning is possible each day.

### 4) Consistency with `train_labels` (ground truth)

* For each row, compare values to `train_labels` at **`label_date_id`** for the corresponding target columns; compute **max |diff|** and fraction of exact matches.

  * Expect exact equality; any drift implies rounding or join issues.

### 5) Missingness patterns

* Per lag: overall **NaN %**, **per‑target** NaN %, and **per‑date** NaN %.
* Check whether missingness **clusters by asset class** (once targets are mapped via `target_pairs`).

### 6) Distribution & drift (public window only)

* For each target block (106 per lag) over their **label\_date\_id window (1827–1916)**: mean, std, skew, kurtosis, p1/p99 of values.
* Compare to the **full‑history** stats from `train_labels` to see if the public window is **harder/easier** than average.

### 7) Cross‑sectional dynamics (what powers Spearman)

* For each `label_date_id`: compute **cross‑sectional dispersion** (std across the 106 targets in that lag block) and **rank‑persistence** vs previous day (on the overlap set).
* Summarize **persistence vs dispersion** relationship in the public window; this informs our **shrinkage schedule**.

### 8) Asset‑mapping feasibility (for our two‑stage model)

* For each lag and each **label\_date\_id**, form the linear system to recover asset returns (106 targets → 106 assets).
* Check the system is solvable with observed missingness; compute **condition number** or success rate.

  * If singular on some days, note we’ll need **regularized least squares** (ridge) for the update step on those days.

### 9) Outliers & stress handling

* Per `label_date_id`, compute **p95(|label|)** and the **share of outliers** (e.g., |value| > 3× MAD).
* Flag public‑window days with **unusual tails**; these are candidates for **winsorized** updates.

### 10) Update value proxy (offline)

* Simulate the public window day‑by‑day:

  * For each P, pretend you **only** ingest rows with `date_id == P`.
  * Measure a **quick proxy uplift**: after ingesting P’s labels, re‑fit a tiny linear head on a short rolling window and evaluate on **P+1** (using train labels for ground truth).
* Summarize **with‑update vs no‑update** improvement (mean daily Spearman and Sharpe) to validate the loop.

### 11) Edge cases & robustness

* Confirm behavior when **some lags arrive, others don’t** on the same `date_id`.
* Verify **no future labels** are ever read (build a guard: reject rows with `date_id > current_day`).
* Check **first/last day** boundaries (cold‑start at 1827; final updates near 1916).

### 12) Outputs to save (so we can code once)

* A **per‑day ingest plan**: table with `date_id`, available lags, counts per lag, and `label_date_id`s covered.
* **Offset constants** per lag (or a map if jitter exists).
* A list of **no‑update days** per lag and **low‑coverage days** (threshold you choose).
* **Conditioning warnings** (days where mapping is ill‑posed).
* **Shrinkage hints**: relation between (feature‑side dispersion proxies) and label‑side dispersion in the public window (for training the λ policy offline).

Run these checks once; they’ll directly drive the **ingest guards**, **online‑update cadence**, and the **fallback logic** in the submission notebook. When you have the summaries, share the offset stats, availability matrix, and any ill‑posed days—we’ll lock the final update policy from that.
