"""Template for the two classes hosts should customize for each competition."""

import abc
import os
import time
import warnings

from typing import Any, Callable, Generator, Optional, Tuple, Union

import pandas as pd
import polars as pl

import kaggle_evaluation.core.base_gateway
import kaggle_evaluation.core.relay


_initial_import_time = time.time()
_issued_startup_time_warning = False


class Gateway(kaggle_evaluation.core.base_gateway.BaseGateway, abc.ABC):
    """
    Template to start with when writing a new gateway.
    In most cases, hosts should only need to edit the three abstract methods below.

    There are two main methods for sending data to the inference_server hosts should understand:
    - Small datasets: use `self.predict`, which gets called by default.
    Competitors will receive the data passed to self.predict as Python objects in memory. This is
    just a wrapper for self.client.send(); you can write additional wrappers if necessary.
    - Large datasets: it's much faster to send data via self.share_files, which is equivalent to making
    files available via symlink. See base_gateway.BaseGateway.share_files for the full details.
    """

    @abc.abstractmethod
    def unpack_data_paths(self) -> None:
        """Map the contents of self.data_paths to the competition-specific entries
        Each competition should respect these paths to make it easy for competitors to
        run tests on their local machines or with custom files.

        Should include default paths to support data_paths = None.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def generate_data_batches(self) -> Generator:
        """Used by the default implementation of `get_all_predictions` so we can
        ensure `competition_agnostic_validation` is run every time `predict` is called.

        This method must yield both the batch of data to be sent to `predict` and a series
        of row IDs to be used for validation and the submission file. For example:
            return (test_feature_batch, test_metadata_batch), row_id_batch
        """
        raise NotImplementedError

    @abc.abstractmethod
    def competition_specific_validation(
        self, prediction_batch: Any, row_ids: Union[pl.DataFrame, pl.Series, pd.DataFrame, pd.Series], data_batch: Any
    ) -> None:
        """Competition specific checks should be added here. Typically you'll want to confirm the predictions are a valid data type at a minimum.
        Args:
            - prediction_batch: The user's predictions.
            - row IDs: The associated row IDs generated by the gateway.
            - data_batch: Data originally provided to the user.
        """
        raise NotImplementedError


class InferenceServer(abc.ABC):
    """
    Base class for competition participants to inherit from when writing their submission. In most cases, users should
    only need to implement a `predict` function or other endpoints to pass to this class's constructor, and hosts will
    provide a mock Gateway for testing.
    """

    def __init__(self, *endpoint_listeners: Callable):
        self.server = kaggle_evaluation.core.relay.define_server(*endpoint_listeners)
        self.client = None  # The inference_server can have a client but it isn't typically necessary.
        self._issued_startup_time_warning = False
        self._startup_limit_seconds = kaggle_evaluation.core.relay.STARTUP_LIMIT_SECONDS

    def serve(self) -> None:
        self.server.start()
        if os.getenv('KAGGLE_IS_COMPETITION_RERUN') is not None:
            self.server.wait_for_termination()  # This will block all other code

    @abc.abstractmethod
    def _get_gateway_for_test(self, data_paths, file_share_dir=None, *args, **kwargs):
        # Must return a version of the competition-specific gateway able to load data for unit tests.
        raise NotImplementedError

    def run_local_gateway(self, data_paths: Optional[Tuple[str]] = None, file_share_dir: str = None, *args, **kwargs) -> None:
        """Construct a copy of the gateway that uses local file paths."""
        global _issued_startup_time_warning
        script_elapsed_seconds = time.time() - _initial_import_time
        if script_elapsed_seconds > self._startup_limit_seconds and not _issued_startup_time_warning:
            warnings.warn(
                f"""{int(script_elapsed_seconds)} seconds elapsed before server startup.
                This exceeds the startup time limit of {int(self._startup_limit_seconds)} seconds that the gateway will enforce
                during the rerun on the hidden test set. Start the server before performing any time consuming steps.""",
                category=RuntimeWarning,
            )
            _issued_startup_time_warning = True

        self.server.start()
        try:
            self.gateway = self._get_gateway_for_test(data_paths, file_share_dir, *args, **kwargs)
            self.gateway.run()
        except Exception as err:
            raise err from None
        finally:
            self.server.stop(0)
