### Jane Street Real-Time Market Data Forecasting Competition (Kaggle, 2024-2025)

**Challenge**: Forecast "responder_6" (an anonymized market return metric, clipped to [-5, 5]) up to six months ahead using real-time market data, mimicking production trading systems. Models must handle fat-tailed distributions, non-stationary time series, regime shifts, and anonymized features while predicting timestep-by-timestep via an API (no forward peeking). Evaluation: Weighted zero-mean R² on responder_6, with a training phase (historical test set) followed by a live forecasting phase against unseen market data (updated biweekly until July 14, 2025). Submissions rescored periodically; prize pool $100,000 (top prizes $25k, $15k, $10k). Emphasizes robustness to evolving markets and inference speed (under time limits).

**Data**: Time-series Parquet files spanning ~500 historical days (training: ~4.5M rows; test similar size). Rows per unique symbol_id (39 encrypted instruments) and timestamp (date_id: day integer, time_id: intra-day order, up to 968/day). Columns: 79 anonymized features (continuous, some sparse/NaN), 9 responders (responder_0-8, with metadata in responders.csv), weights for scoring, is_scored flag (private only scored). Lags Parquet provides prior-day responders. Features.csv has metadata. Non-stationary; symbols may appear/disappear; real intervals vary. Training from date_id >700 for stability; test includes unscored historical extensions.

**Public LB High Ensemble Solution (e.g., ~0.0078; NN + XGBoost + TabM + Ridge)**: Multi-model blend for regression. Preprocessing: Standardize continuous features, encode categoricals (features 09-11, symbol_id, time_id), add 9 lagged responders, ffill NaNs. Models: (1) 5-fold NN (MLP-like with SiLU, dropout 0.1-0.3, BN; AdamW lr=1e-3, wd=8e-4; weighted MSE loss); (2) 5-fold XGBoost (default params, includes symbol/time); (3) TabM (Tabular Transformer-like with MLP backbone, 3 blocks d=512, dropout 0.25; R² loss, AdamW); (4) Ridge (linear, fills NaNs with 3). Ensemble: Weighted average (0.5 NN+XGB, 0.4 TabM, 0.1 Ridge). Handles lags via join; clips preds [-5,5]. Focuses on diversity for robustness; CV correlates with LB.

**Private LB 8th / Public 6th Solution (0.0112; Time-Series GRU with Online Learning)**: GRU-based sequence model (per-day inputs) with auxiliary targets and real-time updates. Preprocessing: Standardize, impute NaNs=0; add market avgs (per date/time), rolling stats (avg/std over 1000 time_ids per symbol), time_id; drop categoricals 09-11. Features: 125 total. Model: Two GRUs (1-layer + 2 linear/ReLU/dropout; 3-layer vanilla) per 4 responders (responder_6/7/8 + derived rolling avgs over 8/60 days); linear fusion to responder_6; weighted R² loss. Train: lr=5e-4, batch=1 day, 8 epochs. Ensemble: 3 seeds each GRU (unweighted avg). Online: Update weights daily (lr=3e-4) on new data (scored+unscored) via forward pass. Improves CV +0.008; handles gaps without full retrain. Inference: 0.06s/step, 3.6s/update.