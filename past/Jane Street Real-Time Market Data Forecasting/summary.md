**Executive summary — Jane <PERSON> Real‑Time Market Data Forecasting (Kaggle 2024–2025)**

**Challenge.** Predict the anonymized market target responder\_6 (clipped to \[−5, 5]) **timestep‑by‑timestep through an evaluation API** that mimics real trading, then survive a **six‑month live forecasting phase with periodic rescoring**. Data are **fat‑tailed, noisy, and non‑stationary**; inference must be fast (tight per‑step time budget) and robust to regime shifts. **Metric:** weighted zero‑mean R², R² = 1 − Σ wᵢ(yᵢ − ŷᵢ)² / Σ wᵢ yᵢ². **Timeline:** training phase ends January 13, 2025; final leaderboard July 14, 2025. **Prize pool:** 100k USD. &#x20;

**Data.** \~4.5M training rows, **79 anonymized features**, **9 responders** (only responder\_6 is scored), **weights**, **lags of responders served daily**, symbols can appear/disappear, **up to 968 time\_ids per day** (stabilizes after date\_id ≈ 700, which several solutions start from). Submission API streams data sequentially and forbids look‑ahead.  &#x20;

---

## Representative solutions (what, why, caveats)

**(A) Private LB 8th / Public 6th — GRU + auxiliary targets + **online learning** (LB 0.0112 vs 0.0105 best single).**

* **Model:** Two GRU variants (1‑layer GRU + 2 linear layers vs 3‑layer GRU), trained **per‑day sequences**, **auxiliary targets** responder\_7/8 plus two **engineered rolling‑average responders**; final responder\_6 via linear head. **Weighted zero‑mean R² loss.**
* **FE:** Standardize, NaN→0, drop categorical 09–11, **market averages and 1000‑time\_id rolling stats**, keep time\_id; **start from date\_id = 700**.
* **CV:** 2 time folds (200‑day val), plus a **200‑day gap simulation** for the private phase.
* **Online learning:** **One weight‑update per day** at lr=3e‑4 on newly revealed data (even is\_scored=False) → **+0.008 CV R²**. Inference ≈ 0.06s/step; weight update ≈ 3.6s/day. Considered but skipped full retraining due to the **1‑minute limit per date**.
* **Why it worked:** Multi‑task targets stabilize training; **online adaptation directly addresses drift**.
* **Caveats:** Small #folds, modest ensemble (6 models); numerical/ops complexity of rolling features and online updates.&#x20;

**(B) Public LB 12th wrap‑up — 2 architectures (GRU/MLP/Transformer with symbol‑wise attention) + **daily online learning**, multi‑target.**

* **Features:** 79 raw (exclude 09–11), time\_id, **mean/std of lagged responders**; NaN→0.
* **Training sets:** 600/800/978 days; **validate on last 120 days** in both offline and online modes; final **8‑model ensemble** (978 & 800‑day trains kept).
* **Online learning:** Daily updates (no special treatment of aux‑target losses).
* **Why it worked:** Diversity across **architectures & train window lengths**, **multi‑target** supervision, and **explicit online adaptation**.
* **Caveats:** Heavy engineering to keep the pipeline inside the 1‑minute SLA; lots of dead‑ends (GBDTs underperformed, many DL variants failed before landing on working configs).&#x20;

**(C) Public top‑notebook style **heterogeneous ensemble** — NN×5 + XGB×5 + TabM + Ridge (LB ≈ 0.0078).**

* **Preprocess:** Standardization, categorical encoding (09/10/11, symbol\_id, time\_id), **9 lagged responders**, forward‑fill then 0.
* **Models:**

  * **NN (5‑fold MLP)** with SiLU, BN, dropout, Adam(W), weighted MSE → strong non‑linear baseline.
  * **XGBoost (multiple folds/seeds)** on same features.
  * **TabM / FT‑Transformer‑like** model (R² loss, AdamW, categorical embeddings) for **architectural diversity**.
  * **Ridge baseline** (LB 0.0042) shows linear ceiling.
* **Blend:** 0.50 (NN+XGB) + 0.40 (TabM) + 0.10 (Ridge), clip to \[−5,5].
* **Why it worked:** **Cross‑paradigm ensembling** to hedge model/seed instability and non‑stationarity.
* **Caveats:** No explicit online learning; risk of drift in the live phase.&#x20;

---

## What consistently mattered

* **Online learning / continual adaptation** materially boosts R²; even **tiny daily updates** are worth more than elaborate static CV tricks. &#x20;
* **Multi‑target (all responders) training** provides free regularization and stronger shared signal. &#x20;
* **Time‑series CV with realistic gaps** correlates to public LB; simulating the private gap helps.&#x20;
* **Heterogeneous ensembles** (DL + GBDT + TabM + linear) improve robustness; linear models alone are far from competitive.&#x20;
* **Inference engineering** (rolling features, memory, per‑step latency) is a first‑class objective, not an afterthought.&#x20;

---

## Pitfalls / gotchas

* **1‑minute per‑date SLA**: full retrains are impractical unless fragmented across steps.&#x20;
* **Severe seed/model instability**: many DL variants failed; broad search + ensembling was needed.&#x20;
* **Leakage via lags & responders**: must respect the API contract (only lagged responders at time\_id=0).&#x20;
* **Feature drift**: number of time\_ids changes early; several teams **start at date\_id ≥700** to stabilize.&#x20;

---

## Transferable takeaways

1. **Design for drift**: make online/continual learning a core primitive, not a patch.&#x20;
2. **Multi‑target supervision** (aux responders, engineered rolling targets) is a cheap way to densify the signal. &#x20;
3. **Engineer the inference stack early** (synthetic test sets, profiling, strict time budget).&#x20;
4. **Blend diverse inductive biases** (sequence models, tabular transformers, GBDTs, linear) to survive regime shifts.&#x20;

Want me to start a **cross‑competition matrix** (targets, metrics, CV styles, what worked) now, or keep doing one‑pagers and summarize patterns at the end?
