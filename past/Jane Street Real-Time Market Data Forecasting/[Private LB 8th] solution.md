[Private LB 8th] solution
First of all, thanks to the host for this amazing competition! It was a rare and exciting opportunity to apply deep learning to tabular data and test how well models can adapt to new data in real-time, simulating real-world conditions. I really enjoyed participating and learning throughout the process. Thanks also to all the participants who contributed to public discussions - I learned a lot from you! @victors<PERSON><PERSON><PERSON>, @lihaorocky, @johnpayne0, @shiyili

Link to the code https://github.com/evgeniavolkova/kagglejanestreet
Link to the submission notebook https://www.kaggle.com/code/eivolkova/public-6th-place?scriptVersionId=217330222

1. Cross-validation
I used a time-series CV with two folds. The validation size was set to 200 dates, as in the public dataset. It correlated well with the public LB scores. Additionally, the model from the first fold was tested on the last 200 dates with a 200-day gap to simulate the private dataset scenario.

2. Feature engineering and data preparation
2.1 Sample
I used data starting from date_id = 700, as this is when the number of time_ids stabilizes at 968. I experimented with using the entire dataset, but it did not result in any score improvement.

2.2 Data preparation
Simple standardization and NaN imputation with zero were applied. Other methods didn't provide any improvement.

2.3 Feature engeneering
I used all original features except for three categorical ones (features 09–11). I also selected 16 features that showed a high correlation with the target and created two groups of additional features:

Market averages: Averages per date_id and time_id.
Rolling statistics: Rolling averages and standard deviations over the last 1000 time_ids for each symbol.
Besides that, I added time_id as a feature.

Adding these features resulted in an improvement of about +0.002 on CV.

3. Model architecture
3.1 Base model
Time-series GRU with sequence equal to one day. I ended up with two slightly different architectures:

3-layer GRU
1-layer GRU followed by 2 linear layers with ReLU activation and dropout.
The second model worked better than the first model on CV (+0.001), but the first model still contributed to the ensemble, so I kept it.

MLP, time-series transformers, cross-symbol attention and embeddings didn't work for me.

3.2 Responders
I used 4 responders as auxiliary targets: responder_7 and responder_8, and two calculated ones:

df = df.with_columns(
    (
        pl.col("responder_8")
        + pl.col("responder_8").shift(-4).over("symbol_id")
    ).fill_null(0.0).alias("responder_9"),
    (
        pl.col("responder_6")
        + pl.col("responder_6").shift(-20).over("symbol_id")
        + pl.col("responder_6").shift(-40).over("symbol_id")
    ).fill_null(0.0).alias("responder_10"),
)
These are approximate rolling averages of the base target over 8 and 60 days, respectively. As described in detail in this discussion by @johnpayne0, responder_6 is a 20-day rolling average of some variable, while responder_7 and responder_8 are 120-day and 4-day rolling averages of the same variable, with some added noise. Given an N-day rolling average, we can easily calculate N*K-day rolling averages.

A separate base model was used for each auxiliary target. The predictions from these models were then passed through a linear layer to produce the final target output, responder_6.

The sum of losses (weighted zero-mean R²) for each responder was used to train the model.

Adding auxiliary targets improved both CV and LB scores by about +0.001.

Models were trained using a batch size of one day, with a learning rate of 0.0005.
For submission, I trained models on data up to the last date_id, using the number of epochs equal to the average optimal number of epochs on CV.

3.3 Ensemble
I ran both models on 3 seeds and took a simple unweighted average of predictions from those 6 models. This resulted in an LB score of 0.0112 (vs best single model LB 0.0105).

4. Online Learning
During inference, when new data with targets becomes available, I perform one forward pass to update the model weights with a learning rate of 0.0003. This approach significantly improved the model’s performance on CV (+0.008). Interestingly, for an MLP model, the score without online learning was higher than for the GRU, but lower with online learning.

Updates are performed only with the responder_6 loss, without auxiliary targets.

Updates are applied for the entire dataset provided during submission, including rows with is_scored = False.

I also considered performing a full online retraining on the data up to the start of the private dataset. This would make sense because there is a significant gap between the training data and the private dataset. However, retraining the model would require distributing the training process across multiple inference steps, as the one-minute time limit between dates would not be sufficient. I believe this would have been feasible but I decided not to spend time on it, although my tests suggested that it could provide a +0.001 improvement in the score. Still, I find it amazing that, instead of a full model retraining, performing one-day updates for almost a year is enough, and the model continues to perform well.

5. Technical details
5.1 Inference Speed
Inference speed was critically important, so I spent a significant amount of time optimizing my code, particularly data processing and calculation of rolling features.

For my final submission, it takes 0.06 seconds to run one inference step (time_id), 0.02 of which are spent on data processing. Updating model weights once per date_id takes 3.6 seconds.

I used PyTorch, but since TensorFlow is said to be faster, I tried switching to it. However, after a few days of experimenting, I couldn't achieve better performance, so I decided to stick with PyTorch.

5.2 Technical stack
Due to RAM requirements, I switched from Google Colab to vast.ai and was extremely happy with the decision. I wrote code locally, enjoying all the perks of VSCode, and then ran a script to push the code to github, pull it on the server and execute scripts remotely.

I also used WandB to monitor experiments, which helped me keep track of scores and easily revert to an older version of the code if something went wrong.

To debug my submission notebook and estimate submission time I used synthetic dataset by @shiyili.

6. Scores
CV fold 0	CV fold 1v	Fold 1 with 200 days gap	CV avg
GRU 1 without both auxiliary targets and online learning	0.0161	0.0062	0.0011	0.0112
GRU 1 without auxiliary targets	0.0235	0.0148	0.0136	0.0190
GRU 1	0.0249	0.0153	0.0147	0.0201
GRU 2	0.0262	0.0166	0.0161	0.0214
GRU 1 + GRU 2	0.0268	0.0169	0.0163	0.0218
GRU 1 3 seeds	0.0258	0.0164	0.0152	0.0211
GRU 2 3 seeds	0.0267	0.0175	0.0163	0.0221
GRU 1 + GRU 2 3 seeds	0.0270	0.0175	0.0162	0.0222
Fold 0: date_ids from 1298 to 1498.
Fold 1: date_ids from 1499 to 1698.

# code

Imports¶
import os
os.system('pip install --force-reinstall /kaggle/input/janestreet2025-code/janestreet-0.1-py3-none-any.whl')

import time
import copy

import numpy as np
import pandas as pd
import polars as pl
import torch 

from kaggle_evaluation import jane_street_inference_server

from janestreet.pipeline import FullPipeline, PipelineCV
from janestreet.data_processor import DataProcessor
from janestreet.config import PATH_DATA
Settings
RUN_NAME = "full"
MODEL_NAMES = ["gru_2.0_700", "gru_2.1_700", "gru_2.2_700", "gru_3.0_700", "gru_3.1_700", "gru_3.2_700"]
WEIGHTS = np.array([1.0]*len(MODEL_NAMES))/ len(MODEL_NAMES)
WEIGHTS = WEIGHTS/sum(WEIGHTS)
N_ROLL = 1000

print("_".join(MODEL_NAMES))
print(WEIGHTS)
gru_2.0_700_gru_2.1_700_gru_2.2_700_gru_3.0_700_gru_3.1_700_gru_3.2_700
[0.16666667 0.16666667 0.16666667 0.16666667 0.16666667 0.16666667]
Load models
data_processor = DataProcessor(MODEL_NAMES[0]).load()

pipelines = {}
for model_name in MODEL_NAMES:
    pipeline = FullPipeline(
        None,
        run_name=RUN_NAME,
        name=model_name,
        load_model=True,
        features=None,
        save_to_disc=False
    )
    pipeline.fit(verbose=True)
    pipelines[model_name] = pipeline

    print("-"*100)
    print(model_name)
    print(pipeline.model.get_params())
    print(f"Number of features: {len(pipeline.features)}")
    print(pipeline.model.model.num_resp)
WARNING: Preprocessor not found.
----------------------------------------------------------------------------------------------------
gru_2.0_700
{'model_type': 'gru', 'hidden_sizes': [500], 'dropout_rates': [0.3, 0.0, 0.0], 'hidden_sizes_linear': [500, 300], 'dropout_rates_linear': [0.2, 0.1], 'lr': 0.0005, 'batch_size': 1, 'epochs': 8, 'early_stopping_patience': 1, 'early_stopping': False, 'lr_patience': 10, 'lr_factor': 0.5, 'lr_refit': 0.0003, 'random_seed': 0}
Number of features: 125
4
WARNING: Preprocessor not found.
----------------------------------------------------------------------------------------------------
gru_2.1_700
{'model_type': 'gru', 'hidden_sizes': [500], 'dropout_rates': [0.3, 0.0, 0.0], 'hidden_sizes_linear': [500, 300], 'dropout_rates_linear': [0.2, 0.1], 'lr': 0.0005, 'batch_size': 1, 'epochs': 8, 'early_stopping_patience': 1, 'early_stopping': False, 'lr_patience': 10, 'lr_factor': 0.5, 'lr_refit': 0.0003, 'random_seed': 1}
Number of features: 125
4
WARNING: Preprocessor not found.
----------------------------------------------------------------------------------------------------
gru_2.2_700
{'model_type': 'gru', 'hidden_sizes': [500], 'dropout_rates': [0.3, 0.0, 0.0], 'hidden_sizes_linear': [500, 300], 'dropout_rates_linear': [0.2, 0.1], 'lr': 0.0005, 'batch_size': 1, 'epochs': 8, 'early_stopping_patience': 1, 'early_stopping': False, 'lr_patience': 10, 'lr_factor': 0.5, 'lr_refit': 0.0003, 'random_seed': 2}
Number of features: 125
4
WARNING: Preprocessor not found.
----------------------------------------------------------------------------------------------------
gru_3.0_700
{'model_type': 'gru', 'hidden_sizes': [250, 150, 150], 'dropout_rates': [0.0, 0.0, 0.0], 'hidden_sizes_linear': [], 'dropout_rates_linear': [], 'lr': 0.0005, 'batch_size': 1, 'epochs': 8, 'early_stopping_patience': 1, 'early_stopping': False, 'lr_patience': 10, 'lr_factor': 0.5, 'lr_refit': 0.0003, 'random_seed': 0}
Number of features: 125
4
WARNING: Preprocessor not found.
----------------------------------------------------------------------------------------------------
gru_3.1_700
{'model_type': 'gru', 'hidden_sizes': [250, 150, 150], 'dropout_rates': [0.0, 0.0, 0.0], 'hidden_sizes_linear': [], 'dropout_rates_linear': [], 'lr': 0.0005, 'batch_size': 1, 'epochs': 8, 'early_stopping_patience': 1, 'early_stopping': False, 'lr_patience': 10, 'lr_factor': 0.5, 'lr_refit': 0.0003, 'random_seed': 1}
Number of features: 125
4
WARNING: Preprocessor not found.
----------------------------------------------------------------------------------------------------
gru_3.2_700
{'model_type': 'gru', 'hidden_sizes': [250, 150, 150], 'dropout_rates': [0.0, 0.0, 0.0], 'hidden_sizes_linear': [], 'dropout_rates_linear': [], 'lr': 0.0005, 'batch_size': 1, 'epochs': 8, 'early_stopping_patience': 1, 'early_stopping': False, 'lr_patience': 10, 'lr_factor': 0.5, 'lr_refit': 0.0003, 'random_seed': 2}
Number of features: 125
4
Load tail of train data for rolling features calculation
MAX_DATE = 1698
COLS_ID = ['row_id', 'date_id', 'time_id', 'symbol_id', 'weight', 'is_scored']

df_raw = pl.scan_parquet(f"{PATH_DATA}/train.parquet")
df_raw = df_raw.filter(pl.col("date_id")>=MAX_DATE-10)
df_raw = df_raw.collect()
df_raw = df_raw.with_columns(
    pl.lit(-1).cast(pl.Int64).alias("row_id"),
    pl.lit(True).alias("is_scored"),
    (pl.col("date_id")-MAX_DATE-1).alias("date_id")
)
df_raw = df_raw.select(COLS_ID + data_processor.COLS_FEATURES_INIT)

df_raw = (
    df_raw.filter(pl.col("date_id") >= -5)
    .sort(['date_id', 'time_id', 'symbol_id'])
)

hidden_states = [None] * len(pipelines)
dfs = []
Predict
DEBUG = False

CNT_DATES = 9
CNT_DATES_NOT_SCORED = 4

time_start = time.time()
time_start_not_scored = time.time()
time_est = 0
time_est_not_scored = 0
cnt_dates = 0

def predict(test: pl.DataFrame, lags: pl.DataFrame | None) -> pl.DataFrame | pd.DataFrame:
    """Make a prediction."""
    start_time = time.time()
    
    global df_raw
    global hidden_states
    global pipeline
    global dfs
    global time_est, time_start, time_start_not_scored, time_est_not_scored
    global cnt_dates
    
    date_id = test["date_id"][0]
    time_id = test["time_id"][0]
    is_scored = test["is_scored"][0]

    # Count time for debug
    if DEBUG:
        if not is_scored:
            time_est_not_scored = time.time()-time_start_not_scored
        else:
            time_est = time.time()-time_start

        if time_id == 0:
            print("-" * 100)
            
            if date_id == 1:
                time_start_not_scored = time.time()
    
            if date_id == CNT_DATES_NOT_SCORED: 
                time_start = time.time()

    # Reset hidden states and collect data for weights update
    if time_id == 0:
        cnt_dates += 1
        hidden_states = [None for _, p in pipelines.items()]
        lags = lags.with_columns(
            pl.col("responder_6_lag_1").alias("responder_6"),
            pl.lit(date_id-1).cast(pl.Int16).alias("date_id")
        ).select(["date_id", "time_id", "symbol_id", "responder_6"])
        if cnt_dates > 1:
            df = pl.concat(dfs)
            dfs = []
            df = df.join(lags, on=["date_id", "time_id", "symbol_id"], how="left")
            df = df.sort(["date_id", "time_id", "symbol_id"])

    # Add data to raw dataframe
    test = test.select(df_raw.columns)
    df_raw = pl.concat([df_raw, test], how="vertical_relaxed")
    df_raw = df_raw.select(test.columns)
    
    # Cut raw data (keep last N_ROLL time_ids for each symbol)
    df_raw = (
        df_raw
        .group_by(["symbol_id"])
        .tail(N_ROLL)
    )

    # Calculate features and save
    df_cur = data_processor.process_test_data(df_raw, fast=True, date_id=date_id, time_id=time_id, symbols=test["symbol_id"])
    df_cur = df_cur.sort(["symbol_id"])
    dfs.append(df_cur)
    df_cur = df_cur.with_columns(pl.lit(None).alias("responder_6"))
    
    # Update model weights
    if (time_id == 0) & (cnt_dates > 1):
        if len(df) > 968:
            for i, (name, pipeline) in enumerate(pipelines.items()):
                pipeline.update(df)

    # Make predictions
    if is_scored:
        preds = []
        for i, (name, pipeline) in enumerate(pipelines.items()):
            pred, hidden_states[i] = pipeline.predict(df_cur, hidden=hidden_states[i], n_times=1)
            preds.append(pred)
        pred = np.average(preds, axis=0, weights=WEIGHTS)
    
        df_cur = df_cur.with_columns(pl.Series("responder_6", pred))
        df_cur = test.select(["date_id", "time_id", "symbol_id"]).join(df_cur, on=["date_id", "time_id", "symbol_id"], how="left")
        predictions = df_cur.select(["row_id", "responder_6"])
    else:
        predictions = test.select(
            'row_id',
            pl.lit(0.0).alias('responder_6'),
        )

    if DEBUG:
        if time_id % 100 == 0:
            n_nans = sum(sum(predictions.fill_nan(None).null_count().to_numpy()))
            print(
                f"{date_id} {time_id:3.0f} (is_scored {is_scored}): "
                f"time elps {time.time()-start_time:.4f}, # nans {n_nans}"
            )
    else:
        if (time_id==0)&(date_id==0):
            print(predictions)
            print((time_id, time.time()-start_time))
    
    return predictions

    
inference_server = jane_street_inference_server.JSInferenceServer(predict)

if os.getenv('KAGGLE_IS_COMPETITION_RERUN'):
    inference_server.serve()
else:
    if not DEBUG:
        inference_server.run_local_gateway(
            (
                f'{PATH_DATA}/test.parquet',
                f'{PATH_DATA}/lags.parquet',
            )
        )
    else:
        inference_server.run_local_gateway(
            (
                '/kaggle/input/js24-rmf-submission-api-debug-with-synthetic-test/synthetic_test.parquet',
                '/kaggle/input/js24-rmf-submission-api-debug-with-synthetic-test/synthetic_lag.parquet',
            )
        )

if DEBUG:
    time_est_cur = time_est/(CNT_DATES-CNT_DATES_NOT_SCORED)*200/60/60
    time_est_scored = time_est/(CNT_DATES-CNT_DATES_NOT_SCORED)*120/60/60
    time_est_not_scored = time_est_not_scored/(CNT_DATES_NOT_SCORED-1)*240/60/60
    time_est_final = time_est_scored + time_est_not_scored
    print("-"*100)
    print(f"Estimated current time: {time_est_cur:.4f}")
    print(f"Estimated final time (is_score=True): {time_est_scored:.4f}")
    print(f"Estimated final time (is_score=False): {time_est_not_scored:.4f}")
    print(f"Estimated final time: {time_est_final:.4f}")
/usr/local/lib/python3.10/dist-packages/torch/nn/modules/rnn.py:1139: UserWarning: RNN module weights are not part of single contiguous chunk of memory. This means they need to be compacted at every call, possibly greatly increasing memory usage. To compact weights again call flatten_parameters(). (Triggered internally at ../aten/src/ATen/native/cudnn/RNN.cpp:1424.)
  result = _VF.gru(input, hx, self._flat_weights, self.bias, self.num_layers,
shape: (39, 2)
┌────────┬─────────────┐
│ row_id ┆ responder_6 │
│ ---    ┆ ---         │
│ i64    ┆ f64         │
╞════════╪═════════════╡
│ 0      ┆ 0.006225    │
│ 1      ┆ 0.001164    │
│ 2      ┆ -0.015933   │
│ 3      ┆ -0.013856   │
│ 4      ┆ 0.028691    │
│ …      ┆ …           │
│ 34     ┆ -0.011823   │
│ 35     ┆ -0.003499   │
│ 36     ┆ -0.005554   │
│ 37     ┆ -0.004253   │
│ 38     ┆ -0.014129   │
└────────┴─────────────┘
(0, 1.0164384841918945)