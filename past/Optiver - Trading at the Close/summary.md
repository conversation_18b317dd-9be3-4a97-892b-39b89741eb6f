### Optiver - Trading at the Close Competition (Kaggle, 2023)

**Challenge**: Predict the "target" (60-second future index-weighted average price move minus 60-second future stock price move) during Nasdaq's 10-minute closing auction for ~200 stocks. Models process iterative reveals (every 20 seconds, up to 55 per auction) with online updates; must handle non-stationarity, auction dynamics (imbalances, orders), and time constraints (inference <2s/step). Regression task; evaluation via mean absolute error (MAE) on unseen private period (post-Nov 2023, ~200 days). Public LB on holdout; emphasizes real-time adaptation amid volatility.

**Data**: train.csv (~5.2M rows across 481 days, 200 anonymized stocks): stock_id, date_id, seconds_in_bucket (0-540 in 10s increments), imbalance_size/flag (unmatched shares), reference_price (auction midpoint), matched_size (paired shares), far/near_price (non-auction/auction book midpoints), bid/ask_price/size, wap (weighted avg price), target (to predict), row_id. Features sparse (e.g., far_price from 300s); no explicit test set—API simulates reveals. Supplementary: sample_submission.csv.

### 6th Place Solution: Sequence-to-Sequence Transformers and GRU with Online Learning

**Solution Overview**: Deep learning for time-series prediction (seq2seq); preprocess: zero-impute NaNs, standardize. Features: ~35-36 (raw + seconds flags + borrowed: volume, mid-price, imbalances—liquidity/matched/size/pairwise/harmonic). Models: 3 transformers (dim=64, encoder=2-day history, decoder=4 layers + stock-wise attention, linear head); 1 GRU (dim=128, decoder=2 layers + attention, 2 FC head); output shape (batch, stocks, 55 timesteps). Train: MSE loss + zero-sum constraint (postprocess for one model). Validation: Time-split (train 359 days, val 121); EMA-smoothed metric. Online: Daily incremental updates on new data. Ensemble: Average 4 models. Achieved private MAE 5.4285; emphasizes DL over heavy FE.

### 9th Place Solution: XGBoost with Feature Engineering and Post-Processing

**Solution Overview**: Gradient boosting focus; preprocess: Scale sizes by stock median. Features: 157 (raw + basics: add/sub/mult/div; imb1/imb2 imbalances; market_urgency variants; diffs/shifts/rolls on windows [1-60]; global date-second weights; MACD on prices; mock targets via WAP shifts/rolls). Selection: Group-wise addition, retain top per group via CV uplift. Model: XGBoost (MAE obj, depth=10, subsample=0.77, etc.; 3 seeds; GPU; 1.5x weight on last 45 days; 5-fold time-split CV). Online: Retrain twice (at private start, +30 days). Postprocess: Subtract weighted sum (enforce zero-sum targets). Validation: Stable CV-LB correlation; private aligned with CV. Key: Feature selection optimized time/memory; postprocess improved stability.