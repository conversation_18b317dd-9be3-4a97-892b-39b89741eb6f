**Executive summary — Optiver: Trading at the Close (Kaggle 2023–2024)**

**Challenge.** Predict each stock’s **60‑second future WAP move relative to a synthetic index** during the **last 10 minutes of trading** (55 timesteps per day), under a **time‑series API that forbids look‑ahead**. **Metric:** Mean Absolute Error (MAE). A **live, post‑submission forecasting phase** periodically re-scored models on real market data.

**Data (what matters).** \~5.2M train rows covering 200 stocks × 481 days × 55 seconds\_in\_bucket steps; rich auction + order‑book features (prices, sizes, imbalance, WAP). Targets for the prior day are revealed at time\_id = 0, enabling **online / incremental learning**. Many teams **center predictions cross‑sectionally (weighted mean = 0)** to respect how the target is defined.

---

## Top shared solutions

### 1st place — **CatBoost + GRU + Transformer** (+ online learning + post‑processing)

**Blend:** CatBoost 0.5, GRU 0.3, Transformer 0.2, all trained on **the same 300 features** selected via CatBoost importance. **OL every 12 days (4 actually executed due to time limits)**; **post‑process by subtracting the stock‑weighted mean**. Simple **holdout CV (first 400 days train / last 81 valid)** tracked the LB well, so the author optimized mostly on CV. Extra gains from **seconds\_in\_bucket group features**, **per‑second rank features**, and a **zero‑mean trick in the Transformer head**. Larger Transformers, CNNs, MLPs, and multi‑day inputs for the GRU **didn’t help**. Final private LB 5.4030 (slightly overtime).

**Why it worked:**

* **Heterogeneous ensemble** mixing tabular trees and sequence models captured both cross‑sectional and temporal structure.
* **Periodic online retraining** handled drift without blowing the 1‑minute/day SLA (helped more than squeezing extra CV folds).
* **Strong post‑processing** aligned predictions with the evaluation definition.

**Caveats:** Memory/time constraints make OL with large GBDTs tricky (needs per‑day chunk loading). Overtime risk is real.

---

### 6th place — **Seq2Seq Transformers ×3 + GRU, all with stock‑wise attention & daily online updates**

Small, **clean DL stack (35–36 features)**: raw inputs + simple engineered flags (seconds\_in\_bucket bins, imbalance, mid price, volume). **Zero‑impute + standardize.** **Encoder uses 2 days of history; decoder has 4 Transformer layers** (or 2‑layer GRU). **Outputs forced to sum to zero** (except one model). **Time split 359/121 days**; because MAE was noisy per epoch, the team used **EMA smoothing** to pick checkpoints. **Online incremental learning each day** materially improved validation. **Ensemble = simple average of 4 models.** Private LB MAE 5.4285.

**Why it worked:**

* **Daily OL** exploited the revealed targets and stabilized performance under drift.
* **Attention over stocks** captured cross‑sectional relations with minimal feature heavy‑lifting.
* **EMA smoothing** reduced the metric’s training noise.

**Caveats:** Limited FE → depends on the model’s capacity; needs careful latency engineering to stay under API limits.

---

### 9th place — **Pure XGBoost (3 seeds) + heavy FE + light OL**

Came late, **focused on GBDTs only**: **157 carefully curated features** built in staged groups and **kept only if CV improved** (rolling stats, MACD, imbalance & “market urgency” constructs, cross‑sectional “global” features, target mocks, etc.). **Sample‑weight recent 45 days (1.5×).** **Post‑process by subtracting weighted sum**, **two online retrains** (at N and N+30) to respect time/memory limits. Similar LB for LightGBM, but **GPU XGBoost trained faster**. Private LB 9th.

**Why it worked:**

* **Massive, domain‑guided FE** can still compete with DL when OL is constrained.
* **Tight feature selection** to survive the strict latency/memory budget.

**Caveats:** Less adaptable to drift (only 2 OL steps); engineering‑heavy; trees struggled to scale OL beyond \~200 features without tricks.

---

## What consistently mattered

* **Online / continual learning** (even lightweight daily updates) was a **key differentiator**.
* **Cross‑sectional centering (subtract weighted mean / enforce zero‑sum)** directly improves MAE given how the target is defined.
* **Simple time holdouts correlated well with LB** → many teams optimized primarily on a single late holdout.
* **Latency & memory are first‑class constraints**: they shaped feature counts, OL frequency, and even which model families were viable.
* **Heterogeneous ensembling** (trees + GRU/Transformer) gave the best of both worlds; pure DL or pure GBDT could still score high, but blends topped out.

---

## Pitfalls & dead ends (from winners’ notes)

* **Overtime kills submissions**; OL frequency had to be dialed back.
* **Bigger isn’t better**: large Transformers, DeBERTa‑style models, CNN/MLP add‑ons didn’t pay off.
* **Multi‑day sequences for GRUs** underperformed single‑day setups here.
* **GBDT OL doubles memory** unless you stream per‑day shards.

---

## Transferable takeaways

1. **Design OL from day 1**—budget time/memory to update models as soon as new labels drop.
2. **Exploit cross‑sectional structure**: stock‑wise attention or explicit global statistics help.
3. **Post‑processing aligned to the metric is free alpha** (weighted zero‑mean).
4. **Validate with a realistic final block**; here, a single late holdout tracked LB well.
5. **Blend inductive biases** when you can afford it; otherwise, either go **lean DL + OL** or **heavy FE + trees** under strict latency.

Want me to start the **cross‑competition matrix** now (targets, metrics, CV, OL usage, model families, post‑processing) so we can see the patterns side‑by‑side?
