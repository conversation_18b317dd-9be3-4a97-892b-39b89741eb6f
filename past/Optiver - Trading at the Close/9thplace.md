9th Place Solution
A big thanks to <PERSON><PERSON><PERSON> and <PERSON><PERSON> for hosting this competition. This competition has a really stable correlation between local cv and lb.

Actually I entered this game a little late, about 30 days before its ends and I am not good at NN, so I only focus on Gradient Boosting tree models and its feature engineering. I noticed there are many top solutions using NN and it is really a good opportunity for me to learn NN.

Model
Xgboost with 3 different seeds and same 157 features
There is not much difference between Xgboost and Lightgbm in lb score. But GPU Xgboost trains faster than GPU Lightgbm.
Feature Engineering
Firstly, create some "basic features" based on raw features(i.e. add, subtract, multiply, divide from raw features). Also, create some median-scaled raw size features.
size_col = ['imbalance_size','matched_size','bid_size','ask_size']
for _ in size_col:
    train[f"scale_{_}"] = train[_] / train.groupby(['stock_id'])[_].transform('median')
Secondly, do further feature engineering/aggregation on raw features and "basic features"
imb1, imb2 features
market_urgency feateures I copied from public notebook
diff features on different time window
shift features on different time window
rolling_mean/std features on different time window
using history wap to calculate target of 6 second before. Then, do some rolling_mean
some global date_id+seconds weighted features
MACD feateures
target rolliong_mean over stock_id + seconds_in_bucket
Feature Selection
Because we have limit on inference time and memory, it's essential to do some feature selection. I add features group by group and check whether the local cv improves. Each feature group usually have 10 - 30 features. If one groups make local cv improve, I add feature one by one insides this feature group and usually kept only 5-10 most effective features.
I keep 157 features in my final model.
Post-processing:
Subtract weighted sum. From the definition of target, we can know weighted sum of target for all stocks should be zero.
test_df['pred'] = lgb_predictions
test_df['w_pred'] = test_df['weight'] * test_df['pred']
test_df["post_num"] = test_df.groupby(["date_id","seconds_in_bucket"])['w_pred'].transform('sum') / test_df.groupby(["date_id","seconds_in_bucket"])['weight'].transform('sum')
test_df['pred'] = test_df['pred'] - test_df['post_num']
Others:
xgb mae objective
xgb sample_weight 1.5 weight for latest 45 days data
Online training. I only retrain model twice. one is N day (N is the start date of private lb), the other is N+30 day.
polars and reduce_mem_usage function helps a lot
Codes
train: https://github.com/ChunhanLi/9th-kaggle-optiver-trading-close
inference: https://www.kaggle.com/code/hookman/9th-submission


local cv
import pandas as pd
import numpy as np
import lightgbm as lgb
from itertools import combinations
import polars as pl
pl.__version__
train = pd.read_pickle("../raw_data/train.pkl")
train = train[~train['target'].isna()]
print(train.shape)

size_col = ['imbalance_size','matched_size','bid_size','ask_size']
for _ in size_col:
    train[f"scale_{_}"] = train[_] / train.groupby(['stock_id'])[_].transform('median')
    
#buy-side imbalance; 1
#sell-side imbalance; -1
#no imbalance; 0
train['auc_bid_size'] = train['matched_size']
train['auc_ask_size'] = train['matched_size']
train.loc[train['imbalance_buy_sell_flag']==1,'auc_bid_size'] += train.loc[train['imbalance_buy_sell_flag']==1,'imbalance_size']
train.loc[train['imbalance_buy_sell_flag']==-1,'auc_ask_size'] += train.loc[train['imbalance_buy_sell_flag']==-1,'imbalance_size']


weight_df = pd.DataFrame()
weight_df['stock_id'] = list(range(200))
weight_df['weight'] =  [
    0.004, 0.001, 0.002, 0.006, 0.004, 0.004, 0.002, 0.006, 0.006, 0.002, 0.002, 0.008,
    0.006, 0.002, 0.008, 0.006, 0.002, 0.006, 0.004, 0.002, 0.004, 0.001, 0.006, 0.004,
    0.002, 0.002, 0.004, 0.002, 0.004, 0.004, 0.001, 0.001, 0.002, 0.002, 0.006, 0.004,
    0.004, 0.004, 0.006, 0.002, 0.002, 0.04 , 0.002, 0.002, 0.004, 0.04 , 0.002, 0.001,
    0.006, 0.004, 0.004, 0.006, 0.001, 0.004, 0.004, 0.002, 0.006, 0.004, 0.006, 0.004,
    0.006, 0.004, 0.002, 0.001, 0.002, 0.004, 0.002, 0.008, 0.004, 0.004, 0.002, 0.004,
    0.006, 0.002, 0.004, 0.004, 0.002, 0.004, 0.004, 0.004, 0.001, 0.002, 0.002, 0.008,
    0.02 , 0.004, 0.006, 0.002, 0.02 , 0.002, 0.002, 0.006, 0.004, 0.002, 0.001, 0.02,
    0.006, 0.001, 0.002, 0.004, 0.001, 0.002, 0.006, 0.006, 0.004, 0.006, 0.001, 0.002,
    0.004, 0.006, 0.006, 0.001, 0.04 , 0.006, 0.002, 0.004, 0.002, 0.002, 0.006, 0.002,
    0.002, 0.004, 0.006, 0.006, 0.002, 0.002, 0.008, 0.006, 0.004, 0.002, 0.006, 0.002,
    0.004, 0.006, 0.002, 0.004, 0.001, 0.004, 0.002, 0.004, 0.008, 0.006, 0.008, 0.002,
    0.004, 0.002, 0.001, 0.004, 0.004, 0.004, 0.006, 0.008, 0.004, 0.001, 0.001, 0.002,
    0.006, 0.004, 0.001, 0.002, 0.006, 0.004, 0.006, 0.008, 0.002, 0.002, 0.004, 0.002,
    0.04 , 0.002, 0.002, 0.004, 0.002, 0.002, 0.006, 0.02 , 0.004, 0.002, 0.006, 0.02,
    0.001, 0.002, 0.006, 0.004, 0.006, 0.004, 0.004, 0.004, 0.004, 0.002, 0.004, 0.04,
    0.002, 0.008, 0.002, 0.004, 0.001, 0.004, 0.006, 0.004,
]
train = train.merge(weight_df,how='left',on=['stock_id'])
(5237892, 18)
train
stock_id	date_id	seconds_in_bucket	imbalance_size	imbalance_buy_sell_flag	reference_price	matched_size	far_price	near_price	bid_price	...	time_id	row_id	fold	scale_imbalance_size	scale_matched_size	scale_bid_size	scale_ask_size	auc_bid_size	auc_ask_size	weight
0	0	0	0	3180602.69	1	0.999812	13380276.64	NaN	NaN	0.999812	...	0	0_0_0	2	1.547844	0.635182	3.001806	0.376896	16560879.33	13380276.64	0.004
1	1	0	0	166603.91	-1	0.999896	1642214.25	NaN	NaN	0.999896	...	0	0_0_1	0	1.031025	0.593159	0.261912	1.560460	1642214.25	1808818.16	0.001
2	2	0	0	302879.87	-1	0.999561	1819368.03	NaN	NaN	0.999403	...	0	0_0_2	0	0.945033	0.457058	3.063661	1.372570	1819368.03	2122247.90	0.002
3	3	0	0	11917682.27	-1	1.000171	18389745.62	NaN	NaN	0.999999	...	0	0_0_3	0	2.187799	0.308522	0.114156	22.488728	18389745.62	30307427.89	0.006
4	4	0	0	447549.96	-1	0.999532	17860614.95	NaN	NaN	0.999394	...	0	0_0_4	0	0.223200	0.790134	0.982033	0.025198	17860614.95	18308164.91	0.004
...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...
5237887	195	480	540	2440722.89	-1	1.000317	28280361.74	0.999734	0.999734	1.000317	...	26454	480_540_195	2	1.033448	1.177623	1.259572	12.147279	28280361.74	30721084.63	0.004
5237888	196	480	540	349510.47	-1	1.000643	9187699.11	1.000129	1.000386	1.000643	...	26454	480_540_196	0	0.692713	1.753503	9.938564	4.276373	9187699.11	9537209.58	0.001
5237889	197	480	540	0.00	0	0.995789	12725436.10	0.995789	0.995789	0.995789	...	26454	480_540_197	2	0.000000	1.324129	1.117586	11.965859	12725436.10	12725436.10	0.004
5237890	198	480	540	1000898.84	1	0.999210	94773271.05	0.999210	0.999210	0.998970	...	26454	480_540_198	0	0.132882	1.117509	0.828980	4.372968	95774169.89	94773271.05	0.006
5237891	199	480	540	1884285.71	-1	1.002129	24073677.32	1.000859	1.001494	1.002129	...	26454	480_540_199	2	1.430339	1.523099	4.448923	5.182861	24073677.32	25957963.03	0.004
5237892 rows × 25 columns

train['date_id'].max()
480
def generate_features_no_hist_polars(df):
    # 加一个ask_size - bid_size的特征 然后Rolling
    df = pl.from_pandas(df)
    feas_list = ['stock_id','seconds_in_bucket','imbalance_size','imbalance_buy_sell_flag',
               'reference_price','matched_size','far_price','near_price','bid_price','bid_size',
                'ask_price','ask_size','wap','scale_imbalance_size','scale_matched_size','scale_bid_size','scale_ask_size'
                 ,'auc_bid_size','auc_ask_size']
    # 基础特征
    df = df.with_columns([
        # 阶段1
        (pl.col('ask_size') * pl.col('ask_price')).alias("ask_money"),
        (pl.col('bid_size') * pl.col('bid_price')).alias("bid_money"),
        (pl.col('ask_size') + pl.col("auc_ask_size")).alias("ask_size_all"),
        (pl.col('bid_size') + pl.col("auc_bid_size")).alias("bid_size_all"),
        (pl.col('ask_size') + pl.col("auc_ask_size") + pl.col('bid_size') + pl.col("auc_bid_size")).alias("volumn_size_all"),
        (pl.col('reference_price') * pl.col('auc_ask_size')).alias("ask_auc_money"),
        (pl.col('reference_price') * pl.col('auc_bid_size')).alias("bid_auc_money"),
        (pl.col('ask_size') * pl.col('ask_price') + pl.col('bid_size') * pl.col('bid_price')).alias("volumn_money"),
        (pl.col('ask_size') + pl.col('bid_size')).alias('volume_cont'),
        (pl.col('ask_size') - pl.col('bid_size')).alias('diff_ask_bid_size'),
        (pl.col('imbalance_size') + 2 * pl.col('matched_size')).alias('volumn_auc'),
        ((pl.col('imbalance_size') + 2 * pl.col('matched_size')) * pl.col("reference_price")).alias('volumn_auc_money'),
        ((pl.col('ask_price') + pl.col('bid_price'))/2).alias('mid_price'),
        ((pl.col('near_price') + pl.col('far_price'))/2).alias('mid_price_near_far'),
        (pl.col('ask_price') - pl.col('bid_price')).alias('price_diff_ask_bid'),
        (pl.col('ask_price') / pl.col('bid_price')).alias('price_div_ask_bid'),
        (pl.col('imbalance_buy_sell_flag') * pl.col('scale_imbalance_size')).alias('flag_scale_imbalance_size'),
        (pl.col('imbalance_buy_sell_flag') * pl.col('imbalance_size')).alias('flag_imbalance_size'),
        (pl.col('imbalance_size') / pl.col('matched_size') * pl.col('imbalance_buy_sell_flag')).alias("div_flag_imbalance_size_2_balance"),
        ((pl.col('ask_price') - pl.col('bid_price')) * pl.col('imbalance_size')).alias('price_pressure'),
        ((pl.col('ask_price') - pl.col('bid_price')) * pl.col('imbalance_size') * pl.col('imbalance_buy_sell_flag')).alias('price_pressure_v2'),
        ((pl.col("ask_size") - pl.col("bid_size")) / (pl.col("far_price") - pl.col("near_price"))).alias("depth_pressure"),
        (pl.col("bid_size") / pl.col("ask_size")).alias("div_bid_size_ask_size"),
    ])
    feas_list.extend(['ask_money', 'bid_money', 'ask_auc_money','bid_auc_money',"ask_size_all","bid_size_all","volumn_size_all",
                      'volumn_money','volume_cont',"volumn_auc","volumn_auc_money","mid_price",
                      'mid_price_near_far','price_diff_ask_bid',"price_div_ask_bid","flag_imbalance_size","div_flag_imbalance_size_2_balance",
                     "price_pressure","price_pressure_v2","depth_pressure","flag_scale_imbalance_size","diff_ask_bid_size"])        

    # 各种ratio
    # 提升微忽几微
    add_cols = []
    for col1, col2 in [
        ("imbalance_size","bid_size"),
        ("imbalance_size","ask_size"),
        ("matched_size","bid_size"),
        ("matched_size","ask_size"),
        ("imbalance_size","volume_cont"),
        ("matched_size","volume_cont"),
        ("auc_bid_size","bid_size"),
        ("auc_ask_size","ask_size"),
        ("bid_auc_money","bid_money"),
        ("ask_auc_money","ask_money"),
    ]:
        add_cols.append((pl.col(col1) / pl.col(col2)).alias(f"div_{col1}_2_{col2}"))
        feas_list.append(f"div_{col1}_2_{col2}")        
    df = df.with_columns(add_cols)

    # 阶段2 不平衡特征
    # 除了price相关
    # 没加auc的ask/bid的 构造price以及不平衡进去
    add_cols = []
    for pair1,pair2 in [
        ('ask_size','bid_size'),
        ('ask_money','bid_money'),
        ('volumn_money','volumn_auc_money'),
        ('volume_cont','volumn_auc'),
        ('imbalance_size','matched_size'),
        ('auc_ask_size','auc_bid_size'),
        ("ask_size_all",'bid_size_all')
    ]:
        col_imb = f"imb1_{pair1}_{pair2}"
        add_cols.extend([
            ((pl.col(pair1) - pl.col(pair2)) / (pl.col(pair1) + pl.col(pair2))).alias(col_imb),
        ])
        feas_list.extend([col_imb])
    df = df.with_columns(add_cols)
    
    # price侧的imb1
    fea_append_list = []
    prices = ["reference_price", "far_price", "near_price", "ask_price", "bid_price", "wap","mid_price"]
    for c in combinations(prices, 2):
        fea_append_list.append(((pl.col(c[0]) - pl.col(c[1])) / (pl.col(c[0]) + pl.col(c[1]))).alias(f"imb1_{c[0]}_{c[1]}"))
        # fea_append_list.append((pl.col(c[0]) - pl.col(c[1])).alias(f"diff_{c[0]}_{c[1]}"))
        feas_list.extend([f"imb1_{c[0]}_{c[1]}"])
    df = df.with_columns(fea_append_list)
    
    
    # 不平衡特征 累计乘
    df = df.with_columns([
        ((pl.col("imb1_ask_size_bid_size") + 2) * (pl.col("imb1_ask_price_bid_price") + 2) * (pl.col("imb1_auc_ask_size_auc_bid_size")+2)).alias("market_urgency_v2"),
        (pl.col('price_diff_ask_bid') * (pl.col('imb1_ask_size_bid_size'))).alias('market_urgency'),
        (pl.col('imb1_ask_price_bid_price') * (pl.col('imb1_ask_size_bid_size'))).alias('market_urgency_v3'),
    ])
    feas_list.extend([f"market_urgency_v3",'market_urgency','market_urgency_v2'])
    
    feas_list = ['imb1_wap_mid_price', 'imb1_ask_money_bid_money', 'imb1_volume_cont_volumn_auc', 'imb1_reference_price_ask_price', 
                 'imb1_reference_price_mid_price', 'seconds_in_bucket', 'div_flag_imbalance_size_2_balance', 'ask_price', 
                 'imb1_reference_price_bid_price', 'scale_matched_size', 'imb1_near_price_wap', 'volumn_auc_money', 'imb1_far_price_wap', 
                 'bid_size', 'scale_bid_size', 'bid_size_all']
    # 隔离
    add_cols = []
    for col in ["bid_auc_money","imb1_reference_price_wap","bid_size_all",
                "imb1_auc_ask_size_auc_bid_size","div_flag_imbalance_size_2_balance",
                "imb1_ask_size_all_bid_size_all","flag_imbalance_size","imb1_reference_price_mid_price"]:
        for window in [3,6,18,36,60]:
            add_cols.append(pl.col(col).rolling_mean(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_mean_{col}'))
            add_cols.append(pl.col(col).rolling_std(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_std_{col}'))
            feas_list.extend([f'rolling{window}_mean_{col}',f'rolling{window}_std_{col}'])
    feas_list = ['imb1_wap_mid_price', 'imb1_ask_money_bid_money', 'imb1_volume_cont_volumn_auc', 
                     'imb1_reference_price_ask_price', 'imb1_reference_price_mid_price', 
                     'seconds_in_bucket', 'div_flag_imbalance_size_2_balance', 'ask_price', 
                     'imb1_reference_price_bid_price', 'scale_matched_size', 'imb1_near_price_wap', 
                     'volumn_auc_money', 'imb1_far_price_wap', 'bid_size', 'scale_bid_size', 'bid_size_all', 
                     'rolling18_mean_imb1_auc_ask_size_auc_bid_size', 'rolling3_mean_div_flag_imbalance_size_2_balance', 
                     'rolling60_std_div_flag_imbalance_size_2_balance', 'rolling36_mean_flag_imbalance_size', 
                     'rolling3_std_imb1_auc_ask_size_auc_bid_size', 'rolling18_mean_imb1_ask_size_all_bid_size_all', 
                     'rolling6_mean_div_flag_imbalance_size_2_balance', 'rolling6_std_imb1_auc_ask_size_auc_bid_size', 
                     'rolling3_mean_imb1_auc_ask_size_auc_bid_size', 'rolling60_std_imb1_auc_ask_size_auc_bid_size', 
                     'rolling6_std_bid_size_all', 'rolling3_std_bid_size_all', 'rolling3_mean_bid_size_all', 
                     'rolling18_std_bid_auc_money', 'rolling36_mean_bid_auc_money',"rolling60_mean_imb1_reference_price_wap",
                    'rolling18_mean_imb1_reference_price_wap', 'rolling3_mean_imb1_reference_price_mid_price']
    df = df.with_columns(add_cols)
    
#     for col in ["flag_imbalance_size","imb1_reference_price_wap","imb1_reference_price_mid_price","mid_price","imb1_far_price_wap",
#                'matched_size', 'reference_price', 'imbalance_buy_sell_flag']:
#         add_cols = []
#         for window_size in [1,2,4,6,12]:
#             add_cols.append(pl.col(col).shift(window_size).over('stock_id','date_id').alias(f'shift{window_size}_{col}'))
#             add_cols.append((pl.col(col) / pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'div_shift{window_size}_{col}'))
#             add_cols.append((pl.col(col) - pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'diff_shift{window_size}_{col}'))
#             feas_list.extend([f'shift{window_size}_{col}',f'div_shift{window_size}_{col}',f'diff_shift{window_size}_{col}'])
#         df = df.with_columns(add_cols)
    ### 杂七杂八
    df = df.with_columns([
        pl.col("flag_imbalance_size").diff().over('stock_id','date_id').alias("imbalance_momentum_unscaled"),
        pl.col("price_diff_ask_bid").diff().over('stock_id','date_id').alias("spread_intensity"),
    ])
    feas_list.extend(["imbalance_momentum_unscaled","spread_intensity"])
    df = df.with_columns([
        (pl.col("imbalance_momentum_unscaled")/pl.col("matched_size")).alias("imbalance_momentum")
    ])
    feas_list.extend(["imbalance_momentum"])

    #Calculate diff features for specific columns
    add_cols = []
    for col in ['ask_price',
 'bid_price',
 'imb1_reference_price_near_price',
 'bid_size',
 'scale_bid_size',
 'mid_price',
 'ask_size',
 'price_div_ask_bid',
 'div_bid_size_ask_size',
 'market_urgency',
 'wap',
 'imbalance_momentum']:
        for window in [1, 2, 3, 10]:
            add_cols.append((pl.col(col).diff(window).over('stock_id','date_id')).alias(f"{col}_diff_{window}"))
            feas_list.append(f"{col}_diff_{window}")
    df = df.with_columns(add_cols)
    
    ### target mock系列
    for mock_period in [1,3,12,6]:
    
        df = df.with_columns([
            pl.col("wap").shift(-mock_period).over("stock_id","date_id").alias(f"wap_shift_n{mock_period}")
        ])
        df = df.with_columns([
            (pl.col(f"wap_shift_n{mock_period}")/pl.col("wap")).alias("target_single")
        ])

        tmp_df = df.select(pl.col("target_single"),pl.col("weight")).to_pandas()
        tmp_df.loc[tmp_df["target_single"].isna(),"weight"] = 0
        df = df.with_columns([
            pl.lit(np.array(tmp_df["weight"])).alias("weight_tmp")
        ])

        df = df.with_columns([
            (((pl.col("weight_tmp") * pl.col("target_single")).sum().over("date_id","seconds_in_bucket")) / ((pl.col("weight_tmp")).sum().over("date_id","seconds_in_bucket"))).alias("index_target_mock")
        ])

        df = df.with_columns([
            ((pl.col("target_single") - pl.col("index_target_mock"))*10000).alias("target_mock")
        ])

        df = df.with_columns([
            pl.col("target_mock").shift(mock_period).over("stock_id","date_id").alias(f"target_mock_shift{mock_period}"),
            #pl.col("index_target_mock").shift(mock_period).over("stock_id","date_id").alias(f"index_target_mock_shift{mock_period}"),
            #pl.col("target_single").shift(mock_period).over("stock_id","date_id").alias(f"target_single_shift{mock_period}")
        ])
    # df.drop_in_place("wap_shift_6")
    # df.drop_in_place("target_single_shift6")
    # df.drop_in_place("indexwap_shift6")
    # add_cols_new = []
    add_cols = []
    for col in ['target_mock_shift6','target_mock_shift1','target_mock_shift3','target_mock_shift12']:
        for window in [1, 3,6,12,24,48]:
            add_cols.append(pl.col(col).rolling_mean(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_mean_{col}'))
            #add_cols.append(pl.col(col).rolling_std(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_std_{col}'))
            # add_cols_new.extend([f'rolling{window}_mean_{col}'])
    df = df.with_columns(add_cols)
    keep_cols_new = ['rolling48_mean_target_mock_shift3', 'rolling48_mean_target_mock_shift1', 'rolling48_mean_target_mock_shift12',
'rolling1_mean_target_mock_shift6', 'rolling24_mean_target_mock_shift6','rolling24_mean_target_mock_shift12',]
    feas_list.extend(keep_cols_new)
    
    add_cols = []
    for col in ["imb1_auc_ask_size_auc_bid_size","flag_imbalance_size","price_pressure_v2","scale_matched_size"]:
        for window_size in [1,2,3,6,12]:
            add_cols.append(pl.col(col).shift(window_size).over('stock_id','date_id').alias(f'shift{window_size}_{col}'))
            add_cols.append((pl.col(col) / pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'div_shift{window_size}_{col}'))
            add_cols.append((pl.col(col) - pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'diff_shift{window_size}_{col}'))
            #feas_list.extend([f'shift{window_size}_{col}',f'div_shift{window_size}_{col}',f'diff_shift{window_size}_{col}'])
    feas_list.extend(['div_shift6_imb1_auc_ask_size_auc_bid_size',
 'diff_shift6_price_pressure_v2',
 'shift1_price_pressure_v2',
 'div_shift3_flag_imbalance_size',
 'div_shift12_imb1_auc_ask_size_auc_bid_size',
 'div_shift3_scale_matched_size',
 'diff_shift6_flag_imbalance_size',
 'shift12_imb1_auc_ask_size_auc_bid_size',
 'div_shift12_price_pressure_v2',
 'shift6_flag_imbalance_size',
 'diff_shift3_imb1_auc_ask_size_auc_bid_size',
 'div_shift12_flag_imbalance_size',
 'shift12_flag_imbalance_size'])
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in ['imb1_ask_price_mid_price',
 'market_urgency',
 'market_urgency_diff_1',
 'imb1_ask_money_bid_money',
 'rolling18_mean_imb1_ask_size_all_bid_size_all',
 'rolling18_mean_imb1_auc_ask_size_auc_bid_size',
 'rolling18_mean_imb1_reference_price_wap',
 'ask_price_diff_3',
 'diff_shift1_price_pressure_v2',
 'diff_shift12_scale_matched_size',
 'diff_shift1_flag_imbalance_size',
 'imb1_ask_size_bid_size',
 'imb1_bid_price_mid_price',
 'rolling48_mean_target_mock_shift6']:
        add_cols.append((((pl.col(col) * pl.col("weight")).sum().over("date_id","seconds_in_bucket"))/(((pl.col("weight")).sum().over("date_id","seconds_in_bucket")))).alias(f"global_{col}"))
        feas_list.append(f"global_{col}")
    df = df.with_columns(add_cols)
    
    
    # MACD
    rsi_cols = ["mid_price_near_far","imb1_reference_price_wap","near_price",]
    add_cols = []
    for col in rsi_cols:
        for window_size in [3,6,12,24,48]:
            add_cols.append(pl.col(col).ewm_mean(span=window_size, adjust=False).over('stock_id','date_id').alias(f"rolling_ewm_{window_size}_{col}"))
            #feas_list.append(f"rolling_ewm_{window_size}_{col}")
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in rsi_cols:
        for w1,w2 in zip((3,6,12,24),(6,12,24,48)):
            add_cols.append((pl.col(f"rolling_ewm_{w1}_{col}") - pl.col(f"rolling_ewm_{w2}_{col}")).alias(f"dif_{col}_{w1}_{w2}"))
            #feas_list.append(f"dif_{col}_{w1}_{w2}")
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in rsi_cols:
        for w1,w2 in zip((3,6,12,24),(6,12,24,48)):
            add_cols.append(pl.col(f"dif_{col}_{w1}_{w2}").ewm_mean(span=9, adjust=False).over('stock_id','date_id').alias(f"dea_{col}_{w1}_{w2}"))
            #feas_list.append(f"dea_{col}_{w1}_{w2}")
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in rsi_cols:
        for w1,w2 in zip((3,6,12,24),(6,12,24,48)):
            add_cols.append((pl.col(f"dif_{col}_{w1}_{w2}") - pl.col(f"dea_{col}_{w1}_{w2}")).alias(f"macd_{col}_{w1}_{w2}"))
            #feas_list.append(f"macd_{col}_{w1}_{w2}")
    
    feas_list.extend(['macd_imb1_reference_price_wap_12_24',
 'dif_imb1_reference_price_wap_3_6',
 'macd_mid_price_near_far_12_24',
 'dif_near_price_3_6',
 'macd_near_price_24_48',
 'dea_imb1_reference_price_wap_12_24',
 'macd_near_price_12_24',
 'rolling_ewm_24_imb1_reference_price_wap',
 'dif_near_price_6_12',
 'dea_mid_price_near_far_6_12',
 'dea_near_price_24_48',
 'rolling_ewm_12_imb1_reference_price_wap',
 'dif_imb1_reference_price_wap_12_24'])
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in ["target"]:
        # 176 1,2,3,5,10,15,20,25,30
        # [1,2,3,5,10,15,20,25,30,35,40,45,60] 5.8704926 157
        # [1,2,3,5,10,15,20,30,45,60] 5.8708683137
        for window_size in [1,2,3,5,10,15,20,25,30,35,40,45,60]:
            add_cols.append(pl.col(col).shift(1).rolling_mean(window_size=window_size,min_periods=1).over('stock_id','seconds_in_bucket').alias(f'rolling_mean_{window_size}_{col}_second'))
            add_cols.append(pl.col(col).shift(1).rolling_std(window_size=window_size,min_periods=1).over('stock_id','seconds_in_bucket').alias(f'rolling_std_{window_size}_{col}_second'))

            
            feas_list.extend([f'rolling_mean_{window_size}_{col}_second',f'rolling_std_{window_size}_{col}_second',])

    df = df.with_columns(add_cols)
    
    
    return df.to_pandas(), feas_list
    
train_feas_all, feas_list = generate_features_no_hist_polars(train)
valid_feas = train_feas_all[train_feas_all['date_id'] >= 390]
train_feas = train_feas_all[train_feas_all['date_id'] < 390]
# train_feas = train_feas[train_feas['fold']==0]
print(train_feas[feas_list].shape)
(4236893, 157)
train_feas = train_feas.fillna(-9e10)
valid_feas = valid_feas.fillna(-9e10)
from tqdm.auto import tqdm
for _ in tqdm(feas_list):
    train_feas[_] = train_feas[_].clip(lower=-9e9,upper=9e9)
    valid_feas[_] = valid_feas[_].clip(lower=-9e9,upper=9e9)
  0%|          | 0/157 [00:00<?, ?it/s]
import xgboost as xgb
params = {
    'random_state': 47,
    'learning_rate':0.01,
    'n_estimators':10000,
    'n_jobs':-1,
    'objective':'reg:absoluteerror',
    "device": "gpu",
    'max_depth': 10,
     'min_child_weight': 8.860379669551103,
     'subsample': 0.7711820080525443,
     'colsample_bytree': 0.5348780216605801,
     'reg_alpha': 0.12854342791716195,
     'reg_lambda': 0.39326076062073634,
     'gamma': 0.24378704040107024
}

clf = xgb.XGBRegressor(**params)
clf.fit(train_feas[feas_list],train_feas['target'],
        eval_set = [(train_feas[feas_list],train_feas['target']),(valid_feas[feas_list],valid_feas['target'])]
        ,early_stopping_rounds=200,verbose=50)
/root/miniconda3/lib/python3.8/site-packages/xgboost/sklearn.py:885: UserWarning: `early_stopping_rounds` in `fit` method is deprecated for better compatibility with scikit-learn, use `early_stopping_rounds` in constructor or`set_params` instead.
  warnings.warn(
[0]	validation_0-mae:6.49395	validation_1-mae:6.02904
[50]	validation_0-mae:6.40492	validation_1-mae:5.96984
[100]	validation_0-mae:6.35422	validation_1-mae:5.94013
[150]	validation_0-mae:6.32137	validation_1-mae:5.92436
[200]	validation_0-mae:6.29595	validation_1-mae:5.91413
[250]	validation_0-mae:6.27467	validation_1-mae:5.90719
[300]	validation_0-mae:6.25589	validation_1-mae:5.90207
[350]	validation_0-mae:6.23819	validation_1-mae:5.89805
[400]	validation_0-mae:6.22189	validation_1-mae:5.89464
[450]	validation_0-mae:6.20645	validation_1-mae:5.89172
[500]	validation_0-mae:6.19216	validation_1-mae:5.88921
[550]	validation_0-mae:6.17877	validation_1-mae:5.88721
[600]	validation_0-mae:6.16588	validation_1-mae:5.88534
[650]	validation_0-mae:6.15346	validation_1-mae:5.88371
[700]	validation_0-mae:6.14171	validation_1-mae:5.88235
[750]	validation_0-mae:6.13032	validation_1-mae:5.88123
[800]	validation_0-mae:6.11943	validation_1-mae:5.88020
[850]	validation_0-mae:6.10866	validation_1-mae:5.87918
[900]	validation_0-mae:6.09885	validation_1-mae:5.87837
[950]	validation_0-mae:6.08899	validation_1-mae:5.87762
[1000]	validation_0-mae:6.07938	validation_1-mae:5.87698
[1050]	validation_0-mae:6.07031	validation_1-mae:5.87648
[1100]	validation_0-mae:6.06142	validation_1-mae:5.87587
[1150]	validation_0-mae:6.05292	validation_1-mae:5.87543
[1200]	validation_0-mae:6.04393	validation_1-mae:5.87503
[1250]	validation_0-mae:6.03549	validation_1-mae:5.87461
[1300]	validation_0-mae:6.02722	validation_1-mae:5.87432
[1350]	validation_0-mae:6.01892	validation_1-mae:5.87389
[1400]	validation_0-mae:6.01058	validation_1-mae:5.87362
[1450]	validation_0-mae:6.00317	validation_1-mae:5.87337
[1500]	validation_0-mae:5.99542	validation_1-mae:5.87315
[1550]	validation_0-mae:5.98846	validation_1-mae:5.87297
[1600]	validation_0-mae:5.98126	validation_1-mae:5.87271
[1650]	validation_0-mae:5.97419	validation_1-mae:5.87251
[1700]	validation_0-mae:5.96741	validation_1-mae:5.87233
[1750]	validation_0-mae:5.96031	validation_1-mae:5.87217
[1800]	validation_0-mae:5.95329	validation_1-mae:5.87207
[1850]	validation_0-mae:5.94654	validation_1-mae:5.87198
[1900]	validation_0-mae:5.94003	validation_1-mae:5.87181
[1950]	validation_0-mae:5.93343	validation_1-mae:5.87166
[2000]	validation_0-mae:5.92681	validation_1-mae:5.87148
[2050]	validation_0-mae:5.92071	validation_1-mae:5.87144
[2100]	validation_0-mae:5.91447	validation_1-mae:5.87131
[2150]	validation_0-mae:5.90812	validation_1-mae:5.87121
[2200]	validation_0-mae:5.90209	validation_1-mae:5.87112
[2250]	validation_0-mae:5.89619	validation_1-mae:5.87106
[2300]	validation_0-mae:5.89040	validation_1-mae:5.87106
[2350]	validation_0-mae:5.88448	validation_1-mae:5.87098
[2400]	validation_0-mae:5.87831	validation_1-mae:5.87094
[2450]	validation_0-mae:5.87240	validation_1-mae:5.87091
[2500]	validation_0-mae:5.86662	validation_1-mae:5.87093
[2550]	validation_0-mae:5.86109	validation_1-mae:5.87078
[2600]	validation_0-mae:5.85560	validation_1-mae:5.87071
[2650]	validation_0-mae:5.85027	validation_1-mae:5.87072
[2700]	validation_0-mae:5.84471	validation_1-mae:5.87069
[2750]	validation_0-mae:5.83912	validation_1-mae:5.87068
[2800]	validation_0-mae:5.83371	validation_1-mae:5.87067
[2850]	validation_0-mae:5.82878	validation_1-mae:5.87063
[2900]	validation_0-mae:5.82341	validation_1-mae:5.87057
[2950]	validation_0-mae:5.81809	validation_1-mae:5.87052
[3000]	validation_0-mae:5.81296	validation_1-mae:5.87052
[3050]	validation_0-mae:5.80785	validation_1-mae:5.87053
[3100]	validation_0-mae:5.80248	validation_1-mae:5.87052
[3150]	validation_0-mae:5.79737	validation_1-mae:5.87054
[3178]	validation_0-mae:5.79444	validation_1-mae:5.87054
XGBRegressor(base_score=None, booster=None, callbacks=None,
             colsample_bylevel=None, colsample_bynode=None,
             colsample_bytree=0.5348780216605801, device='gpu',
             early_stopping_rounds=None, enable_categorical=False,
             eval_metric=None, feature_types=None, gamma=0.24378704040107024,
             grow_policy=None, importance_type=None,
             interaction_constraints=None, learning_rate=0.01, max_bin=None,
             max_cat_threshold=None, max_cat_to_onehot=None,
             max_delta_step=None, max_depth=10, max_leaves=None,
             min_child_weight=8.860379669551103, missing=nan,
             monotone_constraints=None, multi_strategy=None, n_estimators=10000,
             n_jobs=-1, num_parallel_tree=None, objective='reg:absoluteerror', ...)
In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook.
On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.
clf.best_iteration
2978
clf.best_score
5.870492650169636
clf.save_model("../save/xgboost_157.pkl")
pred_cat = clf.predict(valid_feas[feas_list])
valid_feas["pred"] = pred_cat
valid_feas[['stock_id','date_id','seconds_in_bucket','target','pred']].to_pickle("../save/xgboost_157_oof.pkl")
/root/miniconda3/lib/python3.8/site-packages/xgboost/core.py:160: UserWarning: [18:19:06] WARNING: /workspace/src/c_api/c_api.cc:1240: Saving into deprecated binary model format, please consider using `json` or `ubj`. Model format will default to JSON in XGBoost 2.2 if not specified.
  warnings.warn(smsg, UserWarning)
/root/miniconda3/lib/python3.8/site-packages/xgboost/core.py:160: UserWarning: [18:19:07] WARNING: /workspace/src/common/error_msg.cc:58: Falling back to prediction using DMatrix due to mismatched devices. This might lead to higher memory usage and slower performance. XGBoost is running on: cuda:0, while the input data is on: cpu.
Potential solutions:
- Use a data structure that matches the device ordinal in the booster.
- Set the device for booster before call to inplace_predict.

This warning will only be shown once.

  warnings.warn(smsg, UserWarning)
/tmp/ipykernel_41748/3251720126.py:3: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`
  valid_feas["pred"] = pred_cat
full data 5 folds
import pandas as pd
import numpy as np
import lightgbm as lgb
from itertools import combinations
import polars as pl
import xgboost as xgb
pl.__version__
train = pd.read_pickle("../raw_data/train.pkl")
train = train[~train['target'].isna()]
print(train.shape)

size_col = ['imbalance_size','matched_size','bid_size','ask_size']
for _ in size_col:
    train[f"scale_{_}"] = train[_] / train.groupby(['stock_id'])[_].transform('median')
    
#buy-side imbalance; 1
#sell-side imbalance; -1
#no imbalance; 0
train['auc_bid_size'] = train['matched_size']
train['auc_ask_size'] = train['matched_size']
train.loc[train['imbalance_buy_sell_flag']==1,'auc_bid_size'] += train.loc[train['imbalance_buy_sell_flag']==1,'imbalance_size']
train.loc[train['imbalance_buy_sell_flag']==-1,'auc_ask_size'] += train.loc[train['imbalance_buy_sell_flag']==-1,'imbalance_size']


weight_df = pd.DataFrame()
weight_df['stock_id'] = list(range(200))
weight_df['weight'] =  [
    0.004, 0.001, 0.002, 0.006, 0.004, 0.004, 0.002, 0.006, 0.006, 0.002, 0.002, 0.008,
    0.006, 0.002, 0.008, 0.006, 0.002, 0.006, 0.004, 0.002, 0.004, 0.001, 0.006, 0.004,
    0.002, 0.002, 0.004, 0.002, 0.004, 0.004, 0.001, 0.001, 0.002, 0.002, 0.006, 0.004,
    0.004, 0.004, 0.006, 0.002, 0.002, 0.04 , 0.002, 0.002, 0.004, 0.04 , 0.002, 0.001,
    0.006, 0.004, 0.004, 0.006, 0.001, 0.004, 0.004, 0.002, 0.006, 0.004, 0.006, 0.004,
    0.006, 0.004, 0.002, 0.001, 0.002, 0.004, 0.002, 0.008, 0.004, 0.004, 0.002, 0.004,
    0.006, 0.002, 0.004, 0.004, 0.002, 0.004, 0.004, 0.004, 0.001, 0.002, 0.002, 0.008,
    0.02 , 0.004, 0.006, 0.002, 0.02 , 0.002, 0.002, 0.006, 0.004, 0.002, 0.001, 0.02,
    0.006, 0.001, 0.002, 0.004, 0.001, 0.002, 0.006, 0.006, 0.004, 0.006, 0.001, 0.002,
    0.004, 0.006, 0.006, 0.001, 0.04 , 0.006, 0.002, 0.004, 0.002, 0.002, 0.006, 0.002,
    0.002, 0.004, 0.006, 0.006, 0.002, 0.002, 0.008, 0.006, 0.004, 0.002, 0.006, 0.002,
    0.004, 0.006, 0.002, 0.004, 0.001, 0.004, 0.002, 0.004, 0.008, 0.006, 0.008, 0.002,
    0.004, 0.002, 0.001, 0.004, 0.004, 0.004, 0.006, 0.008, 0.004, 0.001, 0.001, 0.002,
    0.006, 0.004, 0.001, 0.002, 0.006, 0.004, 0.006, 0.008, 0.002, 0.002, 0.004, 0.002,
    0.04 , 0.002, 0.002, 0.004, 0.002, 0.002, 0.006, 0.02 , 0.004, 0.002, 0.006, 0.02,
    0.001, 0.002, 0.006, 0.004, 0.006, 0.004, 0.004, 0.004, 0.004, 0.002, 0.004, 0.04,
    0.002, 0.008, 0.002, 0.004, 0.001, 0.004, 0.006, 0.004,
]
train = train.merge(weight_df,how='left',on=['stock_id'])
(5237892, 18)
def generate_features_no_hist_polars(df):
    # 加一个ask_size - bid_size的特征 然后Rolling
    df = pl.from_pandas(df)
    feas_list = ['stock_id','seconds_in_bucket','imbalance_size','imbalance_buy_sell_flag',
               'reference_price','matched_size','far_price','near_price','bid_price','bid_size',
                'ask_price','ask_size','wap','scale_imbalance_size','scale_matched_size','scale_bid_size','scale_ask_size'
                 ,'auc_bid_size','auc_ask_size']
    # 基础特征
    df = df.with_columns([
        # 阶段1
        (pl.col('ask_size') * pl.col('ask_price')).alias("ask_money"),
        (pl.col('bid_size') * pl.col('bid_price')).alias("bid_money"),
        (pl.col('ask_size') + pl.col("auc_ask_size")).alias("ask_size_all"),
        (pl.col('bid_size') + pl.col("auc_bid_size")).alias("bid_size_all"),
        (pl.col('ask_size') + pl.col("auc_ask_size") + pl.col('bid_size') + pl.col("auc_bid_size")).alias("volumn_size_all"),
        (pl.col('reference_price') * pl.col('auc_ask_size')).alias("ask_auc_money"),
        (pl.col('reference_price') * pl.col('auc_bid_size')).alias("bid_auc_money"),
        (pl.col('ask_size') * pl.col('ask_price') + pl.col('bid_size') * pl.col('bid_price')).alias("volumn_money"),
        (pl.col('ask_size') + pl.col('bid_size')).alias('volume_cont'),
        (pl.col('ask_size') - pl.col('bid_size')).alias('diff_ask_bid_size'),
        (pl.col('imbalance_size') + 2 * pl.col('matched_size')).alias('volumn_auc'),
        ((pl.col('imbalance_size') + 2 * pl.col('matched_size')) * pl.col("reference_price")).alias('volumn_auc_money'),
        ((pl.col('ask_price') + pl.col('bid_price'))/2).alias('mid_price'),
        ((pl.col('near_price') + pl.col('far_price'))/2).alias('mid_price_near_far'),
        (pl.col('ask_price') - pl.col('bid_price')).alias('price_diff_ask_bid'),
        (pl.col('ask_price') / pl.col('bid_price')).alias('price_div_ask_bid'),
        (pl.col('imbalance_buy_sell_flag') * pl.col('scale_imbalance_size')).alias('flag_scale_imbalance_size'),
        (pl.col('imbalance_buy_sell_flag') * pl.col('imbalance_size')).alias('flag_imbalance_size'),
        (pl.col('imbalance_size') / pl.col('matched_size') * pl.col('imbalance_buy_sell_flag')).alias("div_flag_imbalance_size_2_balance"),
        ((pl.col('ask_price') - pl.col('bid_price')) * pl.col('imbalance_size')).alias('price_pressure'),
        ((pl.col('ask_price') - pl.col('bid_price')) * pl.col('imbalance_size') * pl.col('imbalance_buy_sell_flag')).alias('price_pressure_v2'),
        ((pl.col("ask_size") - pl.col("bid_size")) / (pl.col("far_price") - pl.col("near_price"))).alias("depth_pressure"),
        (pl.col("bid_size") / pl.col("ask_size")).alias("div_bid_size_ask_size"),
    ])
    feas_list.extend(['ask_money', 'bid_money', 'ask_auc_money','bid_auc_money',"ask_size_all","bid_size_all","volumn_size_all",
                      'volumn_money','volume_cont',"volumn_auc","volumn_auc_money","mid_price",
                      'mid_price_near_far','price_diff_ask_bid',"price_div_ask_bid","flag_imbalance_size","div_flag_imbalance_size_2_balance",
                     "price_pressure","price_pressure_v2","depth_pressure","flag_scale_imbalance_size","diff_ask_bid_size"])        

    # 各种ratio
    # 提升微忽几微
    add_cols = []
    for col1, col2 in [
        ("imbalance_size","bid_size"),
        ("imbalance_size","ask_size"),
        ("matched_size","bid_size"),
        ("matched_size","ask_size"),
        ("imbalance_size","volume_cont"),
        ("matched_size","volume_cont"),
        ("auc_bid_size","bid_size"),
        ("auc_ask_size","ask_size"),
        ("bid_auc_money","bid_money"),
        ("ask_auc_money","ask_money"),
    ]:
        add_cols.append((pl.col(col1) / pl.col(col2)).alias(f"div_{col1}_2_{col2}"))
        feas_list.append(f"div_{col1}_2_{col2}")        
    df = df.with_columns(add_cols)

    # 阶段2 不平衡特征
    # 除了price相关
    # 没加auc的ask/bid的 构造price以及不平衡进去
    add_cols = []
    for pair1,pair2 in [
        ('ask_size','bid_size'),
        ('ask_money','bid_money'),
        ('volumn_money','volumn_auc_money'),
        ('volume_cont','volumn_auc'),
        ('imbalance_size','matched_size'),
        ('auc_ask_size','auc_bid_size'),
        ("ask_size_all",'bid_size_all')
    ]:
        col_imb = f"imb1_{pair1}_{pair2}"
        add_cols.extend([
            ((pl.col(pair1) - pl.col(pair2)) / (pl.col(pair1) + pl.col(pair2))).alias(col_imb),
        ])
        feas_list.extend([col_imb])
    df = df.with_columns(add_cols)
    
    # price侧的imb1
    fea_append_list = []
    prices = ["reference_price", "far_price", "near_price", "ask_price", "bid_price", "wap","mid_price"]
    for c in combinations(prices, 2):
        fea_append_list.append(((pl.col(c[0]) - pl.col(c[1])) / (pl.col(c[0]) + pl.col(c[1]))).alias(f"imb1_{c[0]}_{c[1]}"))
        # fea_append_list.append((pl.col(c[0]) - pl.col(c[1])).alias(f"diff_{c[0]}_{c[1]}"))
        feas_list.extend([f"imb1_{c[0]}_{c[1]}"])
    df = df.with_columns(fea_append_list)
    
    
    # 不平衡特征 累计乘
    df = df.with_columns([
        ((pl.col("imb1_ask_size_bid_size") + 2) * (pl.col("imb1_ask_price_bid_price") + 2) * (pl.col("imb1_auc_ask_size_auc_bid_size")+2)).alias("market_urgency_v2"),
        (pl.col('price_diff_ask_bid') * (pl.col('imb1_ask_size_bid_size'))).alias('market_urgency'),
        (pl.col('imb1_ask_price_bid_price') * (pl.col('imb1_ask_size_bid_size'))).alias('market_urgency_v3'),
    ])
    feas_list.extend([f"market_urgency_v3",'market_urgency','market_urgency_v2'])
    
    feas_list = ['imb1_wap_mid_price', 'imb1_ask_money_bid_money', 'imb1_volume_cont_volumn_auc', 'imb1_reference_price_ask_price', 
                 'imb1_reference_price_mid_price', 'seconds_in_bucket', 'div_flag_imbalance_size_2_balance', 'ask_price', 
                 'imb1_reference_price_bid_price', 'scale_matched_size', 'imb1_near_price_wap', 'volumn_auc_money', 'imb1_far_price_wap', 
                 'bid_size', 'scale_bid_size', 'bid_size_all']
    # 隔离
    add_cols = []
    for col in ["bid_auc_money","imb1_reference_price_wap","bid_size_all",
                "imb1_auc_ask_size_auc_bid_size","div_flag_imbalance_size_2_balance",
                "imb1_ask_size_all_bid_size_all","flag_imbalance_size","imb1_reference_price_mid_price"]:
        for window in [3,6,18,36,60]:
            add_cols.append(pl.col(col).rolling_mean(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_mean_{col}'))
            add_cols.append(pl.col(col).rolling_std(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_std_{col}'))
            feas_list.extend([f'rolling{window}_mean_{col}',f'rolling{window}_std_{col}'])
    feas_list = ['imb1_wap_mid_price', 'imb1_ask_money_bid_money', 'imb1_volume_cont_volumn_auc', 
                     'imb1_reference_price_ask_price', 'imb1_reference_price_mid_price', 
                     'seconds_in_bucket', 'div_flag_imbalance_size_2_balance', 'ask_price', 
                     'imb1_reference_price_bid_price', 'scale_matched_size', 'imb1_near_price_wap', 
                     'volumn_auc_money', 'imb1_far_price_wap', 'bid_size', 'scale_bid_size', 'bid_size_all', 
                     'rolling18_mean_imb1_auc_ask_size_auc_bid_size', 'rolling3_mean_div_flag_imbalance_size_2_balance', 
                     'rolling60_std_div_flag_imbalance_size_2_balance', 'rolling36_mean_flag_imbalance_size', 
                     'rolling3_std_imb1_auc_ask_size_auc_bid_size', 'rolling18_mean_imb1_ask_size_all_bid_size_all', 
                     'rolling6_mean_div_flag_imbalance_size_2_balance', 'rolling6_std_imb1_auc_ask_size_auc_bid_size', 
                     'rolling3_mean_imb1_auc_ask_size_auc_bid_size', 'rolling60_std_imb1_auc_ask_size_auc_bid_size', 
                     'rolling6_std_bid_size_all', 'rolling3_std_bid_size_all', 'rolling3_mean_bid_size_all', 
                     'rolling18_std_bid_auc_money', 'rolling36_mean_bid_auc_money',"rolling60_mean_imb1_reference_price_wap",
                    'rolling18_mean_imb1_reference_price_wap', 'rolling3_mean_imb1_reference_price_mid_price']
    df = df.with_columns(add_cols)
    
#     for col in ["flag_imbalance_size","imb1_reference_price_wap","imb1_reference_price_mid_price","mid_price","imb1_far_price_wap",
#                'matched_size', 'reference_price', 'imbalance_buy_sell_flag']:
#         add_cols = []
#         for window_size in [1,2,4,6,12]:
#             add_cols.append(pl.col(col).shift(window_size).over('stock_id','date_id').alias(f'shift{window_size}_{col}'))
#             add_cols.append((pl.col(col) / pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'div_shift{window_size}_{col}'))
#             add_cols.append((pl.col(col) - pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'diff_shift{window_size}_{col}'))
#             feas_list.extend([f'shift{window_size}_{col}',f'div_shift{window_size}_{col}',f'diff_shift{window_size}_{col}'])
#         df = df.with_columns(add_cols)
    ### 杂七杂八
    df = df.with_columns([
        pl.col("flag_imbalance_size").diff().over('stock_id','date_id').alias("imbalance_momentum_unscaled"),
        pl.col("price_diff_ask_bid").diff().over('stock_id','date_id').alias("spread_intensity"),
    ])
    feas_list.extend(["imbalance_momentum_unscaled","spread_intensity"])
    df = df.with_columns([
        (pl.col("imbalance_momentum_unscaled")/pl.col("matched_size")).alias("imbalance_momentum")
    ])
    feas_list.extend(["imbalance_momentum"])

    #Calculate diff features for specific columns
    add_cols = []
    for col in ['ask_price',
 'bid_price',
 'imb1_reference_price_near_price',
 'bid_size',
 'scale_bid_size',
 'mid_price',
 'ask_size',
 'price_div_ask_bid',
 'div_bid_size_ask_size',
 'market_urgency',
 'wap',
 'imbalance_momentum']:
        for window in [1, 2, 3, 10]:
            add_cols.append((pl.col(col).diff(window).over('stock_id','date_id')).alias(f"{col}_diff_{window}"))
            feas_list.append(f"{col}_diff_{window}")
    df = df.with_columns(add_cols)
    
    ### target mock系列
    for mock_period in [1,3,12,6]:
    
        df = df.with_columns([
            pl.col("wap").shift(-mock_period).over("stock_id","date_id").alias(f"wap_shift_n{mock_period}")
        ])
        df = df.with_columns([
            (pl.col(f"wap_shift_n{mock_period}")/pl.col("wap")).alias("target_single")
        ])

        tmp_df = df.select(pl.col("target_single"),pl.col("weight")).to_pandas()
        tmp_df.loc[tmp_df["target_single"].isna(),"weight"] = 0
        df = df.with_columns([
            pl.lit(np.array(tmp_df["weight"])).alias("weight_tmp")
        ])

        df = df.with_columns([
            (((pl.col("weight_tmp") * pl.col("target_single")).sum().over("date_id","seconds_in_bucket")) / ((pl.col("weight_tmp")).sum().over("date_id","seconds_in_bucket"))).alias("index_target_mock")
        ])

        df = df.with_columns([
            ((pl.col("target_single") - pl.col("index_target_mock"))*10000).alias("target_mock")
        ])

        df = df.with_columns([
            pl.col("target_mock").shift(mock_period).over("stock_id","date_id").alias(f"target_mock_shift{mock_period}"),
            #pl.col("index_target_mock").shift(mock_period).over("stock_id","date_id").alias(f"index_target_mock_shift{mock_period}"),
            #pl.col("target_single").shift(mock_period).over("stock_id","date_id").alias(f"target_single_shift{mock_period}")
        ])
    # df.drop_in_place("wap_shift_6")
    # df.drop_in_place("target_single_shift6")
    # df.drop_in_place("indexwap_shift6")
    # add_cols_new = []
    add_cols = []
    for col in ['target_mock_shift6','target_mock_shift1','target_mock_shift3','target_mock_shift12']:
        for window in [1, 3,6,12,24,48]:
            add_cols.append(pl.col(col).rolling_mean(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_mean_{col}'))
            #add_cols.append(pl.col(col).rolling_std(window_size=window,min_periods=1).over('stock_id','date_id').alias(f'rolling{window}_std_{col}'))
            # add_cols_new.extend([f'rolling{window}_mean_{col}'])
    df = df.with_columns(add_cols)
    keep_cols_new = ['rolling48_mean_target_mock_shift3', 'rolling48_mean_target_mock_shift1', 'rolling48_mean_target_mock_shift12',
'rolling1_mean_target_mock_shift6', 'rolling24_mean_target_mock_shift6','rolling24_mean_target_mock_shift12',]
    feas_list.extend(keep_cols_new)
    
    add_cols = []
    for col in ["imb1_auc_ask_size_auc_bid_size","flag_imbalance_size","price_pressure_v2","scale_matched_size"]:
        for window_size in [1,2,3,6,12]:
            add_cols.append(pl.col(col).shift(window_size).over('stock_id','date_id').alias(f'shift{window_size}_{col}'))
            add_cols.append((pl.col(col) / pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'div_shift{window_size}_{col}'))
            add_cols.append((pl.col(col) - pl.col(col).shift(window_size).over('stock_id','date_id')).alias(f'diff_shift{window_size}_{col}'))
            #feas_list.extend([f'shift{window_size}_{col}',f'div_shift{window_size}_{col}',f'diff_shift{window_size}_{col}'])
    feas_list.extend(['div_shift6_imb1_auc_ask_size_auc_bid_size',
 'diff_shift6_price_pressure_v2',
 'shift1_price_pressure_v2',
 'div_shift3_flag_imbalance_size',
 'div_shift12_imb1_auc_ask_size_auc_bid_size',
 'div_shift3_scale_matched_size',
 'diff_shift6_flag_imbalance_size',
 'shift12_imb1_auc_ask_size_auc_bid_size',
 'div_shift12_price_pressure_v2',
 'shift6_flag_imbalance_size',
 'diff_shift3_imb1_auc_ask_size_auc_bid_size',
 'div_shift12_flag_imbalance_size',
 'shift12_flag_imbalance_size'])
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in ['imb1_ask_price_mid_price',
 'market_urgency',
 'market_urgency_diff_1',
 'imb1_ask_money_bid_money',
 'rolling18_mean_imb1_ask_size_all_bid_size_all',
 'rolling18_mean_imb1_auc_ask_size_auc_bid_size',
 'rolling18_mean_imb1_reference_price_wap',
 'ask_price_diff_3',
 'diff_shift1_price_pressure_v2',
 'diff_shift12_scale_matched_size',
 'diff_shift1_flag_imbalance_size',
 'imb1_ask_size_bid_size',
 'imb1_bid_price_mid_price',
 'rolling48_mean_target_mock_shift6']:
        add_cols.append((((pl.col(col) * pl.col("weight")).sum().over("date_id","seconds_in_bucket"))/(((pl.col("weight")).sum().over("date_id","seconds_in_bucket")))).alias(f"global_{col}"))
        feas_list.append(f"global_{col}")
    df = df.with_columns(add_cols)
    
    
    # MACD
    rsi_cols = ["mid_price_near_far","imb1_reference_price_wap","near_price",]
    add_cols = []
    for col in rsi_cols:
        for window_size in [3,6,12,24,48]:
            add_cols.append(pl.col(col).ewm_mean(span=window_size, adjust=False).over('stock_id','date_id').alias(f"rolling_ewm_{window_size}_{col}"))
            #feas_list.append(f"rolling_ewm_{window_size}_{col}")
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in rsi_cols:
        for w1,w2 in zip((3,6,12,24),(6,12,24,48)):
            add_cols.append((pl.col(f"rolling_ewm_{w1}_{col}") - pl.col(f"rolling_ewm_{w2}_{col}")).alias(f"dif_{col}_{w1}_{w2}"))
            #feas_list.append(f"dif_{col}_{w1}_{w2}")
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in rsi_cols:
        for w1,w2 in zip((3,6,12,24),(6,12,24,48)):
            add_cols.append(pl.col(f"dif_{col}_{w1}_{w2}").ewm_mean(span=9, adjust=False).over('stock_id','date_id').alias(f"dea_{col}_{w1}_{w2}"))
            #feas_list.append(f"dea_{col}_{w1}_{w2}")
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in rsi_cols:
        for w1,w2 in zip((3,6,12,24),(6,12,24,48)):
            add_cols.append((pl.col(f"dif_{col}_{w1}_{w2}") - pl.col(f"dea_{col}_{w1}_{w2}")).alias(f"macd_{col}_{w1}_{w2}"))
            #feas_list.append(f"macd_{col}_{w1}_{w2}")
    
    feas_list.extend(['macd_imb1_reference_price_wap_12_24',
 'dif_imb1_reference_price_wap_3_6',
 'macd_mid_price_near_far_12_24',
 'dif_near_price_3_6',
 'macd_near_price_24_48',
 'dea_imb1_reference_price_wap_12_24',
 'macd_near_price_12_24',
 'rolling_ewm_24_imb1_reference_price_wap',
 'dif_near_price_6_12',
 'dea_mid_price_near_far_6_12',
 'dea_near_price_24_48',
 'rolling_ewm_12_imb1_reference_price_wap',
 'dif_imb1_reference_price_wap_12_24'])
    df = df.with_columns(add_cols)
    
    add_cols = []
    for col in ["target"]:
        # 176 1,2,3,5,10,15,20,25,30
        # [1,2,3,5,10,15,20,25,30,35,40,45,60] 5.8704926 157
        # [1,2,3,5,10,15,20,30,45,60] 5.8708683137
        for window_size in [1,2,3,5,10,15,20,25,30,35,40,45,60]:
            add_cols.append(pl.col(col).shift(1).rolling_mean(window_size=window_size,min_periods=1).over('stock_id','seconds_in_bucket').alias(f'rolling_mean_{window_size}_{col}_second'))
            add_cols.append(pl.col(col).shift(1).rolling_std(window_size=window_size,min_periods=1).over('stock_id','seconds_in_bucket').alias(f'rolling_std_{window_size}_{col}_second'))

            
            feas_list.extend([f'rolling_mean_{window_size}_{col}_second',f'rolling_std_{window_size}_{col}_second',])

    df = df.with_columns(add_cols)
    
    
    return df.to_pandas(), feas_list
    
train_feas, feas_list = generate_features_no_hist_polars(train)
print(train_feas.shape)
print(len(feas_list))
feas_dict = {}
feas_dict['selected_feas'] = feas_list
(5237892, 407)
157
train_feas = train_feas.fillna(-9e10)
#valid_feas = valid_feas.fillna(-9e10)
from tqdm.auto import tqdm
for _ in tqdm(feas_list):
    train_feas[_] = train_feas[_].clip(lower=-9e9,upper=9e9)
    #valid_feas[_] = valid_feas[_].clip(lower=-9e9,upper=9e9)
  0%|          | 0/157 [00:00<?, ?it/s]
from sklearn.model_selection import KFold
import json
with open("../save/xgb3_feas_v7_157.json",'w') as f:
    json.dump(feas_dict,f)
kf = KFold(n_splits=5,shuffle=True,random_state=47)
k = 0
for train_index,test_index in kf.split(train_feas):
    k+=1
    print(f'{k}folds begins******************************')
    params = {
        'random_state': 47,
        'learning_rate':0.01,
        'n_estimators':2978,
        'n_jobs':-1,
        'objective':'reg:absoluteerror',
        "device": "gpu",
        'max_depth': 10,
         'min_child_weight': 8.860379669551103,
         'subsample': 0.7711820080525443,
         'colsample_bytree': 0.5348780216605801,
         'reg_alpha': 0.12854342791716195,
         'reg_lambda': 0.39326076062073634,
         'gamma': 0.24378704040107024
    }
    date_ids = np.array(train_feas.iloc[train_index,:]["date_id"])
    weights_date = np.ones_like(date_ids).astype(float)
    weights_date[date_ids>=435] = 1.5
    
    clf = xgb.XGBRegressor(**params)
    clf.fit(train_feas.iloc[train_index,:][feas_list],train_feas.iloc[train_index,:]['target'],
            eval_set = [(train_feas.iloc[train_index,:][feas_list],train_feas.iloc[train_index,:]['target'])]
            ,verbose=50,sample_weight=weights_date)
    clf.save_model(f"../save/xgb3_v7_k{k}_weight15_debug.json")