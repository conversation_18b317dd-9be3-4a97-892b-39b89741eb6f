6th Place Solution
Thank you to <PERSON><PERSON><PERSON> and <PERSON><PERSON> for hosting this competition. This time I wanted to gain hands-on experience applying deep learning to trading and time series data, and didn't focus on extensive feature engineering or boosting trees based models.

Data Preprocessing: Zero imputation to handle missing values, and standard scaling to normalize the features.

Feature Engineering: Total of 35-36 features for the models, which included the raw input features, binary flags for the 'seconds_in_bucket' variable, and additional features borrowed from public notebooks such as volume, mid price, and various imbalance measures (liquidity, matched, size, pairwise, harmonic).

Modeling Approach:

Sequence-to-sequence transformers (3 slightly varying models): dimensionality 64, encoder with 2 days of historical data, decoder with 4 stacked transformers layers, and head with simple linear output layer. Both encoder and decoder with stock-wise (attention) layers.
GRU (1 model): similar seq-2-seq architecture (decoder-only), dimensionality 128, decoder with 2 GRU layers, and head consisting 2 fully-connected layers. Both decoder and head with stock-wise (attention) layers.
All models produced outputs of shape (batch_size, number_stocks, 55). To leverage the time series nature of the competition with revealed targets, online incremental learning was performed, where the models were updated each day using only the newly unseen data (for the decoder).

Validation Strategy: Simple time-based split was used for validation, with the first 359 days used for training and the last 121 days used for validation. Because the evaluation metric was unstable after each training epoch, exponential moving average was used to smooth the values and compare models. To assess online incremental learning, models were validated with the latest 20 days of data.

Postprocessing: All models except one were trained with an additional constraint to enforce that the sum of the model outputs is zero.

Ensembling: The final ensemble consisted of an average of predictions from the 3 transformer models and 1 GRU model.

Final Results: Final submission placed 6th on the private leaderboard with a mean absolute error (MAE) of 5.4285.