10th Place Solution: Geometric Brownian Motion and Mixture Density Networks
Given that the our solution is still number 2 after the second rerun, we have slightly more confidence to write a post about the methodology. we also use regression which is, I think, unique in this competition.

Key Points of the Method:

Geometric Brownian Motion with Drift Fitting
Mixture Density Network with negative log likelihood loss function
These two are the only significant key differences I have seen between our solution and the other posted notebooks.

Geometric Brownian Motion with Drift Fitting:

One of the most basic models used when modeling financial assets is geometric brownian motion with drift. Assuming that our data here roughly follows such a distribution, sampling the value of the price at a given time gives us very little information on this financial asset because of the stochastic nature of the process. The drift in price however is much more fundamental to the particular stock than the return at any particular time so this is a great feature to engineer. We use this as a target feature when training the neural network.

The following code computes the drift factor for each asset in the training through MLE on resp_1 to resp_4. This notebook really influenced the code: https://www.kaggle.com/pcarta/jane-street-time-horizons-and-volatilities
Here is my code: I am happy to answer questions in comments but the above notebook really explains everything, all I did was use a more complex model (with drift) and do the same maximum likelihood estimation, basically converting math to pytorch.

import torch
from tqdm import tqdm
mus = None
sigmas = None
horizons = None

def train_gbm():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    dT_init = np.ones(len(columns)-1).astype(float)
    sigma_init = np.random.randn(len(resps))*.01 + 1
    mu_param = torch.tensor(np.random.randn(len(resps))*.01, device=device).requires_grad_(True)
    with torch.no_grad():
        dT_param_log = torch.log(torch.tensor(dT_init)).to(device).requires_grad_(True)
        sigma_param_log = torch.log(torch.tensor(sigma_init)).to(device).requires_grad_(True)
    W = torch.tensor(resps.values + 1, device=device).float()
    W_init = torch.cat((torch.ones((W.size(0),1)).to(device), W[:,:-1]), dim = 1)
    opt = torch.optim.Adam([dT_param_log, sigma_param_log, mu_param], lr = 0.001)
    iteration = 0
    for iteration in tqdm(range(15000)):
        opt.zero_grad()
        # add the fixed 1 at the beginning of the time deltas
        dT_log = torch.cat([torch.zeros(1, device=device) , dT_param_log])
        dT = torch.exp(dT_log).view((1,-1))
        sigmas = torch.exp(sigma_param_log).view((-1,1))
        mus = mu_param.view((-1,1))
        ln_W = torch.log(W)
        ln_W_init = torch.log(W_init)
        log_prob = -ln_W - torch.log(sigmas) - 1/2 * torch.log(dT) - (ln_W - ln_W_init - (mus - 0.5*sigmas**2)@dT)**2/(2*sigmas**2 @ dT)
        row_liklihood = log_prob.sum(dim = 1)
        total_liklihood = row_liklihood.mean(dim = 0)
        negative_total_liklihood = -total_liklihood
        if(iteration % 1000 == 0):
            print(negative_total_liklihood)
        negative_total_liklihood.backward()
        opt.step()
    times = torch.exp(torch.cat([torch.zeros(1, device=device) , dT_param_log])).cumsum(dim = 0)
    mus = mu_param.detach().cpu().numpy()
    sigmas = torch.exp(sigma_param_log).detach().cpu().numpy()
    horizons = times.detach().cpu().numpy()
    return mus, sigmas, horizons

mus, sigmas, horizons = train_gbm()
import scipy
from scipy import special
expected_return = np.exp(mus * horizons[3]) - 1
lam = sigmas * np.sqrt(horizons[3])
row = (mus - 0.5*sigmas**2)*horizons[3]
probability_greater_zero = 0.5 - 0.5 * special.erf(-row/(np.sqrt(2)*lam))
This gives me the expected value of resp assuming that the asset follows a geometric brownian motion distribution as well as the drift parameter and the probability that resp is greater than 0. All of which could be used to fine tune the training of the network. I played around with all three as targets and also thought of label smoothing techniques for binary classification using the sigma parameter.

Mixture Density Network:
A mixture density network is just a fancy name for a neural network that predicts a conditional probability distribution as an output. I assumed that the distribution of my target variables (resp_1, resp_2, resp_3, resp, resp_4, drift, expected_return) of financial asset conditioned on the 130 features provided is approximately normal. I don't have a very good justification for this assumption but considering that the unpredictable inherent randomness of financial asset is most likely a combination of a large number of smaller random variables we can use a central limit theorem argument to justify this.

The MDN predicts the parameters to a multivariate normal distribution and it does so by returning a lower triangular matrix of shape 6x6 and a mean vector of length 6. The six components are (resp_1, resp_2, resp_3, resp, resp_4, drift) respectively. And the loss function is negative log likelihood.

Here is the code for anyone interested:

class MDNDecisionMaker(nn.Module):
    def __init__(self, in_dim, out_dim, hidden_dim, dropout_prob):
        super().__init__()
        self.hidden = hidden_dim
        self.out_dim = out_dim
        self.dropout = dropout_prob
        self.feed_forward_network = nn.Sequential(
            nn.BatchNorm1d(in_dim),
            nn.Linear(in_dim, self.hidden), #1
            nn.SiLU(),
            nn.Dropout(p = 0.3),
            nn.Linear(self.hidden, self.hidden), #2
            nn.SiLU(),
            nn.Dropout(p = 0.3),
            nn.Linear(self.hidden, self.hidden), #3
            nn.SiLU(),
            nn.Dropout(p = 0.3),
            nn.Linear(self.hidden, self.hidden), #3
            nn.SiLU(),
            nn.BatchNorm1d(self.hidden)
        ).double()
        # predict mean value of multivariate gaussian distribution
        self.mean_network = nn.Sequential(
            nn.Linear(self.hidden, self.hidden), #3
            nn.SiLU(),
            nn.Linear(self.hidden, out_dim)
        ).double()
        # predict non diagonal lower triangular values of matrix
        self.cholesky_nondiag_sigmas_network = nn.Sequential(
            nn.Linear(self.hidden, self.hidden), #3
            nn.SiLU(),
            nn.Linear(self.hidden, out_dim*out_dim), #2
        ).double()
        # predict the diagonal elements, these must be non zero to ensure invertibility
        self.cholesky_diag_sigmas_network = nn.Sequential(
            nn.Linear(self.hidden, self.hidden), #3
            nn.SiLU(),
            nn.Linear(self.hidden, out_dim)
        ).double()
        self.bceloss = nn.BCELoss()

    def forward(self, x, return_covariance = False):
        parameters = self.feed_forward_network(x.double())
        means = self.mean_network(parameters)
        cholesky_lower_triangular = torch.tril(self.cholesky_nondiag_sigmas_network(parameters).view(-1, self.out_dim, self.out_dim), diagonal = -1)
        cholesky_diag = torch.diag_embed(torch.exp(self.cholesky_diag_sigmas_network(parameters)).view(-1, self.out_dim))
        cholesky_sigmas =  cholesky_diag + cholesky_lower_triangular
        if return_covariance:
            covariances = torch.bmm(cholesky_sigmas, torch.transpose(cholesky_sigmas, 1, 2))
            return mean, covariances
        return means, cholesky_sigmas

    def liklihood_loss(self, means, cholesky_covariances, samples):
        batch_dim = samples.size()[0] 
        means = means.double()
        samples = samples.double()
        cholesky_covariances = cholesky_covariances.double()
        log_probs = -1/2 * torch.bmm((samples-means).view(batch_dim, 1, self.out_dim), 
                                     torch.cholesky_solve((samples - means).view(batch_dim, self.out_dim, 1), cholesky_covariances)).view(-1,1) \
                    -torch.log(torch.diagonal(cholesky_covariances, dim1=-2, dim2=-1)).sum(dim = 1, keepdim = True) \
                    - self.out_dim/2 * 1.83787706641        
        log_liklihood = log_probs.sum(dim = 0)/batch_dim
        return -log_liklihood
A few key points of the network include:

Outputting a valid positive definite gaussian kernel required me to output a lower triangular matrix with a positive diagonal. Then I can multiply this by its adjoint to get a positive definite matrix (this is called the cholesky decomposition). Getting the right type of matrix required some reparameterization with exponentials to ensure I got positive numbers.
The mixture density network is very very difficult to train. In particular NaN's pop up everywhere because of all the inverses of matrices. Dropout was carefully tuned to prevent overfitting while also not causing erratic optimizer behaviour that would drive the loss to NaNs.
There probably was a lot of scope for tuning the architecture of the network, but I went with a reasonable choice of layers and breadth (SiLU also looked good from the public kernels).
My partner used k-fold purged cross validation to choose the best neural network model to select. And we tried ensembling models from different folds to boost performance.
Predictions
We played around with this for a while, I used monte-carlo simulation where I sampled from the geometric brownian motion process I parameterized earlier. It turned out that nothing worked better for me than taking the mean of the output mean vector from the neural network and trading if that was positive and not trading if it was negative.