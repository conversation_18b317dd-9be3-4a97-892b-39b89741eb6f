### Jane Street Market Prediction Competition (Kaggle, 2020)

**Challenge**: Predict trading actions (0 or 1) in a simulated market environment to maximize a utility score, which rewards positive weighted returns (resp) while penalizing volatility and non-trades. The task involves handling noisy, anonymized high-frequency trading data with time horizons, requiring models to generalize to unseen future data amid market regime shifts.

**Data**: Time-series dataset with ~2.4 million rows across 500 days; includes 130 anonymous features (continuous, some sparse/missing), response variables (resp for overall return, resp_1–4 for different horizons), trade weights (for scoring), and dates (for time-based splitting). Features exhibit multicollinearity, missing values, and non-stationarity; targets are continuous returns but often binarized for action prediction.

### 10th Place Solution: Geometric Brownian Motion (GBM) with Mixture Density Networks (MDN)

**Solution Overview**: Combines stochastic process modeling with probabilistic neural networks for regression. Engineers GBM-based features (drift, volatility, expected return, P(resp>0)) via MLE fitting on resp_1–4 horizons. Trains an MDN to output parameters of a multivariate normal distribution for 6 targets (resp_1–4, overall resp, drift) using negative log-likelihood loss. Architecture: Feedforward NN with SiLU activations, dropout (0.3), batch norm; outputs Cholesky-decomposed covariance for stability. Uses k-fold purged CV, ensembles folds. Inference: Trade if mean output >0; outperforms Monte Carlo simulations.

**Key Insights**: Unique regression focus (vs. classification); GBM captures fundamental asset dynamics beyond raw returns. Challenges: NaN issues in MDN training, careful dropout tuning. Achieved high reruns, emphasizing drift as a robust feature.

### 1st Place Solution: Supervised Autoencoder with Multi-Layer Perceptron (MLP)

**Solution Overview**: Deep learning pipeline blending autoencoder for feature extraction with MLP for prediction, trained end-to-end per CV fold to prevent leakage. CV: 5-fold purged group time-series split (31-day gap), discards first 85 days due to variance shifts. Feature engineering: FFill missing values; multi-label classification on signs of resp/resp_1–4, weighted by absolute resp means. Model: Gaussian noise + encoder-decoder (supervised with targets), concatenated features to MLP (Swish activations, BN, dropout); BCE loss with label smoothing. Hyperopt tuning; averages 3 seeds from last 2 folds. Blended with XGBoost for final submission.

**Key Insights**: Supervised AE generates relevant features without leakage; multi-target weighting focuses on high-impact samples. Single-model scores ~6022 on private LB; emphasizes monitoring BCE on MLP for early stopping, reducing variance via ensembling/seeds.

### TabNet Solution (Adapted from Similar Multi-Label Prediction Contexts)

**Solution Overview**: Employs TabNet (attention-based tabular NN) as a regressor for multi-label outputs, enabling interpretability via feature masks. Stratified K-fold (10 folds) or shuffle-split; BCE loss with logits. Architecture: n_d/a=8–24, 1–3 steps, gamma=1.3, entmax/sparsemax masks; supports categorical/numerical inputs without global norm (uses BN). Fits with Adam (lr=2e-2, decay), MultiStepLR scheduler, patience=50. Inference: Sigmoid on predictions; ensembles seeds/folds. Feature selection is instance-wise, aiding sparsity.

**Key Insights**: Fast GPU training for multi-label (one model vs. per-target); adds diversity to ensembles. From Lish-MOA context (multi-label drug response prediction), but adaptable to quant finance for handling tabular time-series with imbalances. Strengths: Built-in sparsity/interpretability; challenges: Hyperparameter sensitivity, requires binary targets check.