**Executive summary — Jane Street Market Prediction (Kaggle 2020–2021)**

**Challenge.** Predict a binary **action** (trade / no‑trade) to **maximize a clipped “utility” score** built from per‑trade weights and returns, under a **time‑series code API** that forbids look‑ahead and with a second, **live forecasting phase** (massive distribution shift, strong seed/variance effects). &#x20;

**Data.** \~2.4M training rows over \~500 days, **130 anonymized continuous features**, targets resp and resp₁–₄ (different horizons), plus **weight** (rows with weight=0 don’t score but are present), **date / ts\_id** for ordered splits. First \~85 days behave differently and are commonly dropped. The public (historical) test had \~1M rows; later, models were rerun on **live market data**. The dataset was later removed (2021‑10‑08). &#x20;

---

## Top shared solutions (what they did, why it helped, caveats)

**1) 1st place — Supervised Autoencoder + MLP (blended with XGBoost).**

* **Core idea:** Learn a **representation with a supervised autoencoder** (targets injected to avoid useless reconstructions) and feed it, concatenated with raw features, into an **MLP trained per CV fold** to avoid leakage. Multi‑label classification over signs of resp/resp₁–₄; final action = mean of predicted probabilities. **Label smoothing**, **Swish**, BN, dropout, **Gaussian noise**, **Hyperopt** for tuning. **5‑fold Purged Group TimeSeries CV with 31‑day gap**, drop first 85 days, forward‑fill NaNs, sample‑weight by |resp| mean. Train **3 seeds, keep the last two folds (saw more data)**. Single AE‑MLP scored **6022.2 (private LB)**.
* **Why it worked:** Powerful **leakage‑free representation learning** plus robust, purged CV that mirrors production; multi‑target training shares signal across horizons.
* **Caveats:** Heavy engineering; supervised AE must be **retrained inside each fold** to avoid leakage.&#x20;

**2) 3rd place — “Fight randomness with randomness”: 15× (49‑layer) deep MLP ensemble.**

* **Core idea:** **Minimal FE, no CV** in the final training, but **15 very deep MLPs (49 layers, Mish, BN, dropout, skip connections)** trained with different seeds; predict 5 resps jointly, then average and threshold at 0.5. Mixed **BCE (label smoothing 0.1) + utility‑like loss**. **Median impute conditioned on f0**, **include weight=0 rows**, drop first 85 days. **Train/val split (85–300 vs. 350–500)** and **public LB used for early stopping**. Models converted to **TFLite** for inference speed.
* **Why it worked:** **Extreme ensembling over seeds** stabilized a highly seed‑sensitive objective; very deep nets could fit weak signals when regularized hard.
* **Caveats:** **Public‑LB early stopping risk**, weak validation design (no purged CV), heavy compute.&#x20;

**3) 10th place — Geometric Brownian Motion (GBM) features + Mixture Density Network (MDN).**

* **Core idea:** **Estimate GBM drift/volatility via MLE** on resp₁–₄ to engineer **drift, σ, expected return, P(resp>0)**, then train an **MDN that outputs a full multivariate Gaussian (means + Cholesky for covariance) over 6 targets (resp, resp₁–₄, drift)** using **negative log‑likelihood**. **Purged k‑fold CV**; final decision = trade if mean(resp̂) > 0.
* **Why it worked:** Injects a **stochastic‑process prior** that’s more stable than raw returns; **probabilistic output** matches noisy finance targets.
* **Caveats:** **Very hard to train (NaNs, matrix inverses)**; MDNs are fragile, lots of tuning needed; regression focus was unusual vs. the dominant classifiers.&#x20;

**4) TabNet (as a fast, diversity‑adding ensembling component).**

* **Core idea:** Use **TabNetRegressor** for **multi‑label BCE**, leveraging **instance‑wise sparse feature masks** (entmax/sparsemax) for speed + interpretability; typically **10‑fold stratified** or shuffle‑split; often not SOTA alone but **cheap and diverse** for blends.
* **Why it worked:** Different inductive bias (sequential attention + sparsity) → **useful diversification** in ensembles.
* **Caveats:** **Hyperparameter sensitive**, and in this competition it generally trailed the top deep MLP pipelines.&#x20;

---

## What actually mattered across strong solutions

* **Purged, gap‑aware time‑series CV** (or at least a careful holdout) to control leakage and regime shift. The best solution is explicit about this; skipping it (3rd place) required “LB early stopping” and many seeds to cope with instability. &#x20;
* **Multi‑target training on resp, resp₁–₄** (classification or regression) consistently helped — sharing signal across horizons improves robustness. &#x20;
* **Representation learning** (supervised AE) can beat raw‑feature MLPs/XGB when done **fold‑wise to avoid leakage**.&#x20;
* **Heavy ensembling / multiple seeds** to tame **extreme seed variance** and noisy targets.&#x20;
* **Simple imputations work** (ffill; median conditional on a key feature) — over‑engineering features didn’t dominate. &#x20;
* **Losses:** BCE (with label smoothing) was the de‑facto choice; a few tried **utility‑aware** or **likelihood (MDN)** losses — the latter added complexity but brought principled uncertainty modeling. &#x20;

---

## Common pitfalls & gotchas

* **Leakage via pretraining representations outside CV folds** (why the 1st place integrated AE training inside each fold).&#x20;
* **Distribution shift in the first 85 days** — most winners **dropped them**. &#x20;
* **Seed instability** → need many seeds / ensembles to stabilize.&#x20;
* **Public‑LB overfitting / using it for early stopping** — tempting but risky (works here largely because the public set was huge).&#x20;

---

## Transferable takeaways

1. **Use purged, gap‑aware time splits** for any market‑like problem.
2. **Multi‑horizon, multi‑target training** is a cheap way to densify signal.
3. **Representation learning (e.g., supervised AE)** can help on anonymized, collinear features — **but must be folded to avoid leakage**.
4. **Fight randomness with randomness**: average many seeds/folds; stabilize with label smoothing, BN, dropout.
5. **If you model distributions (MDN), do it for a reason** — e.g., to price uncertainty or derive risk‑aware actions — and budget time for numerical stability.

If you’d like, I can now: (a) build a **cross‑competition comparison table** template we can fill as you add more cases, or (b) turn these into a **playbook/checklist** you can reuse on future quant ML comps. Let me know.
