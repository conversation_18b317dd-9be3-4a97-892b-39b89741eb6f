4th place solution
update
We published the code in the following notebook.
https://www.kaggle.com/code/flat831/4th-place-model/notebook?scriptVersionId=100052889

Thank you JP<PERSON> for sharing your data and hosting a fun competition. Thank you Kaggle. Thank you Kagglers for sharing many helpful discussions and notebooks. Thank you my teammates, <PERSON><PERSON><PERSON><PERSON> and hayato03!

Model Summary
Our model is very simple. To be honest, this result is mostly luck.

model1(private score: -0.196)
Rank return_1day in ascending order
If ExpectedDividend is greater than 0, make it the lowest.

model2:(private score: 0.347 <- this model is 4th place!)
Rank return_1day in descending order.
If ExpectedDividend is greater than 0, make it the lowest.

return_1day is based on the code in this notebook.
return_1day = feats["AdjustedClose"].pct_change(1)

Below is a brief explanation of the reasons for this approach.

Difficulty in model evaluation
https://www.kaggle.com/competitions/jpx-tokyo-stock-exchange-prediction/discussion/320323

As noted in the discussion above, the random model scores can be approximated by a normal distribution of μ = 0 and σ = 0.13785. Therefore, even if we were to create a model with a score of -0.3~0.3, we cannot assume that it is a good model. Furthermore, if we create many models, the probability of a good score by chance increases. (Like the multiple comparisons problem in statistics)
Finally, we gave up on creating a ML model and decided to submit a simple approach to avoid errors.

ExpectedDividend
We adopted this variable because we were confident that it would be effective.

The definition of ExpectedDividend column is as follows.

Expected dividend value for ex-right date. This value is recorded 2 business days before ex-dividend date.

And, the definition of target column is as follows.

Change ratio of adjusted closing price between t+2 and t+1 where t+0 is TradeDate

Thus, since t+2 is the ex-dividend date and t+1 is the date of record, target is likely to be negative.
(However, since ExpectedDividend is almost null, this alone does not get high score…)

Flipped ranking
https://www.kaggle.com/competitions/jpx-tokyo-stock-exchange-prediction/discussion/356038

As noted in the discussion above, flipped the ranking and we get a score multiplied by -1. In the competition, we can choose two models, so adopting this strategy will ensure a score > 0.

# libraries
import os
from decimal import ROUND_HALF_UP, Decimal#float型の計算を正確に行うため

import numpy as np
import pandas as pd
from tqdm import tqdm#処理状況の可視化
import warnings

warnings.filterwarnings('ignore')
# set base_dir to load data
base_dir = "../input/jpx-tokyo-stock-exchange-prediction"

train_files_dir = f"{base_dir}/train_files"
supplemental_files_dir = f"{base_dir}/supplemental_files"
functions
#分割・逆分割による過去の価格差を小さくするために、AdjustmentFactorの値を用いて、株価を修正する
def adjust_price(price):
    """
    Args:
        price (pd.DataFrame)  : pd.DataFrame include stock_price
    Returns:
        price DataFrame (pd.DataFrame): stock_price with generated AdjustedClose
    """
    # transform Date column into datetime
    price.loc[: ,"Date"] = pd.to_datetime(price.loc[: ,"Date"], format="%Y-%m-%d")

    def generate_adjusted_close(df):
        """
        Args:
            df (pd.DataFrame)  : stock_price for a single SecuritiesCode
        Returns:
            df (pd.DataFrame): stock_price with AdjustedClose for a single SecuritiesCode
        """
        # sort data to generate CumulativeAdjustmentFactor
        df = df.sort_values("Date", ascending=False)#降順(最新のものが先頭)
        # generate CumulativeAdjustmentFactor
        df.loc[:, "CumulativeAdjustmentFactor"] = df["AdjustmentFactor"].cumprod()#cumprodは累積積を求める関数
        # generate AdjustedClose
        df.loc[:, "AdjustedClose"] = (
            df["CumulativeAdjustmentFactor"] * df["Close"]
        ).map(lambda x: float(
            Decimal(str(x)).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)#四捨五入
        ))
        # reverse order
        df = df.sort_values("Date")#昇順に戻す
        # to fill AdjustedClose, replace 0 into np.nan
        df.loc[df["AdjustedClose"] == 0, "AdjustedClose"] = np.nan
        # forward fill AdjustedClose
        df.loc[:, "AdjustedClose"] = df.loc[:, "AdjustedClose"].ffill()#ffill:前(上)の値に置換
        return df

    # generate AdjustedClose
    price = price.sort_values(["SecuritiesCode", "Date"])
    price = price.groupby("SecuritiesCode").apply(generate_adjusted_close).reset_index(drop=True)

    price.set_index("Date", inplace=True)
    return price
def get_features_for_predict(price, code):
    """
    Args:
        price (pd.DataFrame)  : pd.DataFrame include stock_price
        code (int)  : A local code for a listed company
    Returns:
        feature DataFrame (pd.DataFrame)
    """
    close_col = "AdjustedClose"
    feats = price.loc[price["SecuritiesCode"] == code, ["SecuritiesCode", close_col, "ExpectedDividend"]].copy()

    # calculate return using AdjustedClose
    feats["return_1day"] = feats[close_col].pct_change(1)
    
    # ExpectedDividend
    feats["ExpectedDividend"] = feats["ExpectedDividend"].mask(feats["ExpectedDividend"] > 0, 1)

    # filling data for nan and inf
    feats = feats.fillna(0)
    feats = feats.replace([np.inf, -np.inf], 0)
    # drop AdjustedClose column
    feats = feats.drop([close_col], axis=1)

    return feats
Submit
# load stock price data
df_price_raw = pd.read_csv(f"{train_files_dir}/stock_prices.csv")
price_cols = [
    "Date",
    "SecuritiesCode",
    "Close",
    "AdjustmentFactor",
    "ExpectedDividend"
]
df_price_raw = df_price_raw[price_cols]

# forecasting phase leaderboard:
df_price_supplemental = pd.read_csv(f"{supplemental_files_dir}/stock_prices.csv")
df_price_supplemental = df_price_supplemental[price_cols]
df_price_raw = pd.concat([df_price_raw, df_price_supplemental])

# filter data to reduce culculation cost 
df_price_raw = df_price_raw.loc[df_price_raw["Date"] >= "2022-07-01"]
# load Time Series API
import jpx_tokyo_market_prediction
# make Time Series API environment (this function can be called only once in a session)
env = jpx_tokyo_market_prediction.make_env()
# get iterator to fetch data day by day
iter_test = env.iter_test()
counter = 0
# fetch data day by day
for (prices, options, financials, trades, secondary_prices, sample_prediction) in iter_test:
    current_date = prices["Date"].iloc[0]
    sample_prediction_date = sample_prediction["Date"].iloc[0]
    print(f"current_date: {current_date}, sample_prediction_date: {sample_prediction_date}")

    if counter == 0:
        # to avoid data leakage
        df_price_raw = df_price_raw.loc[df_price_raw["Date"] < current_date]#current_date以前のデータにする

    # to generate AdjustedClose, increment price data
    df_price_raw = pd.concat([df_price_raw, prices[price_cols]])
    # generate AdjustedClose
    df_price = adjust_price(df_price_raw)

    # get target SecuritiesCodes
    codes = sorted(prices["SecuritiesCode"].unique())

    # generate feature
    feature = pd.concat([get_features_for_predict(df_price, code) for code in codes])
    # filter feature for this iteration
    feature = feature.loc[feature.index == current_date]

    # prediction
    feature.loc[:, "predict"] = feature["return_1day"] + feature["ExpectedDividend"]*100

    # set rank by predict
    feature = feature.sort_values("predict", ascending=True).drop_duplicates(subset=['SecuritiesCode'])
    feature.loc[:, "Rank"] = np.arange(len(feature))
    feature_map = feature.set_index('SecuritiesCode')['Rank'].to_dict()
    sample_prediction['Rank'] = sample_prediction['SecuritiesCode'].map(feature_map)

    # check Rank
    assert sample_prediction["Rank"].notna().all()
    assert sample_prediction["Rank"].min() == 0
    assert sample_prediction["Rank"].max() == len(sample_prediction["Rank"]) - 1

    # register your predictions
    env.predict(sample_prediction)
    counter += 1
This version of the API is not optimized and should not be used to estimate the runtime of your code on the hidden test set.
current_date: 2021-12-06, sample_prediction_date: 2021-12-06
current_date: 2021-12-07, sample_prediction_date: 2021-12-07
! head submission.csv
Date,SecuritiesCode,Rank
2021-12-06,1301,0
2021-12-06,1332,1339
2021-12-06,1333,1338
2021-12-06,1375,1337
2021-12-06,1376,1336
2021-12-06,1377,1335
2021-12-06,1379,1334
2021-12-06,1381,1333
2021-12-06,1407,1332
! tail submission.csv