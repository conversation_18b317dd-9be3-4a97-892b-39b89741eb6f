# ==============================================================================
# MITSUI & CO. Commodity Prediction - Advanced Pipeline (V4.6)
#
# Strategy: Two-Stage Asset Forecaster with Online LGBM Updates (Polars Native)
# Author: Your Expert ML Advisor (in collaboration with you)
#
# V4.5: Attempted to suppress warnings using Python's `warnings` module, which failed.
# V4.6 (FINAL, CORRECT FIX):
# - Correctly suppressed the "No further splits" log message by using
#   LightGBM's own `'verbosity': 0` parameter. This is the official and
#   correct way to control the C++ core's logging, as the message is
#   an "Info" level log printed directly to stdout, bypassing Python's warning system.
# - Removed the ineffective `warnings.filterwarnings` call.
# ==============================================================================

import os
import sys
import gc
import numpy as np
import pandas as pd
import polars as pl
import lightgbm as lgb

# ------------------------------------------------------------------------------
# 0. 动态路径修正与模块导入
# ------------------------------------------------------------------------------
try:
    current_script_path = os.path.dirname(os.path.abspath(__file__))
except NameError:
    current_script_path = os.getcwd()

project_root = os.path.abspath(os.path.join(current_script_path, '..', '..'))
module_path = os.path.join(project_root, 'input', 'mitsui-commodity-prediction-challenge')

if module_path not in sys.path:
    print(f"将模块路径添加到 sys.path: {module_path}")
    sys.path.insert(0, module_path)

try:
    import kaggle_evaluation.mitsui_inference_server
    print("成功导入 mitsui_inference_server。")
except ImportError as e:
    print(f"导入失败。错误: {e}")

# ------------------------------------------------------------------------------
# 1. 全局环境标志
# ------------------------------------------------------------------------------
IS_KAGGLE_RERUN = os.getenv('KAGGLE_IS_COMPETITION_RERUN') is not None

# ------------------------------------------------------------------------------
# 2. 智能环境检测与路径管理
# ------------------------------------------------------------------------------
def get_base_path():
    if 'google.colab' in sys.modules: return '/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge'
    elif os.path.exists('/kaggle/input'): return '/kaggle/input/mitsui-commodity-prediction-challenge'
    else: return os.path.join(project_root, 'input', 'mitsui-commodity-prediction-challenge')

BASE_PATH = get_base_path()

# ------------------------------------------------------------------------------
# 3. 全局状态与配置
# ------------------------------------------------------------------------------
MODELS = {1: None, 2: None, 3: None, 4: None}
HISTORICAL_FEATURES = pl.DataFrame()
HISTORICAL_LABELS = pl.DataFrame()
try:
    TARGET_PAIRS = pl.read_csv(os.path.join(BASE_PATH, 'target_pairs.csv'))
    ALL_ASSETS = sorted(list(set(asset for pair_string in TARGET_PAIRS['pair'] for asset in pair_string.split(' - '))))
    ASSET_TO_INDEX = {asset: i for i, asset in enumerate(ALL_ASSETS)}
    INDEX_TO_ASSET = {i: asset for asset, i in ASSET_TO_INDEX.items()}
    NUM_ASSETS = len(ALL_ASSETS)
    NUM_TARGET_COLUMNS = len(TARGET_PAIRS)
    print(f"成功加载目标配对信息，共找到 {NUM_ASSETS} 个独立资产和 {NUM_TARGET_COLUMNS} 个目标。")
except FileNotFoundError:
    print("错误: 无法加载 target_pairs.csv。")
    TARGET_PAIRS, ALL_ASSETS, ASSET_TO_INDEX, INDEX_TO_ASSET, NUM_ASSETS, NUM_TARGET_COLUMNS = None, [], {}, {}, 0, 424

GENERIC_FEATURES = ['log_return', 'mom5', 'vol21', 'rank']

# ------------------------------------------------------------------------------
# 4. 核心功能函数
# ------------------------------------------------------------------------------
def cast_and_clean(df: pl.DataFrame, is_label_df: bool = False) -> pl.DataFrame:
    cols_to_cast = []
    if is_label_df:
        cols_to_cast = [pl.col(c).cast(pl.Float64, strict=False) for c in df.columns if c.startswith('target_')]
    else:
        cols_to_cast = [pl.col(c).cast(pl.Float64, strict=False) for c in df.columns if c != 'date_id']
    return df.with_columns(cols_to_cast)

def calculate_features(df: pl.DataFrame) -> pl.DataFrame:
    price_cols = [col for col in df.columns if col not in ['date_id']]
    log_returns = df.select([pl.col('date_id'), *[pl.col(c).log().diff(1).alias(f"{c}_log_return") for c in price_cols]])
    rolling_features = log_returns.select([pl.col('date_id'), *[pl.col(f"{c}_log_return").rolling_mean(window_size=5).alias(f"{c}_mom5") for c in price_cols], *[pl.col(f"{c}_log_return").rolling_std(window_size=21).alias(f"{c}_vol21") for c in price_cols]])
    rank_features = log_returns.select([pl.col('date_id'), *[pl.col(f"{c}_log_return").rank(descending=False).over('date_id').alias(f"{c}_rank") for c in price_cols]])
    features = log_returns.join(rolling_features, on='date_id', how='left').join(rank_features, on='date_id', how='left')
    return features.fill_null(0).fill_nan(0)

def prepare_training_data_for_lag(features: pl.DataFrame, labels: pl.DataFrame, lag: int) -> tuple[pd.DataFrame, pd.Series]:
    lag_pairs = TARGET_PAIRS.filter(pl.col('lag') == lag)
    lag_assets = sorted(list(set(asset for pair_string in lag_pairs['pair'] for asset in pair_string.split(' - '))))
    all_X, all_y = [], []
    for asset_name in lag_assets:
        asset_id = ASSET_TO_INDEX[asset_name]
        proxy_target_row = lag_pairs.filter(pl.col('pair').str.starts_with(asset_name)).head(1)
        if proxy_target_row.height == 0: continue
        proxy_target_col = proxy_target_row['target'][0]
        asset_feature_cols = [f"{asset_name}_{gf}" for gf in GENERIC_FEATURES]
        if not all(c in features.columns for c in asset_feature_cols): continue
        asset_features = features.select(['date_id'] + asset_feature_cols)
        rename_dict = {old: new for old, new in zip(asset_feature_cols, GENERIC_FEATURES)}
        asset_features = asset_features.rename(rename_dict)
        asset_labels = labels.select(['date_id', proxy_target_col]).rename({proxy_target_col: 'target'})
        merged = asset_features.join(asset_labels, on='date_id', how='inner').drop_nulls()
        merged = merged.with_columns(pl.lit(asset_id).cast(pl.Int32).alias('asset_id'))
        if merged.height > 0:
            all_y.append(merged.get_column('target').to_pandas())
            all_X.append(merged.drop(['date_id', 'target']).to_pandas())
    if not all_X: return pd.DataFrame(), pd.Series()
    return pd.concat(all_X, ignore_index=True), pd.concat(all_y, ignore_index=True)

def train_or_update_model(lag: int, features: pl.DataFrame, labels: pl.DataFrame, is_initial_training: bool):
    global MODELS
    print(f"--- 开始为 lag={lag} 训练/更新模型 ---")
    
    X_train, y_train = prepare_training_data_for_lag(features, labels, lag)
    
    if X_train.empty:
        print(f"--- lag={lag} 没有有效的训练样本，跳过更新 ---")
        return

    params = {
        'objective': 'regression_l1', 'metric': 'mae', 'n_estimators': 2000,
        'n_jobs': -1, 'seed': 42, 'boosting_type': 'gbdt', 'device': 'gpu',
        'learning_rate': 0.03465069205953159, 'num_leaves': 211, 'max_depth': 7,
        'subsample': 0.8121397496875854, 'colsample_bytree': 0.9314550472485685,
        'reg_alpha': 50.72133821482621, 'reg_lambda': 65.12056401382868,
        # **【V4.6 核心改动】**
        # 将 verbosity 设置为 0 来抑制 "Info" 级别的日志（包括 "No further splits"）
        'verbosity': 0,
    }
    
    if not is_initial_training:
        params['n_estimators'] = 100
    
    model = lgb.LGBMRegressor(**params)
    
    callbacks = []
    if not IS_KAGGLE_RERUN:
        if is_initial_training:
            # early_stopping 的 verbose=True 只在停止时打印，这是有用的
            callbacks.append(lgb.early_stopping(100, verbose=True))
        else:
            callbacks.append(lgb.log_evaluation(period=50))
        
    eval_set = None
    if is_initial_training and len(X_train) > 10:
        train_end_idx = int(len(X_train) * 0.9)
        X_tr, X_val = X_train.iloc[:train_end_idx], X_train.iloc[train_end_idx:]
        y_tr, y_val = y_train.iloc[:train_end_idx], y_train.iloc[train_end_idx:]
        eval_set = [(X_val, y_val)]
        X_train, y_train = X_tr, y_tr
    
    model.fit(X_train, y_train, init_model=MODELS[lag], callbacks=callbacks, 
              categorical_feature=['asset_id'], eval_set=eval_set)
              
    MODELS[lag] = model
    print(f"--- lag={lag} 模型在 {len(X_train)} 个样本上更新完毕 ---")

def predict_and_map(features_today: pl.DataFrame) -> pl.DataFrame:
    asset_preds_all_lags = {}
    for lag, model in MODELS.items():
        if model:
            lag_assets = sorted(list(set(asset for pair_string in TARGET_PAIRS.filter(pl.col('lag') == lag)['pair'] for asset in pair_string.split(' - '))))
            for asset_name in lag_assets:
                asset_id = ASSET_TO_INDEX[asset_name]
                asset_feature_cols = [f"{asset_name}_{gf}" for gf in GENERIC_FEATURES]
                if not all(c in features_today.columns for c in asset_feature_cols): continue
                X_asset = features_today.select(asset_feature_cols)
                rename_dict = {old: new for old, new in zip(asset_feature_cols, GENERIC_FEATURES)}
                X_asset = X_asset.rename(rename_dict)
                X_asset = X_asset.with_columns(pl.lit(asset_id).cast(pl.Int32).alias('asset_id'))
                train_cols = model.booster_.feature_name()
                X_asset = X_asset.select(train_cols)
                pred = model.predict(X_asset.to_pandas())[0]
                asset_preds_all_lags[(lag, asset_id)] = pred
    all_predictions = {}
    for row in TARGET_PAIRS.iter_rows(named=True):
        target, lag, pair_str = row['target'], row['lag'], row['pair']
        assets = pair_str.split(' - ')
        asset_a_id = ASSET_TO_INDEX.get(assets[0])
        pred_a = asset_preds_all_lags.get((lag, asset_a_id), 0.0)
        if len(assets) > 1:
            asset_b_id = ASSET_TO_INDEX.get(assets[1])
            pred_b = asset_preds_all_lags.get((lag, asset_b_id), 0.0)
            all_predictions[target] = pred_a - pred_b
        else:
            all_predictions[target] = pred_a
    for i in range(NUM_TARGET_COLUMNS):
        if f"target_{i}" not in all_predictions: all_predictions[f"target_{i}"] = 0.0
    preds_df = pl.DataFrame([all_predictions])
    transposed = preds_df.transpose()
    ranked_transposed = transposed.select(pl.all().rank(method='average', descending=False))
    final_ranks = ranked_transposed.transpose()
    final_ranks.columns = preds_df.columns
    return final_ranks.fill_null(NUM_TARGET_COLUMNS / 2)
    
# ------------------------------------------------------------------------------
# 5. 核心预测函数
# ------------------------------------------------------------------------------
def predict(
    test: pl.DataFrame,
    label_lags_1_batch: pl.DataFrame,
    label_lags_2_batch: pl.DataFrame,
    label_lags_3_batch: pl.DataFrame,
    label_lags_4_batch: pl.DataFrame,
) -> pl.DataFrame | pd.DataFrame:
    
    global HISTORICAL_FEATURES, HISTORICAL_LABELS
    is_initial_call = all(m is None for m in MODELS.values())
    
    if is_initial_call:
        print("首次调用，必须先加载数据并进行初始训练...")
        train_path = os.path.join(BASE_PATH, 'train.csv')
        labels_path = os.path.join(BASE_PATH, 'train_labels.csv')
        HISTORICAL_FEATURES = cast_and_clean(pl.read_csv(train_path))
        HISTORICAL_LABELS = cast_and_clean(pl.read_csv(labels_path), is_label_df=True)
        first_test_date = test['date_id'][0]
        train_features_raw = HISTORICAL_FEATURES.filter(pl.col('date_id') < first_test_date)
        print("计算初始训练特征...")
        features_df = calculate_features(train_features_raw)
        print(f"用于初始训练的衍生特征形状: {features_df.shape}")
        for lag in [1, 2, 3, 4]:
            train_or_update_model(lag, features_df, HISTORICAL_LABELS, is_initial_training=True)

    test_cleaned = cast_and_clean(test)
    if 'is_scored' in test_cleaned.columns:
        test_features_only = test_cleaned.drop('is_scored')
    else:
        test_features_only = test_cleaned
    HISTORICAL_FEATURES = pl.concat([HISTORICAL_FEATURES, test_features_only])
    
    lag_batches = {1: label_lags_1_batch, 2: label_lags_2_batch, 3: label_lags_3_batch, 4: label_lags_4_batch}
    has_new_labels = any(df is not None and df.height > 0 for df in lag_batches.values())
    
    if has_new_labels and not is_initial_call:
        print("接收到新标签，进行增量更新...")
        latest_label_date = max(b['label_date_id'].max() for b in lag_batches.values() if b is not None and b.height > 0)
        update_window_start = max(0, latest_label_date - 60)
        recent_hist_features_raw = HISTORICAL_FEATURES.filter(pl.col('date_id') >= update_window_start)
        features_df = calculate_features(recent_hist_features_raw)
        for lag, batch in lag_batches.items():
            if batch is not None and batch.height > 0:
                batch_cleaned = cast_and_clean(batch, is_label_df=True)
                existing_dates = HISTORICAL_LABELS['date_id'].to_list()
                new_labels = batch_cleaned.filter(~pl.col('date_id').is_in(existing_dates))
                if new_labels.height > 0:
                    HISTORICAL_LABELS = pl.concat([HISTORICAL_LABELS, new_labels.select(HISTORICAL_LABELS.columns)], how='vertical_relaxed')
        labels_to_update = HISTORICAL_LABELS.filter(pl.col('date_id') >= update_window_start)
        for lag in [1, 2, 3, 4]:
            train_or_update_model(lag, features_df, labels_to_update, is_initial_training=False)

    print("使用更新后的模型进行预测...")
    today_date_id = test['date_id'][0]
    recent_features_raw = HISTORICAL_FEATURES.filter(pl.col('date_id') <= today_date_id).tail(30)
    features_today_df = calculate_features(recent_features_raw).filter(pl.col('date_id') == today_date_id)
    
    return predict_and_map(features_today_df)

# ------------------------------------------------------------------------------
# 6. 健壮的本地评分函数
# ------------------------------------------------------------------------------
def robust_local_scorer(base_data_path, submission_path):
    print("\n" + "="*50 + "\n启动本地评分器...\n" + "="*50)
    try:
        submission_df_pl = pl.read_parquet(submission_path)
        solution_df_full_pl = pl.read_csv(os.path.join(base_data_path, 'train_labels.csv'))
        submitted_dates = submission_df_pl.get_column('date_id').unique()
        solution_df_pl = solution_df_full_pl.filter(pl.col('date_id').is_in(submitted_dates))
        submission_df = submission_df_pl.to_pandas()
        solution_df = solution_df_pl.to_pandas()
        submission_df = submission_df.rename(columns={col: col.replace('target_', 'prediction_') for col in submission_df.columns if col.startswith('target_')})
        merged_df = pd.merge(solution_df, submission_df, on='date_id')
        prediction_cols = [col for col in merged_df.columns if col.startswith('prediction_')]
        target_cols = [col for col in merged_df.columns if col.startswith('target_')]
        def _robust_compute_rank_correlation(row):
            try:
                non_null_targets = [col for col in target_cols if not pd.isnull(row[col])]
                if not non_null_targets: return np.nan
                matching_predictions = [col for col in prediction_cols if col.replace('prediction', 'target') in non_null_targets]
                if row[non_null_targets].std(ddof=0) == 0 or row[matching_predictions].std(ddof=0) == 0: return np.nan
                return np.corrcoef(row[matching_predictions].rank(method='average'), row[non_null_targets].rank(method='average'))[0, 1]
            except Exception: return np.nan
        daily_rank_corrs = merged_df.apply(_robust_compute_rank_correlation, axis=1).dropna()
        if len(daily_rank_corrs) == 0: local_score = 0.0
        else:
            std_dev = daily_rank_corrs.std(ddof=0)
            if std_dev == 0 or pd.isnull(std_dev): local_score = 0.0
            else: local_score = float(daily_rank_corrs.mean() / std_dev)
        print("\n" + "*"*50 + f"\n本地模拟评分完成！\n在 {len(daily_rank_corrs)}/{len(merged_df)} 个可评分日上计算...\n您的本地分数是: {local_score:.4f}\n" + "*"*50)
    except Exception as e: print(f"\n本地评分时发生未知错误: {e}")

# ------------------------------------------------------------------------------
# 7. API服务器初始化与主执行逻辑
# ------------------------------------------------------------------------------
if __name__ == "__main__":
    print("正在初始化API服务器...")
    try:
        inference_server = kaggle_evaluation.mitsui_inference_server.MitsuiInferenceServer(predict)
        if IS_KAGGLE_RERUN:
            inference_server.serve()
        else:
            print("在本地或交互式环境中运行，启动本地网关。")
            if os.path.exists(BASE_PATH):
                 inference_server.run_local_gateway((BASE_PATH,))
                 submission_file_path = os.path.join(current_script_path, 'submission.parquet')
                 if os.path.exists(submission_file_path):
                     robust_local_scorer(BASE_PATH, submission_file_path)
                 else:
                     print(f"\n警告: 未找到生成的 `submission.parquet` 文件。跳过本地评分。")
            else:
                print(f"错误：本地数据路径 '{BASE_PATH}' 不存在。无法启动本地网关。")
    except Exception as e:
        print(f"初始化或运行服务器时发生未知错误: {e}")
    print("脚本执行完毕。")