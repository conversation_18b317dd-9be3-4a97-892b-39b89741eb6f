# ==============================================================================
# Standalone Optuna Hyperparameter Tuner for MITSUI LGBM Model (V2.0 - Simplified)
#
# This script is fully self-contained. It uses a simple, robust path detection
# mechanism that is proven to work on Kaggle and standard local setups.
#
# How to run:
# 1. Place this script anywhere in your project (e.g., `src/algo/`).
# 2. Make sure your data is in the `input/` directory at the project root.
# 3. Run it: `python path/to/optuna_tuner.py`
# ==============================================================================

import os
import sys
import numpy as np
import pandas as pd
import polars as pl
import lightgbm as lgb
import optuna
from sklearn.metrics import mean_absolute_error

# --- 1. 简化的、可靠的路径检测 ---

def get_base_path():
    """
    A simple and robust function to find the data path for Kaggle, Colab,
    and a standard local project structure.
    """
    # Kaggle environment
    if os.path.exists('/kaggle/input'):
        print("检测到 Kaggle 环境。")
        return '/kaggle/input/mitsui-commodity-prediction-challenge'
    
    # Google Colab environment
    if 'google.colab' in sys.modules:
        print("检测到 Google Colab 环境。")
        return '/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge'
    
    # Standard local environment (assumes script is run from project root or subdirs)
    # We look for the 'input' directory in the current path or parent paths.
    current_path = os.getcwd()
    for _ in range(4): # Search up to 4 levels up
        if 'input' in os.listdir(current_path):
            project_root = current_path
            print(f"检测到本地环境，项目根目录: {project_root}")
            return os.path.join(project_root, 'input', 'mitsui-commodity-prediction-challenge')
        current_path = os.path.dirname(current_path)

    raise FileNotFoundError("无法自动定位数据路径。请确保 'input' 文件夹在您的项目目录中。")


BASE_PATH = get_base_path()
print(f"数据基础路径设置为: {BASE_PATH}")

try:
    TARGET_PAIRS = pl.read_csv(os.path.join(BASE_PATH, 'target_pairs.csv'))
    ALL_ASSETS = sorted(list(set(asset for pair_string in TARGET_PAIRS['pair'] for asset in pair_string.split(' - '))))
    ASSET_TO_INDEX = {asset: i for i, asset in enumerate(ALL_ASSETS)}
    print(f"成功加载目标配对信息，共找到 {len(ALL_ASSETS)} 个独立资产。")
except FileNotFoundError:
    print(f"错误: 无法在 '{BASE_PATH}' 中找到 target_pairs.csv。请检查路径。")
    sys.exit(1)

GENERIC_FEATURES = ['log_return', 'mom5', 'vol21', 'rank']

# --- 2. 自包含的数据处理函数 ---

def cast_and_clean(df: pl.DataFrame, is_label_df: bool = False) -> pl.DataFrame:
    cols_to_cast = []
    if is_label_df:
        cols_to_cast = [pl.col(c).cast(pl.Float64, strict=False) for c in df.columns if c.startswith('target_')]
    else:
        cols_to_cast = [pl.col(c).cast(pl.Float64, strict=False) for c in df.columns if c != 'date_id']
    return df.with_columns(cols_to_cast)

def calculate_features(df: pl.DataFrame) -> pl.DataFrame:
    price_cols = [col for col in df.columns if col not in ['date_id']]
    log_returns = df.select([pl.col('date_id'), *[pl.col(c).log().diff(1).alias(f"{c}_log_return") for c in price_cols]])
    rolling_features = log_returns.select([pl.col('date_id'), *[pl.col(f"{c}_log_return").rolling_mean(window_size=5).alias(f"{c}_mom5") for c in price_cols], *[pl.col(f"{c}_log_return").rolling_std(window_size=21).alias(f"{c}_vol21") for c in price_cols]])
    rank_features = log_returns.select([pl.col('date_id'), *[pl.col(f"{c}_log_return").rank(descending=False).over('date_id').alias(f"{c}_rank") for c in price_cols]])
    features = log_returns.join(rolling_features, on='date_id', how='left').join(rank_features, on='date_id', how='left')
    return features.fill_null(0).fill_nan(0)

def prepare_training_data_for_lag(features: pl.DataFrame, labels: pl.DataFrame, lag: int) -> tuple[pd.DataFrame, pd.Series]:
    lag_pairs = TARGET_PAIRS.filter(pl.col('lag') == lag)
    lag_assets = sorted(list(set(asset for pair_string in lag_pairs['pair'] for asset in pair_string.split(' - '))))
    all_X, all_y = [], []
    for asset_name in lag_assets:
        asset_id = ASSET_TO_INDEX[asset_name]
        proxy_target_row = lag_pairs.filter(pl.col('pair').str.starts_with(asset_name)).head(1)
        if proxy_target_row.height == 0: continue
        proxy_target_col = proxy_target_row['target'][0]
        asset_feature_cols = [f"{asset_name}_{gf}" for gf in GENERIC_FEATURES]
        if not all(c in features.columns for c in asset_feature_cols): continue
        asset_features = features.select(['date_id'] + asset_feature_cols)
        rename_dict = {old: new for old, new in zip(asset_feature_cols, GENERIC_FEATURES)}
        asset_features = asset_features.rename(rename_dict)
        asset_labels = labels.select(['date_id', proxy_target_col]).rename({proxy_target_col: 'target'})
        merged = asset_features.join(asset_labels, on='date_id', how='inner').drop_nulls()
        merged = merged.with_columns(pl.lit(asset_id).cast(pl.Int32).alias('asset_id'))
        if merged.height > 0:
            all_y.append(merged.get_column('target').to_pandas())
            all_X.append(merged.drop(['date_id', 'target']).to_pandas())
    if not all_X: return pd.DataFrame(), pd.Series()
    return pd.concat(all_X, ignore_index=True), pd.concat(all_y, ignore_index=True)

# --- 3. Optuna 目标函数 ---

def objective(trial: optuna.Trial, features_df: pl.DataFrame, labels_df: pl.DataFrame, lag: int):
    params = {
        'objective': 'regression_l1', 'metric': 'mae', 'n_estimators': 1000,
        'learning_rate': trial.suggest_float('learning_rate', 1e-3, 1e-1, log=True),
        'num_leaves': trial.suggest_int('num_leaves', 20, 300),
        'max_depth': trial.suggest_int('max_depth', 3, 12),
        'subsample': trial.suggest_float('subsample', 0.6, 1.0),
        'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
        'reg_alpha': trial.suggest_float('reg_alpha', 50.0, 100.0),
        'reg_lambda': trial.suggest_float('reg_lambda', 50.0, 100.0),
        'n_jobs': -1, 'seed': 42, 'boosting_type': 'gbdt', 'device': 'gpu',
    }
    X, y = prepare_training_data_for_lag(features_df, labels_df, lag)
    if X.empty:
        print(f"警告: lag={lag} 没有生成训练数据，跳过此次试验。")
        return float('inf')
    train_end_idx = int(len(X) * 0.8)
    X_train, X_val = X.iloc[:train_end_idx], X.iloc[train_end_idx:]
    y_train, y_val = y.iloc[:train_end_idx], y.iloc[train_end_idx:]
    model = lgb.LGBMRegressor(**params)
    model.fit(X_train, y_train, eval_set=[(X_val, y_val)], eval_metric='mae',
              callbacks=[lgb.early_stopping(100, verbose=False)],
              categorical_feature=['asset_id'])
    preds = model.predict(X_val)
    mae = mean_absolute_error(y_val, preds)
    return mae

# --- 4. 主执行逻辑 ---

if __name__ == '__main__':
    print("="*50)
    print("开始独立的超参数优化...")
    print("="*50)
    
    try:
        train_raw = cast_and_clean(pl.read_csv(os.path.join(BASE_PATH, 'train.csv')).head(1500))
        labels_raw = cast_and_clean(pl.read_csv(os.path.join(BASE_PATH, 'train_labels.csv')), is_label_df=True)
    except FileNotFoundError:
        print(f"错误: 无法在 '{BASE_PATH}' 中找到 train.csv 或 train_labels.csv。")
        sys.exit(1)

    print("计算特征...")
    features = calculate_features(train_raw)
    
    LAG_TO_TUNE = 1
    
    study = optuna.create_study(direction='minimize')
    print(f"开始对 lag={LAG_TO_TUNE} 进行 {50} 次试验...")
    study.optimize(lambda trial: objective(trial, features, labels_raw, LAG_TO_TUNE), n_trials=50)
    
    print("\n" + "="*50)
    print("优化完成！")
    print(f"最佳试验分数 (MAE): {study.best_value}")
    print("最佳超参数 (请复制到您的主提交脚本中):")
    
    best_params_str = "{\n"
    # 添加固定的、非优化的参数
    fixed_params = {
        'objective': 'regression_l1', 'metric': 'mae', 'n_estimators': 2000,
        'n_jobs': -1, 'seed': 42, 'boosting_type': 'gbdt', 'device': 'gpu',
    }
    for key, value in fixed_params.items():
         best_params_str += f"    '{key}': '{value}',\n" if isinstance(value, str) else f"    '{key}': {value},\n"

    # 添加Optuna找到的参数
    for key, value in study.best_params.items():
        best_params_str += f"    '{key}': {value},\n"
    best_params_str += "}"
    print(best_params_str)
    
    print("="*50)