# ==============================================================================
# MITSUI & CO. Commodity Prediction - Intelligent Baseline Submission (V1.3.3)
#
# Strategy: Lagged Rank Momentum with Online Updates
# Author: Your Expert ML Advisor (in collaboration with you)
#
# Rationale:
# 1. Metric-Aware: Focus on stable, accurate ranking for the Spearman Sharpe Ratio.
# 2. EDA-Driven: Exploits the observed daily rank persistence (~0.45).
# 3. API-Compliant: Efficient online learning within the 1-minute time limit.
#
# V1.1: Fixed vertical stacking bug with horizontal `join`.
# V1.2: Fixed `AttributeError` with correct Polars horizontal ranking.
# V1.3: Added `.fill_null()` to prevent NaN propagation.
# V1.3.1: Added dynamic path correction for local module import.
# V1.3.2: Added a local scorer for immediate feedback.
# V1.3.3 (CRITICAL FIX FOR SCORER):
# - Created a robust, self-contained scorer function (`robust_local_scorer`)
#   that handles days where all target values are NaN.
# - The new scorer uses a try-except block to return np.nan for such days
#   instead of crashing, mimicking the behavior of a real competition environment.
# ==============================================================================

import os
import sys
import numpy as np
import pandas as pd
import polars as pl

# ------------------------------------------------------------------------------
# 0. 动态路径修正 (Dynamic Path Correction for Local Execution)
# ------------------------------------------------------------------------------
try:
    current_script_path = os.path.dirname(os.path.abspath(__file__))
except NameError:
    current_script_path = os.getcwd()

project_root = os.path.abspath(os.path.join(current_script_path, '..', '..'))
module_path = os.path.join(project_root, 'input', 'mitsui-commodity-prediction-challenge')

if module_path not in sys.path:
    print(f"将模块路径添加到 sys.path: {module_path}")
    sys.path.insert(0, module_path)

try:
    import kaggle_evaluation.mitsui_inference_server
    print("成功导入 mitsui_inference_server。")
except ImportError as e:
    print(f"导入失败。错误: {e}")

# ------------------------------------------------------------------------------
# 1. 智能环境检测与路径管理 (Smart Environment Detection)
# ------------------------------------------------------------------------------
def get_base_path():
    if 'google.colab' in sys.modules:
        print("检测到 Google Colab 环境。")
        return '/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge'
    elif os.path.exists('/kaggle/input'):
        print("检测到 Kaggle 环境。")
        return '/kaggle/input/mitsui-commodity-prediction-challenge'
    else:
        print("未检测到云环境，假定为本地环境。")
        return os.path.join(project_root, 'input', 'mitsui-commodity-prediction-challenge')

# ------------------------------------------------------------------------------
# 2. 全局状态管理 (Global State Management)
# ------------------------------------------------------------------------------
PREVIOUS_DAY_RANKS = {}
NUM_TARGET_COLUMNS = 424

# ------------------------------------------------------------------------------
# 3. 核心预测函数 (The `predict` Function)
# ------------------------------------------------------------------------------
def predict(
    test: pl.DataFrame,
    label_lags_1_batch: pl.DataFrame,
    label_lags_2_batch: pl.DataFrame,
    label_lags_3_batch: pl.DataFrame,
    label_lags_4_batch: pl.DataFrame,
) -> pl.DataFrame | pd.DataFrame:
    global PREVIOUS_DAY_RANKS
    lag_batches = [label_lags_1_batch, label_lags_2_batch, label_lags_3_batch, label_lags_4_batch]
    non_empty_lags = [df for df in lag_batches if df is not None and df.height > 0]
    if non_empty_lags:
        all_lags = non_empty_lags[0]
        for next_df in non_empty_lags[1:]:
            all_lags = all_lags.join(next_df, on=['date_id', 'label_date_id'], how='left')
        if all_lags.height > 0:
            latest_label_date = all_lags['label_date_id'].max()
            latest_labels = all_lags.filter(pl.col('label_date_id') == latest_label_date)
            target_cols = [col for col in latest_labels.columns if col.startswith('target_')]
            latest_targets_only = latest_labels[target_cols]
            if latest_targets_only.height > 0:
                transposed_df = latest_targets_only.transpose()
                ranked_transposed = transposed_df.select(pl.all().rank(method='average', descending=False))
                ranks_df = ranked_transposed.transpose()
                ranks_df.columns = latest_targets_only.columns
                median_rank_fill_value = NUM_TARGET_COLUMNS / 2
                ranks_df = ranks_df.fill_null(median_rank_fill_value)
                PREVIOUS_DAY_RANKS = ranks_df.to_dict(as_series=False)
                for key, value in PREVIOUS_DAY_RANKS.items():
                    PREVIOUS_DAY_RANKS[key] = value[0]
    if not PREVIOUS_DAY_RANKS:
        print("首次预测：使用基于索引的初始排名。")
        predictions = pl.DataFrame({f'target_{i}': float(i) for i in range(NUM_TARGET_COLUMNS)})
    else:
        median_rank_fallback = NUM_TARGET_COLUMNS / 2
        preds_dict = {f'target_{i}': PREVIOUS_DAY_RANKS.get(f'target_{i}', median_rank_fallback) for i in range(NUM_TARGET_COLUMNS)}
        predictions = pl.DataFrame(preds_dict)
    assert isinstance(predictions, (pd.DataFrame, pl.DataFrame))
    assert len(predictions) == 1
    assert len(predictions.columns) == NUM_TARGET_COLUMNS
    return predictions

# ------------------------------------------------------------------------------
# 5. 健壮的本地评分函数 (Robust Local Scorer Function)
# ------------------------------------------------------------------------------
def robust_local_scorer(base_data_path, submission_path):
    """
    加载提交文件和真实标签，并使用一个健壮的、能处理全NaN日期的评分逻辑来计算分数。
    """
    print("\n" + "="*50)
    print("启动本地评分器...")
    print("="*50)

    try:
        # 加载文件
        submission_df = pd.read_parquet(submission_path)
        solution_df_full = pd.read_csv(os.path.join(base_data_path, 'train_labels.csv'))
        
        # 准备数据
        submitted_dates = submission_df['date_id'].unique()
        solution_df = solution_df_full[solution_df_full['date_id'].isin(submitted_dates)].copy()
        
        # 将提交文件的列名从 'target_' 重命名为 'prediction_'
        submission_df = submission_df.rename(columns={col: col.replace('target_', 'prediction_') for col in submission_df.columns if col.startswith('target_')})
        
        # 合并预测和真实标签
        merged_df = pd.merge(solution_df, submission_df, on='date_id')
        
        # --- 健壮的评分逻辑 ---
        prediction_cols = [col for col in merged_df.columns if col.startswith('prediction_')]
        target_cols = [col for col in merged_df.columns if col.startswith('target_')]

        def _robust_compute_rank_correlation(row):
            try:
                non_null_targets = [col for col in target_cols if not pd.isnull(row[col])]
                if not non_null_targets:
                    # 如果没有非空目标，返回NaN，而不是抛出异常
                    return np.nan
                
                matching_predictions = [col for col in prediction_cols if col.replace('prediction', 'target') in non_null_targets]
                
                # 检查标准差是否为零
                if row[non_null_targets].std(ddof=0) == 0 or row[matching_predictions].std(ddof=0) == 0:
                    return np.nan # 如果任一标准差为零，也无法计算相关性

                return np.corrcoef(row[matching_predictions].rank(method='average'), row[non_null_targets].rank(method='average'))[0, 1]
            except Exception:
                # 捕获任何其他意外错误，并为该天返回NaN
                return np.nan

        daily_rank_corrs = merged_df.apply(_robust_compute_rank_correlation, axis=1)
        
        # 移除无法计算相关性的日子
        daily_rank_corrs = daily_rank_corrs.dropna()

        if len(daily_rank_corrs) == 0:
             print("\n警告: 没有任何一天可以成功计算相关性分数。")
             local_score = 0.0
        else:
            std_dev = daily_rank_corrs.std(ddof=0)
            if std_dev == 0 or pd.isnull(std_dev):
                # 如果所有每日相关性都相同，标准差为0，夏普比率为无穷大，按规则应处理
                print("\n警告: 每日相关性的标准差为0。无法计算夏普比率。")
                local_score = 0.0 # 或者可以返回一个大数，但0更安全
            else:
                sharpe_ratio = daily_rank_corrs.mean() / std_dev
                local_score = float(sharpe_ratio)

        print("\n" + "*"*50)
        print(f"本地模拟评分完成！")
        print(f"在 {len(daily_rank_corrs)}/{len(merged_df)} 个可评分日上计算...")
        print(f"您的本地分数是: {local_score:.4f}")
        print("*"*50)

    except FileNotFoundError as e:
        print(f"\n错误: 找不到文件 '{e.filename}'。")
    except Exception as e:
        print(f"\n本地评分时发生未知错误: {e}")

# ------------------------------------------------------------------------------
# 4. API服务器初始化与主执行逻辑 (Main Execution Logic)
# ------------------------------------------------------------------------------
if __name__ == "__main__":
    is_kaggle_rerun = os.getenv('KAGGLE_IS_COMPETITION_RERUN')
    print("正在初始化API服务器...")
    try:
        inference_server = kaggle_evaluation.mitsui_inference_server.MitsuiInferenceServer(predict)
        if is_kaggle_rerun:
            print("检测到Kaggle重跑环境，启动服务器。")
            inference_server.serve()
        else:
            print("在本地或交互式环境中运行，启动本地网关。")
            base_data_path = get_base_path()
            if os.path.exists(base_data_path):
                 inference_server.run_local_gateway((base_data_path,))
                 submission_file_path = os.path.join(current_script_path, 'submission.parquet')
                 if os.path.exists(submission_file_path):
                     robust_local_scorer(base_data_path, submission_file_path)
                 else:
                     print(f"\n警告: 未找到生成的 `submission.parquet` 文件。跳过本地评分。")
            else:
                print(f"错误：本地数据路径 '{base_data_path}' 不存在。无法启动本地网关。")
    except NameError:
        print("错误: `MitsuiInferenceServer` 未定义。")
    except Exception as e:
        print(f"初始化或运行服务器时发生未知错误: {e}")
    print("脚本执行完毕。")