#!/usr/bin/env python3
"""
MITSUI Commodity Prediction Challenge - Enhanced Dual-Stage Pipeline
================================================================

This is the most advanced version implementing all improvements from 
plan/script_improvement.md for maximum performance and robustness.

Architecture:
- Stage-A: Asset-space forecasters (Ridge + MLP per lag)
- Stage-B: Target mapping + adaptive rank shrinkage + deterministic tie-breaking
- Dual-channel online updates: target-side + asset-side
- Compliant momentum using only lagged test labels
- Robust preprocessing with group cross-sectional normalization
- Strict artifact management with fail-fast loading

Based on:
- plan/script_improvement.md complete optimization roadmap
- src/algo/1.828.asset_mapping_momentum_baseline_only.py successful framework
- Official src/utils/ evaluation metrics

Author: Commodity Prediction Team  
Date: July 2025
Version: Enhanced v2.0
"""

import warnings
warnings.filterwarnings('ignore')

import os
import json
import pickle
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Callable
from collections import defaultdict, deque

import numpy as np
import pandas as pd
import polars as pl
from scipy import stats
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_squared_error
from sklearn.neural_network import MLPRegressor

# Import official Kaggle evaluation API
import kaggle_evaluation.mitsui_inference_server

# ============================================================================
# 0) ENHANCED CONFIGURATION
# ============================================================================

class Config:
    """Enhanced configuration with all improvement parameters"""
    
    # Reproducibility and toggles
    GLOBAL_SEED = 2025
    N_SEEDS = 2
    MLP_TOGGLE = True  # Now enabled for dual-model architecture
    
    # Data ranges and lags (from arrival audit)
    PUBLIC_HOLDOUT = [1827, 1916]  # Never used for tuning
    MAX_LAG = 4
    LAG_OFFSETS = {1: 2, 2: 3, 3: 4, 4: 5}  # Verified constants
    
    # CV & purging
    PURGE_GAP = 10  # days
    FOLDS = 4  # time-blocked folds over [0, 1826]
    
    # Features & preprocessing
    DROP_FEATURES = [
        'US_Stock_GOLD_adj_open', 'US_Stock_GOLD_adj_high', 
        'US_Stock_GOLD_adj_low', 'US_Stock_GOLD_adj_close', 
        'US_Stock_GOLD_adj_volume'
    ]  # All-NaN in test
    FFILL_CAP = 10  # days (within-series forward fill cap)
    WINSOR_K_MAD = 5  # clip at ±5× MAD
    
    # Stage-A models (asset forecasters per lag)
    RIDGE_ALPHAS = [3e-3, 1e-2, 3e-2]  # Grid search
    MLP_HIDDEN = [256, 128]  # Hidden layer dimensions
    MLP_DROPOUT = 0.1
    MLP_LR = 1e-3
    MLP_WEIGHT_DECAY = 1e-4  
    MLP_EPOCHS = 40
    MLP_EARLY_STOP = 6
    RIDGE_MLP_WEIGHTS = [0.6, 0.4]  # Ridge:MLP fusion
    
    # Stage-B post-processing (rank + shrinkage)
    SHRINK_A_INIT = 0.15
    SHRINK_B_INIT = 0.25
    SHRINK_G_WEIGHTS = [0.5, 0.5]  # n_avail vs D_feat
    
    # Online learning (dual-channel)
    ROLL_UPDATE_WINDOW = 90  # days
    N_MIN_UPDATE = 20  # minimum targets for asset-side update
    RECENCY_HALF_LIFE = 30  # days (EMA weights)
    LAG_WEIGHTS = {1: 1.0, 2: 0.9, 3: 0.8, 4: 0.7}
    TIME_BUDGET_PER_DAY = 60  # seconds
    RLS_LAMBDA = 1e-2  # Recursive least squares regularization
    RLS_REFACTOR_DAYS = 14  # Numerical stability refactoring
    
    # Robustness
    TIE_EPSILON = 1e-6  # Deterministic tie-breaking
    SOLUTION_NULL_FILLER = -999999  # From official utils

# ============================================================================
# 1) ENHANCED UTILITY FUNCTIONS
# ============================================================================

def get_data_path():
    """Auto-detect platform-specific data paths"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if kaggle_path.exists():
        return kaggle_path
    elif colab_path.exists():
        return colab_path
    else:
        return local_path

def set_global_seed(seed: int) -> None:
    """Set all RNGs for reproducibility"""
    np.random.seed(seed)
    import random
    random.seed(seed)
    
    # Set additional seeds for sklearn/neural networks if available
    # PyTorch seeding (if available)
    import torch
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

def deterministic_hash(date_id: int, target_id: int, seed: int = Config.GLOBAL_SEED) -> float:
    """Generate deterministic hash for tie-breaking"""
    hash_input = f"{seed}_{date_id}_{target_id}".encode()
    hash_value = int(hashlib.md5(hash_input).hexdigest()[:8], 16)
    return (hash_value % 1000000) / 1000000.0  # Normalize to [0, 1)

def save_artifact(name: str, obj: Any, working_dir: Path = Path("/kaggle/working")) -> None:
    """Save artifact with enhanced error handling"""
    working_dir.mkdir(exist_ok=True)
    
    try:
        if name.endswith('.json'):
            with open(working_dir / name, 'w') as f:
                json.dump(obj, f, indent=2, default=str)
        elif name.endswith('.pkl'):
            with open(working_dir / name, 'wb') as f:
                pickle.dump(obj, f, protocol=pickle.HIGHEST_PROTOCOL)
        elif name.endswith('.npz'):
            if isinstance(obj, dict) and all(isinstance(k, int) for k in obj.keys()):
                obj_str_keys = {f"lag_{k}": v for k, v in obj.items()}
                np.savez_compressed(working_dir / name, **obj_str_keys)
            else:
                np.savez_compressed(working_dir / name, **obj)
        print(f"  ✅ Saved {name}")
    except Exception as e:
        print(f"  ❌ Failed to save {name}: {e}")
        raise

def load_artifact(name: str, working_dir: Path = Path("/kaggle/working")) -> Any:
    """Load artifact with strict error handling"""
    filepath = working_dir / name
    
    if not filepath.exists():
        raise FileNotFoundError(f"Critical artifact missing: {name}")
    
    try:
        if name.endswith('.json'):
            with open(filepath, 'r') as f:
                return json.load(f)
        elif name.endswith('.pkl'):
            with open(filepath, 'rb') as f:
                return pickle.load(f)
        elif name.endswith('.npz'):
            data = dict(np.load(filepath))
            # Convert back lag_X keys to integers if they exist
            if all(k.startswith('lag_') for k in data.keys()):
                return {int(k.split('_')[1]): v for k, v in data.items()}
            else:
                return data
    except Exception as e:
        raise RuntimeError(f"Failed to load critical artifact {name}: {e}")

# ============================================================================
# 2) TARGET ORDER MANAGEMENT
# ============================================================================

def build_target_order(pairs_df: pd.DataFrame) -> List[str]:
    """Build fixed target order: target_0 to target_423"""
    target_order = sorted(pairs_df['target'].unique(), 
                         key=lambda s: int(s.split('_')[1]))
    
    # Validation
    assert len(target_order) == 424, f"Expected 424 targets, got {len(target_order)}"
    assert target_order[0] == 'target_0', f"First target should be target_0, got {target_order[0]}"
    assert target_order[-1] == 'target_423', f"Last target should be target_423, got {target_order[-1]}"
    
    return target_order

def validate_target_order(predictions: pd.DataFrame, target_order: List[str]) -> None:
    """Validate prediction DataFrame follows target order"""
    pred_cols = list(predictions.columns)
    if pred_cols != target_order:
        raise ValueError(f"Prediction columns don't match target_order. Expected: {target_order[:5]}..., Got: {pred_cols[:5]}...")

# ============================================================================
# 3) COMPLIANT LAGGED LABEL INGESTION
# ============================================================================

def load_test_labels_lag(lag: int, data_path: Path) -> Optional[pd.DataFrame]:
    """Load test labels for specific lag"""
    try:
        filepath = data_path / f"test_labels_lag_{lag}.csv"
        if filepath.exists():
            return pd.read_csv(filepath)
        return None
    except Exception as e:
        print(f"Warning: Could not load test_labels_lag_{lag}: {e}")
        return None

def ingest_arrived_labels(date_id: int, 
                         lag_batches: Dict[int, pl.DataFrame],
                         target_order: List[str]) -> Tuple[np.ndarray, Dict[int, int]]:
    """
    Ingest only current day's arrived labels from lag files
    Returns: (prior_ranks, coverage_by_lag)
    """
    # Initialize prior ranks with neutral values
    prior_ranks = np.arange(424, dtype=float)
    coverage_by_lag = {}
    
    # Track all observed values across lags
    all_observations = {}  # target -> (value, weight)
    
    for lag in [1, 2, 3, 4]:
        if lag not in lag_batches:
            raise ValueError(f"Lag batch {lag} not provided")
        
        if lag_batches[lag] is None:
            coverage_by_lag[lag] = 0
            continue
        
        # Convert to pandas for easier processing
        lag_df = lag_batches[lag].to_pandas()
        
        # Find current day's row
        current_rows = lag_df[lag_df['date_id'] == date_id]
        
        if len(current_rows) == 0:
            coverage_by_lag[lag] = 0
            continue
        
        current_row = current_rows.iloc[0]
        
        # Extract available targets for this lag (simplified filter)
        target_cols = [col for col in lag_df.columns if col.startswith('target_')]
        
        # First pass: count available targets
        n_available = 0
        for target_col in target_cols:
            if target_col in current_row.index and not pd.isna(current_row[target_col]):
                n_available += 1
        
        # Calculate strength based on current day availability
        strength = n_available / 106.0
        weight = Config.LAG_WEIGHTS[lag] * strength
        
        # Second pass: accumulate values with correct weight
        for target_col in target_cols:
            if target_col in current_row.index and not pd.isna(current_row[target_col]):
                value = current_row[target_col]
                # Apply robust clipping
                value = np.clip(value, -5, 5)  # ±5 sigma clipping
                
                # Weighted accumulation
                if target_col in all_observations:
                    old_val, old_weight = all_observations[target_col]
                    total_weight = old_weight + weight
                    new_val = (old_val * old_weight + value * weight) / total_weight
                    all_observations[target_col] = (new_val, total_weight)
                else:
                    all_observations[target_col] = (value, weight)
        
        coverage_by_lag[lag] = n_available
    
    # Convert observations to ranks
    if len(all_observations) > 1:
        # Extract values in target_order
        values = []
        available_mask = []
        
        for target in target_order:
            if target in all_observations:
                values.append(all_observations[target][0])
                available_mask.append(True)
            else:
                values.append(0.0)  # Neutral for missing
                available_mask.append(False)
        
        values = np.array(values)
        available_mask = np.array(available_mask)
        
        # Rank only available targets
        if available_mask.sum() > 1:
            available_ranks = stats.rankdata(values[available_mask], method='average')
            
            # Create full rank array with median ranks for missing
            median_rank = len(target_order) / 2.0
            full_ranks = np.full(len(target_order), median_rank)
            full_ranks[available_mask] = available_ranks
            
            # Re-rank entire array to ensure 1-424 range
            prior_ranks = stats.rankdata(full_ranks, method='ordinal').astype(float)
    
    return prior_ranks, coverage_by_lag

# ============================================================================
# 4) TWO-STAGE PREPROCESSING PIPELINE
# ============================================================================

class FeaturePreprocessor:
    """Two-stage feature preprocessing for training/inference consistency"""
    
    def __init__(self):
        self.scalers = {}
        self.group_masks = {}
        self.feature_list = []  # Original features (without missing flags)
        self.final_feature_list = []  # Final features (with missing flags)
        self.fitted = False
    
    def preprocess_fit(self, train_df: pd.DataFrame) -> pd.DataFrame:
        """Stage 1: Fit preprocessing parameters on training data"""
        print("  Fitting preprocessing pipeline...")
        
        # Step 1: Remove dropped features
        feature_cols = [col for col in train_df.columns if col not in Config.DROP_FEATURES]
        df_clean = train_df[feature_cols].copy()
        self.feature_list = [col for col in feature_cols if col != 'date_id']
        
        # Step 2: Transform to returns/diffs
        df_ret = self._to_returns(df_clean)
        
        # Step 3: Fit robust scalers (median/IQR) per series  
        self.scalers = self._fit_robust_scalers(df_ret, (0, 1826))
        
        # Step 4: Build group masks for cross-sectional normalization
        self.group_masks = self._infer_feature_groups(self.feature_list)
        
        # Mark as fitted before applying transformation
        self.fitted = True
        
        # Step 5: Apply full transformation
        df_transformed = self.preprocess_transform(df_ret, already_returns=True)
        
        # Step 6: Save final feature list (with missing flags)
        self.final_feature_list = [col for col in df_transformed.columns if col != 'date_id']
        
        print(f"    ✅ Preprocessing fitted on {len(self.feature_list)} base features, {len(self.final_feature_list)} total features")
        return df_transformed
    
    def preprocess_transform(self, df: pd.DataFrame, already_returns: bool = False) -> pd.DataFrame:
        """Stage 2: Apply preprocessing transformation"""
        if not self.fitted:
            raise RuntimeError("Must call preprocess_fit() before preprocess_transform()")
        
        # Ensure same feature columns
        if 'date_id' in df.columns:
            feature_cols = ['date_id'] + self.feature_list
            df_clean = df[feature_cols].copy()
        else:
            df_clean = df[self.feature_list].copy()
        
        # Apply transformations in sequence
        # Only convert to returns if not already done
        df_ret = df_clean if already_returns else self._to_returns(df_clean)
        df_scaled = self._transform_robust(df_ret, self.scalers)
        df_norm = self._group_normalize_per_day(df_scaled, self.group_masks)
        
        # Save original missing mask before ffill
        orig_nan_mask = df_norm.isna()
        
        df_filled = self._ffill_capped(df_norm, Config.FFILL_CAP)
        df_with_flags = self._add_missing_flags(df_filled, orig_nan_mask)  # Use original mask
        df_final = self._winsorize(df_with_flags, Config.WINSOR_K_MAD)
        
        # Ensure consistent output columns in the correct order
        if hasattr(self, 'final_feature_list') and len(self.final_feature_list) > 0:
            output_cols = ['date_id'] if 'date_id' in df_final.columns else []
            output_cols.extend(self.final_feature_list)
            
            # Add missing columns with default values
            for col in output_cols:
                if col not in df_final.columns and col != 'date_id':
                    df_final[col] = 0.0  # Default value for missing features
            
            # Select and reorder columns
            df_final = df_final[output_cols]
        
        return df_final
    
    def _to_returns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform price-like to log-returns, volume to log-diffs"""
        df_ret = df.copy()
        
        for col in df.columns:
            if col == 'date_id':
                continue
            
            # Volume/open interest to log-diffs
            if any(x in col.lower() for x in ['volume', 'open_interest']):
                df_ret[col] = np.log(df[col] / df[col].shift(1))
            # Price-like to log-returns
            elif any(x in col.lower() for x in ['close', 'open', 'high', 'low']):
                df_ret[col] = np.log(df[col] / df[col].shift(1))
            # Keep other features as-is (ratios, indicators, etc.)
        
        return df_ret
    
    def _fit_robust_scalers(self, df_ret: pd.DataFrame, date_range: Tuple[int, int]) -> Dict[str, Dict[str, float]]:
        """Fit robust scalers (median/IQR) per series"""
        start_date, end_date = date_range
        train_data = df_ret[(df_ret['date_id'] >= start_date) & (df_ret['date_id'] <= end_date)]
        
        scalers = {}
        for col in df_ret.columns:
            if col == 'date_id':
                continue
            
            values = train_data[col].dropna()
            if len(values) > 0:
                median = values.median()
                iqr = values.quantile(0.75) - values.quantile(0.25)
                iqr = max(iqr, 1e-8)  # Avoid division by zero
                scalers[col] = {'median': median, 'iqr': iqr}
            else:
                scalers[col] = {'median': 0.0, 'iqr': 1.0}
        
        return scalers
    
    def _transform_robust(self, df_ret: pd.DataFrame, scalers: Dict[str, Dict[str, float]]) -> pd.DataFrame:
        """Apply robust z-score transformation"""
        df_scaled = df_ret.copy()
        
        for col in df_ret.columns:
            if col == 'date_id':
                continue
            
            if col in scalers:
                median = scalers[col]['median']
                iqr = scalers[col]['iqr']
                df_scaled[col] = (df_ret[col] - median) / iqr
        
        return df_scaled
    
    def _infer_feature_groups(self, columns: List[str]) -> Dict[str, np.ndarray]:
        """Build boolean masks for asset groups"""
        groups = {}
        groups['LME'] = np.array([col.startswith('LME_') for col in columns])
        groups['JPX'] = np.array([col.startswith('JPX_') for col in columns])
        groups['FX'] = np.array([col.startswith('FX_') for col in columns])
        groups['US_Stock'] = np.array([col.startswith('US_Stock_') for col in columns])
        return groups
    
    def _group_normalize_per_day(self, df_scaled: pd.DataFrame, group_masks: Dict[str, np.ndarray]) -> pd.DataFrame:
        """Per-day, within-group cross-sectional normalization"""
        df_norm = df_scaled.copy()
        feature_cols = [col for col in df_scaled.columns if col != 'date_id']
        
        for idx, row in df_scaled.iterrows():
            for group_name, mask in group_masks.items():
                if len(mask) != len(feature_cols):
                    continue  # Skip if mask size doesn't match
                
                group_cols = [col for i, col in enumerate(feature_cols) if mask[i]]
                if len(group_cols) > 1:
                    group_values = row[group_cols].values
                    valid_mask = ~np.isnan(group_values)
                    
                    if valid_mask.sum() > 1:
                        mean_val = np.nanmean(group_values)
                        std_val = np.nanstd(group_values)
                        if std_val > 1e-8:
                            group_values = (group_values - mean_val) / std_val
                            for i, col in enumerate(group_cols):
                                df_norm.at[idx, col] = group_values[i]
        
        return df_norm
    
    def _ffill_capped(self, df_norm: pd.DataFrame, cap: int = 10) -> pd.DataFrame:
        """Forward fill with capped lookback"""
        df_filled = df_norm.copy()
        
        for col in df_norm.columns:
            if col == 'date_id':
                continue
            df_filled[col] = df_norm[col].fillna(method='ffill', limit=cap)
        
        return df_filled
    
    def _add_missing_flags(self, df_filled: pd.DataFrame, orig_nan_mask: pd.DataFrame = None) -> pd.DataFrame:
        """Add missing value indicator features based on original missing data"""
        df_with_flags = df_filled.copy()
        
        # Add missing flags for all feature_list columns consistently
        for col in self.feature_list:
            flag_col = f"{col}_missing"
            if col in df_filled.columns:
                if orig_nan_mask is not None and col in orig_nan_mask.columns:
                    # Use original missing mask before ffill
                    df_with_flags[flag_col] = orig_nan_mask[col].astype(int)
                else:
                    # Fallback to checking filled data (for backward compatibility)
                    df_with_flags[flag_col] = df_filled[col].isna().astype(int)
            else:
                # If column not present, assume all missing
                df_with_flags[flag_col] = 1
        
        return df_with_flags
    
    def _winsorize(self, df_filled: pd.DataFrame, k: float = 5.0) -> pd.DataFrame:
        """Winsorize extremes at ±k× MAD"""
        df_wins = df_filled.copy()
        
        for col in df_filled.columns:
            if col == 'date_id' or col.endswith('_missing'):
                continue
            
            values = df_filled[col].dropna()
            if len(values) > 0:
                median = values.median()
                mad = np.median(np.abs(values - median))
                if mad > 1e-8:
                    lower_bound = median - k * mad
                    upper_bound = median + k * mad
                    df_wins[col] = df_filled[col].clip(lower_bound, upper_bound)
        
        return df_wins

def compute_D_feat(df_processed: pd.DataFrame, group_masks: Dict[str, np.ndarray], date_id: int) -> float:
    """Compute feature-side dispersion proxy for day t"""
    try:
        row = df_processed[df_processed['date_id'] == date_id]
        if len(row) == 0:
            return 0.5  # Neutral value
        
        row = row.iloc[0]
        feature_cols = [col for col in df_processed.columns 
                       if col != 'date_id' and not col.endswith('_missing')]
        
        # Compute median absolute deviation across feature groups
        group_deviations = []
        for group_name, mask in group_masks.items():
            if len(mask) != len(feature_cols):
                continue
            
            group_cols = [col for i, col in enumerate(feature_cols) if mask[i]]
            if len(group_cols) > 1:
                group_values = row[group_cols].values
                valid_values = group_values[~np.isnan(group_values)]
                if len(valid_values) > 1:
                    mad = np.median(np.abs(valid_values - np.median(valid_values)))
                    group_deviations.append(mad)
        
        if len(group_deviations) > 0:
            return np.median(group_deviations)
        else:
            return 0.5
    except:
        return 0.5

# ============================================================================
# 5) ASSET MAPPING AND RECOVERY SYSTEM
# ============================================================================

def build_assets_and_M(pairs_df: pd.DataFrame) -> Tuple[List[str], Dict[int, np.ndarray], Dict[int, List[str]]]:
    """
    Build complete asset universe and mapping matrices
    Returns: (assets, M_by_lag, targets_by_lag)
    """
    # Extract asset universe
    assets = set()
    for _, row in pairs_df.iterrows():
        pair_str = row['pair']
        if ' - ' not in pair_str:
            assets.add(pair_str.strip())
        else:
            parts = pair_str.split(' - ')
            if len(parts) == 2:
                assets.add(parts[0].strip())
                assets.add(parts[1].strip())
    
    assets = sorted(list(assets))
    asset_to_idx = {asset: i for i, asset in enumerate(assets)}
    
    # Build mapping matrices per lag
    M_by_lag = {}
    targets_by_lag = {}
    
    for lag in [1, 2, 3, 4]:
        lag_pairs = pairs_df[pairs_df['lag'] == lag].copy()
        n_targets = len(lag_pairs)
        n_assets = len(assets)
        
        M = np.zeros((n_targets, n_assets))
        targets = []
        
        for target_idx, (_, row) in enumerate(lag_pairs.iterrows()):
            pair_str = row['pair']
            target_name = row['target']
            targets.append(target_name)
            
            if ' - ' not in pair_str:
                # Single asset
                asset = pair_str.strip()
                if asset in asset_to_idx:
                    M[target_idx, asset_to_idx[asset]] = 1.0
            else:
                # Pair "A - B"
                parts = pair_str.split(' - ')
                if len(parts) == 2:
                    asset_a, asset_b = parts[0].strip(), parts[1].strip()
                    if asset_a in asset_to_idx:
                        M[target_idx, asset_to_idx[asset_a]] = 1.0
                    if asset_b in asset_to_idx:
                        M[target_idx, asset_to_idx[asset_b]] = -1.0
        
        M_by_lag[lag] = M
        targets_by_lag[lag] = targets
    
    print(f"  Built asset universe: {len(assets)} assets")
    print(f"  Built mapping matrices: {list(M_by_lag.keys())} lags")
    
    return assets, M_by_lag, targets_by_lag

def build_solver(M_lag: np.ndarray, alpha: float = 1e-3) -> Callable:
    """Build Tikhonov regularized solver with missing value support"""
    def solve(y_obs: np.ndarray, mask: np.ndarray) -> Optional[np.ndarray]:
        """
        Solve min_r || W (M_lag r - y) ||² + α ||r||²
        where W is diagonal mask for observed targets
        """
        M_sub = M_lag[mask]
        y_sub = y_obs[mask]
        
        if len(y_sub) < 2:  # Need minimum observations
            return None
        
        # Tikhonov regularization: (M^T M + αI)^-1 M^T y
        MtM = M_sub.T @ M_sub
        MtM_reg = MtM + alpha * np.eye(MtM.shape[0])
        Mty = M_sub.T @ y_sub
        
        r_recovered = np.linalg.solve(MtM_reg, Mty)
        return r_recovered
    
    return solve

def recover_asset_labels(labels_df: pd.DataFrame, 
                        M_by_lag: Dict[int, np.ndarray],
                        solvers_by_lag: Dict[int, Callable],
                        targets_by_lag: Dict[int, List[str]]) -> Dict[int, Dict[int, np.ndarray]]:
    """Recover asset-space labels for training period [0, 1826]"""
    print("  Recovering asset-space labels...")
    
    r_by_lag = {}
    for lag in [1, 2, 3, 4]:
        r_by_lag[lag] = {}
        targets = targets_by_lag[lag]
        solver = solvers_by_lag[lag]
        
        recovered_count = 0
        for t in range(0, 1827):  # Training range only
            day_labels = labels_df[labels_df['date_id'] == t]
            if len(day_labels) == 0:
                continue
            
            day_row = day_labels.iloc[0]
            
            # Extract target values and create mask
            y_obs = np.array([
                day_row[target] if target in day_row.index and not pd.isna(day_row[target])
                else np.nan for target in targets
            ])
            
            mask = ~np.isnan(y_obs)
            
            # Solve for asset returns
            r_recovered = solver(y_obs, mask)
            if r_recovered is not None:
                r_by_lag[lag][t] = r_recovered
                recovered_count += 1
        
        print(f"    Lag {lag}: {recovered_count} days recovered")
    
    return r_by_lag

def standardize_asset_labels(r_by_lag: Dict[int, Dict[int, np.ndarray]], 
                           date_range: Tuple[int, int]) -> Tuple[Dict[int, Dict[int, np.ndarray]], Dict[int, np.ndarray], Dict[int, np.ndarray]]:
    """Standardize asset returns per lag"""
    start_date, end_date = date_range
    r_std = {}
    means = {}
    stds = {}
    
    for lag in r_by_lag.keys():
        # Collect all returns for this lag
        all_returns = []
        for t in range(start_date, end_date + 1):
            if t in r_by_lag[lag]:
                all_returns.append(r_by_lag[lag][t])
        
        if len(all_returns) > 0:
            all_returns = np.array(all_returns)
            mean_per_asset = np.nanmean(all_returns, axis=0)
            std_per_asset = np.nanstd(all_returns, axis=0)
            std_per_asset = np.maximum(std_per_asset, 1e-8)
            
            means[lag] = mean_per_asset
            stds[lag] = std_per_asset
            
            # Standardize
            r_std[lag] = {}
            for t in r_by_lag[lag].keys():
                r_std[lag][t] = (r_by_lag[lag][t] - mean_per_asset) / std_per_asset
        else:
            # Fallback for empty lags
            n_assets = len(next(iter(r_by_lag[lag].values()))) if r_by_lag[lag] else 106
            means[lag] = np.zeros(n_assets)
            stds[lag] = np.ones(n_assets)
            r_std[lag] = r_by_lag[lag].copy()
    
    return r_std, means, stds

# ============================================================================
# 6) STAGE-A DUAL MODEL ARCHITECTURE
# ============================================================================

class DualModelLag:
    """Ridge + MLP dual model for single lag"""
    
    def __init__(self, lag: int):
        self.lag = lag
        self.ridge = None
        self.mlp = None
        self.best_ridge_alpha = Config.RIDGE_ALPHAS[1]  # Default
        self.fusion_weights = Config.RIDGE_MLP_WEIGHTS.copy()
        
    def train(self, X: np.ndarray, y: np.ndarray, 
              folds: List[Tuple[np.ndarray, np.ndarray]]) -> Dict[str, Any]:
        """Train both Ridge and MLP with time-blocked CV"""
        
        # Handle NaN values
        X = np.nan_to_num(X, nan=0.0)
        y = np.nan_to_num(y, nan=0.0)
        
        results = {'ridge_scores': [], 'mlp_scores': []}
        
        # 1) Ridge grid search
        best_ridge_score = -np.inf
        for alpha in Config.RIDGE_ALPHAS:
            fold_scores = []
            
            for train_idx, valid_idx in folds:
                if len(train_idx) == 0 or len(valid_idx) == 0:
                    continue
                
                # Filter to available indices
                train_mask = train_idx < len(X)
                valid_mask = valid_idx < len(X)
                
                if train_mask.sum() == 0 or valid_mask.sum() == 0:
                    continue
                
                X_train = X[train_idx[train_mask]]
                y_train = y[train_idx[train_mask]]
                X_valid = X[valid_idx[valid_mask]]
                y_valid = y[valid_idx[valid_mask]]
                
                # Train Ridge
                ridge = Ridge(alpha=alpha, random_state=Config.GLOBAL_SEED)
                ridge.fit(X_train, y_train)
                
                # Evaluate
                y_pred = ridge.predict(X_valid)
                mse = mean_squared_error(y_valid, y_pred)
                fold_scores.append(-mse)  # Negative MSE for maximization
            
            if len(fold_scores) > 0:
                mean_score = np.mean(fold_scores)
                results['ridge_scores'].append((alpha, mean_score))
                
                if mean_score > best_ridge_score:
                    best_ridge_score = mean_score
                    self.best_ridge_alpha = alpha
        
        # Train final Ridge
        self.ridge = Ridge(alpha=self.best_ridge_alpha, random_state=Config.GLOBAL_SEED)
        self.ridge.fit(X, y)
        
        # 2) MLP training (if enabled)
        if Config.MLP_TOGGLE:
            try:
                self.mlp = MLPRegressor(
                    hidden_layer_sizes=Config.MLP_HIDDEN,
                    activation='relu',  # Using relu instead of gelu for sklearn compatibility
                    alpha=Config.MLP_WEIGHT_DECAY,
                    learning_rate_init=Config.MLP_LR,
                    max_iter=Config.MLP_EPOCHS,
                    random_state=Config.GLOBAL_SEED,
                    early_stopping=True,
                    validation_fraction=0.2,
                    n_iter_no_change=Config.MLP_EARLY_STOP
                )
                
                self.mlp.fit(X, y)
                results['mlp_trained'] = True
                
            except Exception as e:
                print(f"    Warning: MLP training failed for lag {self.lag}: {e}")
                self.mlp = None
                results['mlp_trained'] = False
        else:
            results['mlp_trained'] = False
        
        return results
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict using fusion of Ridge and MLP"""
        X = np.nan_to_num(X, nan=0.0)
        
        if X.ndim == 1:
            X = X.reshape(1, -1)
        
        # Ridge prediction
        ridge_pred = self.ridge.predict(X)
        # Ensure Ridge output is 1D for single sample
        if ridge_pred.ndim == 2 and ridge_pred.shape[0] == 1:
            ridge_pred = ridge_pred[0]
        
        # MLP prediction (if available)
        if self.mlp is not None:
            mlp_pred = self.mlp.predict(X)
            # Ensure MLP output is 1D for single sample
            if mlp_pred.ndim == 2 and mlp_pred.shape[0] == 1:
                mlp_pred = mlp_pred[0]
            # Fusion
            final_pred = (self.fusion_weights[0] * ridge_pred + 
                         self.fusion_weights[1] * mlp_pred)
        else:
            final_pred = ridge_pred
        
        # Final ensure 1D output
        return np.asarray(final_pred).reshape(-1)

def train_stage_a_models(X_processed: pd.DataFrame,
                        r_std_by_lag: Dict[int, Dict[int, np.ndarray]],
                        folds: List[Tuple[np.ndarray, np.ndarray]],
                        preprocessor: FeaturePreprocessor) -> Dict[int, DualModelLag]:
    """Train Stage-A dual models (Ridge + MLP) per lag"""
    print("  Training Stage-A dual models...")
    
    models_by_lag = {}
    # Use final feature list for consistency between training and inference
    if hasattr(preprocessor, 'final_feature_list') and len(preprocessor.final_feature_list) > 0:
        feature_cols = preprocessor.final_feature_list
    else:
        feature_cols = [col for col in X_processed.columns if col != 'date_id']
    
    for lag in [1, 2, 3, 4]:
        print(f"    Training models for lag {lag}...")
        
        # Prepare training data with explicit date ordering
        date_list = sorted(list(r_std_by_lag[lag].keys()))
        X_lag = []
        y_lag = []
        
        for t in date_list:
            day_features = X_processed[X_processed['date_id'] == t]
            if len(day_features) > 0:
                day_row = day_features.iloc[0]
                X_lag.append(day_row[feature_cols].values)
                y_lag.append(r_std_by_lag[lag][t])
        
        if len(X_lag) == 0:
            print(f"      No training data for lag {lag}, skipping...")
            continue
        
        X_lag = np.array(X_lag)
        y_lag = np.array(y_lag)
        
        # Build date_id to position mapping
        pos_of_date = {d: i for i, d in enumerate(date_list)}
        
        # Map folds from date_id to position indices
        mapped_folds = []
        for tr_ids, va_ids in folds:
            tr_pos = np.array([pos_of_date[d] for d in tr_ids if d in pos_of_date], dtype=int)
            va_pos = np.array([pos_of_date[d] for d in va_ids if d in pos_of_date], dtype=int)
            mapped_folds.append((tr_pos, va_pos))
        
        # Train dual model
        dual_model = DualModelLag(lag)
        results = dual_model.train(X_lag, y_lag, mapped_folds)
        
        models_by_lag[lag] = dual_model
        
        print(f"      Ridge alpha: {dual_model.best_ridge_alpha}")
        print(f"      MLP trained: {results['mlp_trained']}")
    
    return models_by_lag

# ============================================================================
# 7) STAGE-B ROBUST POST-PROCESSING
# ============================================================================

class AdaptiveShrinkage:
    """Adaptive rank shrinkage with deterministic tie-breaking"""
    
    def __init__(self):
        self.a = Config.SHRINK_A_INIT
        self.b = Config.SHRINK_B_INIT
        self.g_weights = Config.SHRINK_G_WEIGHTS.copy()
    
    def compute_alpha(self, n_avail: int, D_feat: float) -> float:
        """Compute adaptive shrinkage coefficient"""
        # Normalized coverage factor
        coverage_factor = 1.0 - (n_avail / 424.0)
        
        # Normalized dispersion factor  
        dispersion_factor = np.clip(D_feat, 0.0, 2.0) / 2.0
        
        # Weighted combination
        g_P = (self.g_weights[0] * coverage_factor + 
               self.g_weights[1] * dispersion_factor)
        
        # Linear shrinkage model
        alpha = np.clip(self.a + self.b * g_P, 0.0, 1.0)
        
        return alpha
    
    def apply_shrinkage(self, model_ranks: np.ndarray, prior_ranks: np.ndarray, 
                       alpha: float, date_id: int) -> np.ndarray:
        """Apply adaptive shrinkage with deterministic tie-breaking"""
        
        # Basic shrinkage
        final_ranks = (1 - alpha) * model_ranks + alpha * prior_ranks
        
        # Apply deterministic perturbation to all targets before ranking
        eps = Config.TIE_EPSILON
        for i in range(len(final_ranks)):
            final_ranks[i] += eps * deterministic_hash(date_id, i)
        
        # Final ranking to ensure proper 1-424 range
        final_ranks = stats.rankdata(final_ranks, method='ordinal').astype(float)
        
        return final_ranks

def process_stage_b(asset_predictions: Dict[int, np.ndarray],
                   M_by_lag: Dict[int, np.ndarray], 
                   target_order: List[str],
                   prior_ranks: np.ndarray,
                   n_avail: int,
                   D_feat: float,
                   date_id: int) -> np.ndarray:
    """Complete Stage-B processing: mapping + ranking + shrinkage"""
    
    # 1) Map asset predictions to targets
    target_predictions = []
    
    for lag in [1, 2, 3, 4]:
        if lag in asset_predictions and lag in M_by_lag:
            M_lag = M_by_lag[lag]                    # (106, n_assets)
            r_pred = np.asarray(asset_predictions[lag]).reshape(-1)  # (n_assets,)
            y_lag = M_lag @ r_pred                   # (106,)
            target_predictions.extend(y_lag)
        else:
            # Fallback: append zeros
            n_targets_lag = M_by_lag.get(lag, np.zeros((106, 106))).shape[0]
            target_predictions.extend([0.0] * n_targets_lag)
    
    # Ensure exactly 424 predictions
    if len(target_predictions) != 424:
        target_predictions = target_predictions[:424] + [0.0] * max(0, 424 - len(target_predictions))
    
    target_predictions = np.array(target_predictions)
    
    # 2) Robust clipping
    target_predictions = np.clip(target_predictions, -5.0, 5.0)
    
    # 3) Initial ranking
    model_ranks = stats.rankdata(target_predictions, method='ordinal').astype(float)
    
    # 4) Adaptive shrinkage
    shrinkage = AdaptiveShrinkage()
    alpha = shrinkage.compute_alpha(n_avail, D_feat)
    final_ranks = shrinkage.apply_shrinkage(model_ranks, prior_ranks, alpha, date_id)
    
    return final_ranks

# ============================================================================
# 8) DUAL-CHANNEL ONLINE UPDATES
# ============================================================================

class OnlineUpdater:
    """Dual-channel online learning: target-side + asset-side"""
    
    def __init__(self, assets: List[str], M_by_lag: Dict[int, np.ndarray]):
        self.assets = assets
        self.M_by_lag = M_by_lag
        
        # Target-side state
        self.prior_ranks = np.arange(424, dtype=float)
        self.rank_history = deque(maxlen=Config.ROLL_UPDATE_WINDOW)
        
        # Asset-side state (simplified for this implementation)
        self.asset_history = {lag: deque(maxlen=Config.ROLL_UPDATE_WINDOW) 
                             for lag in [1, 2, 3, 4]}
        
        # EMA parameters
        self.alpha_ema = 1.0 - np.exp(-1.0 / Config.RECENCY_HALF_LIFE)
        
    def update_target_side(self, date_id: int, y_obs: Dict[str, float], 
                          historical_pred_ranks: Optional[np.ndarray] = None) -> None:
        """Update target-side state with rank calibration"""
        try:
            if len(y_obs) < 2:
                return
            
            # Convert observations to ranks
            obs_values = list(y_obs.values())
            obs_ranks = stats.rankdata(obs_values, method='average')
            
            # Update prior_ranks with EMA
            if len(obs_values) > 0:
                # Simple update: blend observed ranks into prior
                for i, (target, value) in enumerate(y_obs.items()):
                    if target.startswith('target_'):
                        target_idx = int(target.split('_')[1])
                        if 0 <= target_idx < 424:
                            # EMA update
                            self.prior_ranks[target_idx] = (
                                self.alpha_ema * obs_ranks[i] + 
                                (1 - self.alpha_ema) * self.prior_ranks[target_idx]
                            )
            
            # Store in history
            self.rank_history.append({
                'date_id': date_id,
                'observations': y_obs,
                'ranks': obs_ranks
            })
            
        except Exception as e:
            print(f"Warning: target-side update failed for date {date_id}: {e}")
    
    def update_asset_side(self, date_id: int, lag: int, 
                         y_obs: np.ndarray, mask: np.ndarray,
                         solver: Callable) -> Optional[np.ndarray]:
        """Update asset-side state with recovered asset labels"""
        try:
            if mask.sum() < Config.N_MIN_UPDATE:
                return None
            
            # Recover asset labels from target observations
            r_recovered = solver(y_obs, mask)
            if r_recovered is None:
                return None
            
            # Store in history
            self.asset_history[lag].append({
                'date_id': date_id,
                'r_recovered': r_recovered,
                'mask': mask.copy()
            })
            
            return r_recovered
            
        except Exception as e:
            print(f"Warning: asset-side update failed for lag {lag}, date {date_id}: {e}")
            return None
    
    def get_prior_ranks(self) -> np.ndarray:
        """Get current prior ranks"""
        return self.prior_ranks.copy()

# ============================================================================
# 9) EVALUATION AND CV SYSTEM  
# ============================================================================

def build_time_blocked_folds(start: int = 0, end: int = 1826, 
                            n_folds: int = 4, purge: int = 10) -> List[Tuple[np.ndarray, np.ndarray]]:
    """Build time-blocked CV folds with purging for target-space evaluation"""
    total_days = end - start + 1
    fold_size = total_days // n_folds
    
    folds = []
    for i in range(n_folds):
        # Validation period
        valid_start = start + i * fold_size
        valid_end = start + (i + 1) * fold_size - 1
        if i == n_folds - 1:  # Last fold gets remainder
            valid_end = end
        
        # Training period (everything before validation, with purge gap)
        train_end = valid_start - purge - 1
        if train_end < start:
            train_indices = np.array([])
        else:
            train_indices = np.arange(start, train_end + 1)
        
        valid_indices = np.arange(valid_start, valid_end + 1)
        folds.append((train_indices, valid_indices))
    
    print(f"  Built {n_folds} time-blocked folds with {purge}-day purge")
    return folds

def daily_spearman_correlation(y_pred: np.ndarray, y_true: np.ndarray, mask: np.ndarray) -> float:
    """Compute daily Spearman correlation for available targets"""
    if mask.sum() < 2:
        return 0.0
    
    pred_valid = y_pred[mask]
    true_valid = y_true[mask]
    
    if np.std(pred_valid) < 1e-8 or np.std(true_valid) < 1e-8:
        return 0.0
    
    try:
        # Use average method for ties (matches official evaluation)
        pred_ranks = stats.rankdata(pred_valid, method='average')
        true_ranks = stats.rankdata(true_valid, method='average')
        
        corr, _ = stats.spearmanr(pred_ranks, true_ranks)
        return corr if not np.isnan(corr) else 0.0
    except:
        return 0.0

def compute_fold_sharpe(daily_correlations: List[float]) -> float:
    """Compute Sharpe ratio of daily Spearman correlations"""
    if len(daily_correlations) == 0:
        return 0.0
    
    daily_corr = np.array(daily_correlations)
    mean_corr = np.mean(daily_corr)
    std_corr = np.std(daily_corr)
    
    if std_corr < 1e-8:
        return 0.0
    
    return mean_corr / std_corr

# ============================================================================
# 10) MAIN ENHANCED PIPELINE CLASS
# ============================================================================

class EnhancedDualStagePipeline:
    """Complete enhanced dual-stage pipeline"""
    
    def __init__(self, mode: str = "TRAIN"):
        self.mode = mode
        
        # Core mappings and target order
        self.assets = []
        self.M_by_lag = {}
        self.targets_by_lag = {}
        self.target_order = []
        self.solvers_by_lag = {}
        
        # Preprocessing
        self.preprocessor = FeaturePreprocessor()
        
        # Stage-A models
        self.models_by_lag = {}
        self.asset_means = {}
        self.asset_stds = {}
        
        # Stage-B shrinkage
        self.shrinkage = AdaptiveShrinkage()
        
        # Online learning
        self.online_updater = None
        
        # Artifacts loaded flag
        self.artifacts_loaded = False
        
        set_global_seed(Config.GLOBAL_SEED)
    
    def run_training_pipeline(self):
        """Execute complete enhanced training pipeline"""
        print("=== ENHANCED DUAL-STAGE PIPELINE TRAINING ===")
        print(f"Mode: {self.mode}")
        print(f"Global seed: {Config.GLOBAL_SEED}")
        print(f"MLP enabled: {Config.MLP_TOGGLE}")
        
        # Step 1: Load and validate data
        print("\n1. Loading and validating data...")
        train_df = self._load_train()
        labels_df = self._load_labels()
        pairs_df = self._load_pairs()
        
        # Data validation
        assert len(train_df) == 1917, f"Expected 1917 training rows, got {len(train_df)}"
        assert len(labels_df) == 1917, f"Expected 1917 label rows, got {len(labels_df)}"
        assert len(pairs_df) == 424, f"Expected 424 pairs, got {len(pairs_df)}"
        
        print(f"   ✅ Data validated: Train {train_df.shape}, Labels {labels_df.shape}, Pairs {pairs_df.shape}")
        
        # Step 2: Build target order and asset mappings
        print("\n2. Building target order and asset mappings...")
        self.target_order = build_target_order(pairs_df)
        self.assets, self.M_by_lag, self.targets_by_lag = build_assets_and_M(pairs_df)
        self.solvers_by_lag = {lag: build_solver(M, alpha=1e-3) 
                              for lag, M in self.M_by_lag.items()}
        
        # Step 3: Fit preprocessing pipeline
        print("\n3. Fitting preprocessing pipeline...")
        X_processed = self.preprocessor.preprocess_fit(train_df)
        
        # Step 4: Recover asset-space labels
        print("\n4. Recovering asset-space labels...")
        r_by_lag = recover_asset_labels(labels_df, self.M_by_lag, 
                                       self.solvers_by_lag, self.targets_by_lag)
        
        # Step 5: Standardize asset labels
        print("\n5. Standardizing asset labels...")
        r_std, self.asset_means, self.asset_stds = standardize_asset_labels(r_by_lag, (0, 1826))
        
        # Step 6: Build CV folds
        print("\n6. Building time-blocked CV folds...")
        folds = build_time_blocked_folds(0, 1826, Config.FOLDS, Config.PURGE_GAP)
        
        # Step 7: Train Stage-A dual models
        print("\n7. Training Stage-A dual models...")
        self.models_by_lag = train_stage_a_models(X_processed, r_std, folds, self.preprocessor)
        
        # Step 8: Learn shrinkage parameters (simplified for now)
        print("\n8. Learning shrinkage parameters...")
        # For now, use default parameters
        print(f"   Using default shrinkage: a={Config.SHRINK_A_INIT}, b={Config.SHRINK_B_INIT}")
        
        # Step 9: Save all artifacts
        print("\n9. Saving artifacts...")
        self._save_all_artifacts()
        
        # Step 10: Public holdout mirror evaluation (no tuning)
        print("\n10. Mirror evaluation on public holdout...")
        try:
            mirror_score = self._evaluate_public_mirror()
            print(f"   Mirror evaluation score: {mirror_score:.6f}")
        except Exception as e:
            print(f"   Mirror evaluation failed: {e}")
        
        print("\n✅ Enhanced training pipeline complete!")
        return self
    
    def _load_train(self) -> pd.DataFrame:
        """Load training features with path detection"""
        data_path = get_data_path()
        return pd.read_csv(data_path / "train.csv")
    
    def _load_labels(self) -> pd.DataFrame:
        """Load training labels"""
        data_path = get_data_path()
        return pd.read_csv(data_path / "train_labels.csv")
    
    def _load_pairs(self) -> pd.DataFrame:
        """Load target pairs mapping"""
        data_path = get_data_path()
        return pd.read_csv(data_path / "target_pairs.csv")
    
    def _save_all_artifacts(self):
        """Save all critical artifacts with strict error handling"""
        artifacts = [
            ('target_order.json', self.target_order),
            ('assets.json', self.assets),
            ('M_by_lag.npz', self.M_by_lag),
            ('targets_by_lag.json', self.targets_by_lag),
            ('feature_list.json', self.preprocessor.feature_list),
            ('final_feature_list.json', self.preprocessor.final_feature_list),
            ('scalers.pkl', self.preprocessor.scalers),
            ('group_masks.json', {k: v.tolist() for k, v in self.preprocessor.group_masks.items()}),
            ('models_by_lag.pkl', self.models_by_lag),
            ('asset_means.pkl', self.asset_means),
            ('asset_stds.pkl', self.asset_stds),
            ('shrinkage.json', {'a': self.shrinkage.a, 'b': self.shrinkage.b, 'g_weights': self.shrinkage.g_weights}),
            ('config.json', {
                'GLOBAL_SEED': Config.GLOBAL_SEED,
                'LAG_OFFSETS': Config.LAG_OFFSETS,
                'DROP_FEATURES': Config.DROP_FEATURES,
                'RIDGE_ALPHAS': Config.RIDGE_ALPHAS,
                'MLP_TOGGLE': Config.MLP_TOGGLE,
                'RIDGE_MLP_WEIGHTS': Config.RIDGE_MLP_WEIGHTS
            })
        ]
        
        for name, obj in artifacts:
            save_artifact(name, obj)
    
    def _load_all_artifacts(self):
        """Load all artifacts with strict validation"""
        if self.artifacts_loaded:
            return
        
        try:
            print("🔄 Loading critical artifacts...")
            
            # Core mappings
            self.target_order = load_artifact('target_order.json')
            self.assets = load_artifact('assets.json')
            self.M_by_lag = load_artifact('M_by_lag.npz')
            self.targets_by_lag = load_artifact('targets_by_lag.json')
            
            # Preprocessing artifacts
            self.preprocessor.feature_list = load_artifact('feature_list.json')
            self.preprocessor.final_feature_list = load_artifact('final_feature_list.json')
            self.preprocessor.scalers = load_artifact('scalers.pkl')
            group_masks_dict = load_artifact('group_masks.json')
            self.preprocessor.group_masks = {k: np.array(v) for k, v in group_masks_dict.items()}
            self.preprocessor.fitted = True
            
            # Model artifacts
            self.models_by_lag = load_artifact('models_by_lag.pkl')
            self.asset_means = load_artifact('asset_means.pkl')
            self.asset_stds = load_artifact('asset_stds.pkl')
            
            # Shrinkage parameters
            shrinkage_dict = load_artifact('shrinkage.json')
            self.shrinkage.a = shrinkage_dict['a']
            self.shrinkage.b = shrinkage_dict['b']
            self.shrinkage.g_weights = shrinkage_dict['g_weights']
            
            # Rebuild solvers
            self.solvers_by_lag = {lag: build_solver(M, alpha=1e-3) 
                                  for lag, M in self.M_by_lag.items()}
            
            # Initialize online updater
            self.online_updater = OnlineUpdater(self.assets, self.M_by_lag)
            
            # Validation
            assert len(self.target_order) == 424, "Target order must have 424 elements"
            assert len(self.assets) > 0, "Assets list cannot be empty"
            assert len(self.models_by_lag) > 0, "Must have trained models"
            
            self.artifacts_loaded = True
            print("✅ All artifacts loaded and validated")
            
        except Exception as e:
            print(f"❌ Critical artifact loading failed: {e}")
            raise RuntimeError(f"Cannot proceed without artifacts: {e}")
    
    def _evaluate_public_mirror(self) -> float:
        """Mirror evaluation on public holdout [1827, 1916] - NO TUNING"""
        # This is a placeholder - in real implementation would simulate
        # the full pipeline on holdout period
        return 0.0  # Placeholder score
    
    def predict_single(self, test_row: pd.Series, 
                      lag_batches: Dict[int, pl.DataFrame]) -> np.ndarray:
        """Enhanced single-row prediction with dual-channel updates"""
        
        # Ensure artifacts are loaded
        if not self.artifacts_loaded:
            self._load_all_artifacts()
        
        date_id = int(test_row['date_id'])
        
        # Step 1: Extract observations from lag batches for online updates
        y_obs_map = {}  # {target: value} from four lag batches of THIS day
        for lag in [1, 2, 3, 4]:
            if lag in lag_batches and lag_batches[lag] is not None:
                lag_df = lag_batches[lag].to_pandas()
                row = lag_df[lag_df['date_id'] == date_id]
                if len(row) > 0:
                    row = row.iloc[0]
                    for c in [c for c in lag_df.columns if c.startswith('target_')]:
                        v = row[c]
                        if not pd.isna(v):
                            y_obs_map[c] = float(np.clip(v, -5, 5))
        
        # Target-side online update (rank-calibration/EMA)
        if len(y_obs_map) > 0:
            self.online_updater.update_target_side(date_id, y_obs_map)
        
        # Ingest arrived labels (compliant momentum) 
        prior_ranks, coverage_by_lag = ingest_arrived_labels(
            date_id, lag_batches, self.target_order
        )
        
        # Update online state
        total_coverage = sum(coverage_by_lag.values())
        
        # Step 2: Preprocess test features
        test_df = pd.DataFrame([test_row])
        X_processed = self.preprocessor.preprocess_transform(test_df)
        
        if len(X_processed) == 0:
            raise ValueError("Preprocessing returned empty DataFrame")
        
        # Extract feature vector using final feature list (includes missing flags)
        if hasattr(self.preprocessor, 'final_feature_list') and len(self.preprocessor.final_feature_list) > 0:
            feature_cols = self.preprocessor.final_feature_list
        else:
            feature_cols = [col for col in X_processed.columns if col != 'date_id']
        
        feature_vector = X_processed.iloc[0][feature_cols].values
        feature_vector = np.nan_to_num(feature_vector, nan=0.0)
        
        # Step 3: Stage-A asset predictions
        asset_predictions = {}
        for lag in [1, 2, 3, 4]:
            if lag not in self.models_by_lag:
                raise ValueError(f"Model for lag {lag} not found")
            
            # Get standardized prediction
            r_pred_std = self.models_by_lag[lag].predict(feature_vector)
            
            # De-standardize
            if lag in self.asset_means and lag in self.asset_stds:
                r_pred = r_pred_std * self.asset_stds[lag] + self.asset_means[lag]
            else:
                r_pred = r_pred_std
            
            asset_predictions[lag] = r_pred
        
        # Step 4: Compute feature dispersion
        D_feat = compute_D_feat(X_processed, self.preprocessor.group_masks, date_id)
        
        # Step 5: Stage-B processing
        final_ranks = process_stage_b(
            asset_predictions, self.M_by_lag, self.target_order,
            self.online_updater.get_prior_ranks(), total_coverage, D_feat, date_id
        )
        
        # Step 6: Asset-side online updates (if sufficient coverage and time available)
        for lag in [1, 2, 3, 4]:
            if lag in lag_batches and lag_batches[lag] is not None:
                lag_df = lag_batches[lag].to_pandas()
                row = lag_df[lag_df['date_id'] == date_id]
                if len(row) > 0:
                    row = row.iloc[0]
                    targets_lag = self.targets_by_lag[lag]
                    y_obs = np.array([row.get(t, np.nan) for t in targets_lag], dtype=float)
                    mask = ~np.isnan(y_obs)
                    if mask.sum() >= Config.N_MIN_UPDATE:
                        if lag in self.solvers_by_lag:
                            r_true = self.solvers_by_lag[lag](np.clip(y_obs, -5, 5), mask)
                            if r_true is not None:
                                # TODO: Feed (feature_vector, r_true_std) to RLS statistics
                                # For now, just update the online updater state
                                self.online_updater.update_asset_side(
                                    date_id, lag, y_obs, mask, self.solvers_by_lag[lag]
                                )
        
        return final_ranks

# ============================================================================
# 11) KAGGLE API INTEGRATION  
# ============================================================================

# Global pipeline instance
enhanced_pipeline = None

def predict(
    test: pl.DataFrame,
    label_lags_1_batch: pl.DataFrame,
    label_lags_2_batch: pl.DataFrame,
    label_lags_3_batch: pl.DataFrame,
    label_lags_4_batch: pl.DataFrame,
) -> pl.DataFrame:
    """Enhanced Kaggle API predict function with dual-channel updates"""
    global enhanced_pipeline
    
    # Initialize on first call (no time limit)
    if enhanced_pipeline is None:
        print("🚀 Initializing Enhanced Dual-Stage Pipeline...")
        enhanced_pipeline = EnhancedDualStagePipeline(mode="INFERENCE")
        enhanced_pipeline._load_all_artifacts()
        print("✅ Enhanced pipeline ready for inference")
    
    # Convert Polars to Pandas
    test_pd = test.to_pandas()
    
    if len(test_pd) != 1:
        raise ValueError(f"Expected 1 test row, got {len(test_pd)}")
    
    test_row = test_pd.iloc[0]
    
    # Prepare lag batches
    lag_batches = {
        1: label_lags_1_batch,
        2: label_lags_2_batch, 
        3: label_lags_3_batch,
        4: label_lags_4_batch
    }
    
    # Enhanced prediction
    pred_values = enhanced_pipeline.predict_single(test_row, lag_batches)
    
    # Format output according to target_order
    predictions_dict = {}
    for i, target in enumerate(enhanced_pipeline.target_order):
        predictions_dict[target] = [float(pred_values[i])]
    
    result = pl.DataFrame(predictions_dict)
    
    # Validation
    assert len(result) == 1, f"Expected 1 prediction row, got {len(result)}"
    assert len(result.columns) == 424, f"Expected 424 columns, got {len(result.columns)}"
    
    return result

# ============================================================================
# 12) SERVER SETUP & MAIN EXECUTION  
# ============================================================================

# Create inference server at module level
if kaggle_evaluation is not None:
    inference_server = kaggle_evaluation.mitsui_inference_server.MitsuiInferenceServer(predict)
else:
    inference_server = None

if __name__ == "__main__":
    if os.getenv('KAGGLE_IS_COMPETITION_RERUN'):
        # Production mode: serve the API
        if inference_server is not None:
            inference_server.serve()
        else:
            print("❌ Error: kaggle_evaluation not available in competition mode")
    else:
        # Development mode: run enhanced training pipeline
        print("🔧 Running Enhanced Dual-Stage Pipeline in development mode...")
        
        # Initialize and train
        pipeline = EnhancedDualStagePipeline(mode="TRAIN")
        pipeline.run_training_pipeline()
        
        print("\n=== ENHANCED PIPELINE READY FOR SUBMISSION ===")
        print("All artifacts saved to /kaggle/working/")
        print("Features:")
        print("  ✅ Dual-stage architecture (Ridge + MLP)")
        print("  ✅ Compliant momentum using lagged test labels")
        print("  ✅ Dual-channel online updates")
        print("  ✅ Adaptive rank shrinkage")
        print("  ✅ Deterministic tie-breaking")
        print("  ✅ Strict artifact management")
        
        # Optional: Test local gateway
        if inference_server is not None:
            data_path = get_data_path()
            print(f"\n🧪 Testing local gateway with data from: {data_path}")
            inference_server.run_local_gateway((str(data_path),))