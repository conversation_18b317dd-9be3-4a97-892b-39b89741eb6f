"""
Comprehensive EDA for Mi<PERSON>i <PERSON>mmodity Prediction Challenge
Single script - just run: python eda.py
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

warnings.filterwarnings('ignore')
plt.style.use('default')
sns.set_palette("husl")

# Paths
DATA_PATH = Path("../../input/mitsui-commodity-prediction-challenge")
OUTPUT_PATH = Path("./eda_outputs")
OUTPUT_PATH.mkdir(exist_ok=True)

def load_data():
    """Load all data files"""
    print("Loading data...")
    
    train_df = pd.read_csv(DATA_PATH / "train.csv")
    train_labels_df = pd.read_csv(DATA_PATH / "train_labels.csv")
    target_pairs_df = pd.read_csv(DATA_PATH / "target_pairs.csv")
    
    # Load lagged files
    lagged_dfs = {}
    lagged_path = DATA_PATH / "lagged_test_labels"
    for lag_file in lagged_path.glob("test_labels_lag_*.csv"):
        lag_num = lag_file.stem.split('_')[-1]
        lagged_dfs[f'lag_{lag_num}'] = pd.read_csv(lag_file)
    
    print(f"✓ Train: {train_df.shape}, Labels: {train_labels_df.shape}, Pairs: {target_pairs_df.shape}")
    print(f"✓ Lagged files: {len(lagged_dfs)}")
    
    return train_df, train_labels_df, target_pairs_df, lagged_dfs

def basic_analysis(train_df, train_labels_df, target_pairs_df):
    """Basic dataset structure analysis"""
    print("\n" + "="*60)
    print("1. BASIC DATASET STRUCTURE")
    print("="*60)
    
    # Feature prefixes
    prefixes = ['LME', 'JPX', 'US_Stock', 'FX']
    feature_cols = [col for col in train_df.columns if col != 'date_id']
    
    prefix_counts = {}
    for prefix in prefixes:
        count = len([col for col in feature_cols if col.startswith(prefix)])
        prefix_counts[prefix] = count
        print(f"{prefix}: {count} features")
    
    # Data quality
    print(f"\nData Quality:")
    print(f"  Train missing values: {train_df.isnull().sum().sum():,}")
    print(f"  Labels missing values: {train_labels_df.isnull().sum().sum():,}")
    print(f"  Train duplicates: {train_df.duplicated().sum()}")
    
    # Date alignment
    train_dates = set(train_df['date_id'])
    label_dates = set(train_labels_df['date_id'])
    print(f"  Date alignment: {len(train_dates & label_dates)}/{len(train_dates | label_dates)}")
    
    return prefix_counts

def temporal_analysis(train_df):
    """Time coverage and patterns"""
    print("\n" + "="*60)
    print("2. TEMPORAL ANALYSIS")
    print("="*60)
    
    date_ids = sorted(train_df['date_id'].unique())
    print(f"Date range: {min(date_ids)} to {max(date_ids)} ({len(date_ids)} days)")
    
    # Check gaps
    gaps = []
    for i in range(1, len(date_ids)):
        gap = date_ids[i] - date_ids[i-1]
        if gap > 1:
            gaps.append((date_ids[i-1], date_ids[i], gap))
    
    print(f"Date gaps found: {len(gaps)}")
    if gaps:
        for start, end, gap_size in gaps[:3]:
            print(f"  Gap: {start} to {end} ({gap_size} days)")
    
    # Coverage by prefix
    prefixes = ['LME', 'JPX', 'US_Stock', 'FX']
    sample_dates = date_ids[::max(1, len(date_ids)//20)]  # Sample 20 dates
    
    coverage_data = []
    for date_id in sample_dates:
        date_data = train_df[train_df['date_id'] == date_id]
        for prefix in prefixes:
            prefix_cols = [col for col in date_data.columns if col.startswith(prefix)]
            if prefix_cols:
                coverage = date_data[prefix_cols].notna().mean().mean()
                coverage_data.append({'date_id': date_id, 'prefix': prefix, 'coverage': coverage})
    
    if coverage_data:
        coverage_df = pd.DataFrame(coverage_data)
        pivot_data = coverage_df.pivot(index='prefix', columns='date_id', values='coverage')
        
        plt.figure(figsize=(12, 6))
        sns.heatmap(pivot_data, cmap='viridis', cbar_kws={'label': 'Coverage'})
        plt.title('Feature Coverage Over Time by Asset Class')
        plt.tight_layout()
        plt.savefig(OUTPUT_PATH / 'temporal_coverage.png', dpi=150, bbox_inches='tight')
        plt.show()

def feature_analysis(train_df):
    """Feature exploration"""
    print("\n" + "="*60)
    print("3. FEATURE ANALYSIS")
    print("="*60)
    
    feature_cols = [col for col in train_df.columns if col != 'date_id']
    print(f"Total features: {len(feature_cols)}")
    
    # Missing value analysis
    missing_stats = train_df[feature_cols].isnull().sum()
    print(f"Features with no missing: {(missing_stats == 0).sum()}")
    print(f"Features >50% missing: {(missing_stats > len(train_df) * 0.5).sum()}")
    print(f"Average missing rate: {missing_stats.mean() / len(train_df):.1%}")
    
    # Distribution analysis by prefix
    prefixes = ['LME', 'JPX', 'US_Stock', 'FX']
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    axes = axes.flatten()
    
    for i, prefix in enumerate(prefixes):
        prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
        if prefix_cols:
            # Sample columns for distribution
            sample_cols = prefix_cols[:min(3, len(prefix_cols))]
            data = train_df[sample_cols].values.flatten()
            data = data[~np.isnan(data)]
            
            if len(data) > 0:
                axes[i].hist(data, bins=50, alpha=0.7, density=True)
                axes[i].set_title(f'{prefix} Distributions')
                axes[i].axvline(np.mean(data), color='red', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig(OUTPUT_PATH / 'feature_distributions.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # Correlation analysis (sample)
    sample_features = feature_cols[:50]  # Sample for performance
    corr_matrix = train_df[sample_features].corr()
    
    plt.figure(figsize=(10, 8))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, cmap='coolwarm', center=0, square=True)
    plt.title('Feature Correlation Matrix (Sample)')
    plt.tight_layout()
    plt.savefig(OUTPUT_PATH / 'correlation_matrix.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # High correlations
    high_corr = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.8 and not np.isnan(corr_val):
                high_corr.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_val))
    
    print(f"High correlations (|r| > 0.8): {len(high_corr)}")
    for col1, col2, corr in high_corr[:5]:
        print(f"  {col1} - {col2}: {corr:.3f}")

def target_analysis(train_labels_df, target_pairs_df):
    """Target and pair exploration"""
    print("\n" + "="*60)
    print("4. TARGET ANALYSIS")
    print("="*60)
    
    target_cols = [col for col in train_labels_df.columns if col.startswith('target_')]
    print(f"Total targets: {len(target_cols)}")
    
    # Missing values
    target_missing = train_labels_df[target_cols].isnull().sum()
    print(f"Targets with missing: {(target_missing > 0).sum()}")
    print(f"Average missing rate: {target_missing.mean() / len(train_labels_df):.1%}")
    
    # Target distributions (sample)
    sample_targets = target_cols[:12]
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    axes = axes.flatten()
    
    for i, target in enumerate(sample_targets):
        data = train_labels_df[target].dropna()
        if len(data) > 0:
            axes[i].hist(data, bins=30, alpha=0.7, density=True)
            axes[i].set_title(f'{target}')
            axes[i].axvline(data.mean(), color='red', linestyle='--', alpha=0.7)
            
            # Stats
            stats_text = f'μ={data.mean():.3f}\nσ={data.std():.3f}'
            axes[i].text(0.02, 0.98, stats_text, transform=axes[i].transAxes, 
                        verticalalignment='top', fontsize=8, 
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(OUTPUT_PATH / 'target_distributions.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # Overall target stats
    all_target_data = train_labels_df[target_cols].values.flatten()
    all_target_data = all_target_data[~np.isnan(all_target_data)]
    
    print(f"\nOverall Target Statistics:")
    print(f"  Mean: {np.mean(all_target_data):.6f}")
    print(f"  Std: {np.std(all_target_data):.6f}")
    print(f"  Skewness: {stats.skew(all_target_data):.3f}")
    print(f"  Range: [{np.min(all_target_data):.4f}, {np.max(all_target_data):.4f}]")
    
    # Target pairs analysis
    print(f"\nTarget Pairs Analysis:")
    print(f"  Total pairs defined: {len(target_pairs_df)}")
    
    lag_counts = target_pairs_df['lag'].value_counts().sort_index()
    print(f"  Lag distribution:")
    for lag, count in lag_counts.items():
        print(f"    Lag {lag}: {count} targets")
    
    # Single vs pair targets
    single_count = sum(1 for _, row in target_pairs_df.iterrows() if ' - ' not in row['pair'])
    pair_count = len(target_pairs_df) - single_count
    print(f"  Single asset targets: {single_count}")
    print(f"  Asset pair targets: {pair_count}")

def lagged_analysis(train_labels_df, lagged_dfs):
    """Lagged labels analysis"""
    print("\n" + "="*60)
    print("5. LAGGED ANALYSIS")
    print("="*60)
    
    print(f"Available lagged files: {list(lagged_dfs.keys())}")
    
    for lag_name, lag_df in lagged_dfs.items():
        print(f"{lag_name}: {lag_df.shape}, dates {lag_df['date_id'].min()}-{lag_df['date_id'].max()}")
    
    # Autocorrelation analysis (sample targets)
    target_cols = [col for col in train_labels_df.columns if col.startswith('target_')]
    sample_targets = target_cols[:4]
    
    fig, axes = plt.subplots(1, len(sample_targets), figsize=(16, 4))
    if len(sample_targets) == 1:
        axes = [axes]
    
    for i, target in enumerate(sample_targets):
        data = train_labels_df[target].dropna()
        if len(data) > 10:
            autocorr = [data.autocorr(lag=lag) for lag in range(1, min(11, len(data)//2))]
            lags = list(range(1, len(autocorr) + 1))
            
            axes[i].bar(lags, autocorr, alpha=0.7)
            axes[i].set_title(f'{target} Autocorr')
            axes[i].set_xlabel('Lag')
            axes[i].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(OUTPUT_PATH / 'autocorrelations.png', dpi=150, bbox_inches='tight')
    plt.show()

def advanced_analysis(train_df, train_labels_df):
    """Advanced analysis"""
    print("\n" + "="*60)
    print("6. ADVANCED ANALYSIS")
    print("="*60)
    
    # PCA on features (sample)
    feature_cols = [col for col in train_df.columns if col != 'date_id']
    sample_features = feature_cols[:50]  # Sample for performance
    
    pca_data = train_df[sample_features].fillna(0)
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(pca_data)
    
    pca = PCA()
    pca.fit(scaled_data)
    
    # Plot explained variance
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    ax1.plot(range(1, 11), pca.explained_variance_ratio_[:10], 'bo-')
    ax1.set_xlabel('Component')
    ax1.set_ylabel('Explained Variance Ratio')
    ax1.set_title('PCA Explained Variance')
    ax1.grid(True)
    
    cumsum = np.cumsum(pca.explained_variance_ratio_)
    ax2.plot(range(1, 11), cumsum[:10], 'ro-')
    ax2.set_xlabel('Component')
    ax2.set_ylabel('Cumulative Explained Variance')
    ax2.set_title('PCA Cumulative Variance')
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(OUTPUT_PATH / 'pca_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"First 5 components explain {cumsum[4]:.1%} of variance")
    print(f"First 10 components explain {cumsum[9]:.1%} of variance")
    
    # Feature-target correlations (sample)
    target_cols = [col for col in train_labels_df.columns if col.startswith('target_')]
    sample_targets = target_cols[:10]
    
    merged_data = pd.merge(train_df[['date_id'] + sample_features], 
                          train_labels_df[['date_id'] + sample_targets], 
                          on='date_id', how='inner')
    
    max_corrs = []
    for feature in sample_features[:20]:  # Limit for performance
        for target in sample_targets:
            if feature in merged_data.columns and target in merged_data.columns:
                corr = merged_data[feature].corr(merged_data[target])
                if not np.isnan(corr):
                    max_corrs.append((feature, target, abs(corr), corr))
    
    max_corrs.sort(key=lambda x: x[2], reverse=True)
    
    print(f"\nTop feature-target correlations:")
    for feature, target, abs_corr, corr in max_corrs[:5]:
        print(f"  {feature} - {target}: {corr:.3f}")

def main():
    """Run complete EDA"""
    print("MITSUI COMMODITY PREDICTION CHALLENGE - EDA")
    print("="*60)
    
    # Load data
    train_df, train_labels_df, target_pairs_df, lagged_dfs = load_data()
    
    # Run analyses
    basic_analysis(train_df, train_labels_df, target_pairs_df)
    temporal_analysis(train_df)
    feature_analysis(train_df)
    target_analysis(train_labels_df, target_pairs_df)
    lagged_analysis(train_labels_df, lagged_dfs)
    advanced_analysis(train_df, train_labels_df)
    
    print("\n" + "="*60)
    print("EDA COMPLETE!")
    print("="*60)
    print(f"Plots saved to: {OUTPUT_PATH}")
    print("\nKey insights:")
    print("- Check missing value patterns by asset class")
    print("- Note temporal coverage differences")
    print("- Examine feature correlations for redundancy")
    print("- Consider target pair relationships for modeling")
    print("- Use PCA insights for dimensionality reduction")

if __name__ == "__main__":
    main()
