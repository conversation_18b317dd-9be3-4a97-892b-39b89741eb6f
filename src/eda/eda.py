# Advanced EDA for Commodity Prices Competition
# Based on insights from data_insights.md and target_calculation.py analysis
# This script contains all advanced exploratory analysis needed before modeling

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

def get_data_path():
    """Auto-detect if running on Kaggle, Colab, or local"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")

    if kaggle_path.exists():
        return kaggle_path
    elif colab_path.exists():
        return colab_path
    else:
        return local_path

# =============================================================================
# CELL 1: Load All Data and Target Pairs Analysis
# =============================================================================
def cell_1_load_and_target_pairs():
    """Load all datasets and analyze target_pairs.csv for complete taxonomy"""
    print("="*80)
    print("ADVANCED EDA - CELL 1: TARGET PAIRS TAXONOMY ANALYSIS")
    print("="*80)

    data_path = get_data_path()
    print(f"Using data path: {data_path}")

    # Load all datasets
    print("\n1) LOADING ALL DATASETS:")
    train = pd.read_csv(data_path / "train.csv")
    train_labels = pd.read_csv(data_path / "train_labels.csv")
    target_pairs = pd.read_csv(data_path / "target_pairs.csv")

    print(f"   • train.csv: {train.shape}")
    print(f"   • train_labels.csv: {train_labels.shape}")
    print(f"   • target_pairs.csv: {target_pairs.shape}")

    # Analyze target_pairs structure
    print(f"\n2) TARGET PAIRS STRUCTURE:")
    print(f"   • Columns: {list(target_pairs.columns)}")
    print(f"   • Unique lags: {sorted(target_pairs['lag'].unique())}")
    print(f"   • Lag distribution:")
    lag_counts = target_pairs['lag'].value_counts().sort_index()
    for lag, count in lag_counts.items():
        print(f"     - Lag {lag}: {count} targets")

    # Parse pair structure
    print(f"\n3) PAIR STRUCTURE ANALYSIS:")
    target_pairs['pair_split'] = target_pairs['pair'].str.split(' - ')
    target_pairs['asset_a'] = target_pairs['pair_split'].str[0]
    target_pairs['asset_b'] = target_pairs['pair_split'].str[1]

    # Handle any NaN values
    target_pairs['asset_a'] = target_pairs['asset_a'].fillna('Unknown')
    target_pairs['asset_b'] = target_pairs['asset_b'].fillna('Unknown')

    # Asset class mapping
    def get_asset_class(asset_name):
        # Handle NaN or non-string values
        if not isinstance(asset_name, str):
            return 'Unknown'

        if asset_name.startswith('LME_'):
            return 'LME'
        elif asset_name.startswith('JPX_'):
            return 'JPX'
        elif asset_name.startswith('US_Stock_'):
            return 'US_Stock'
        elif asset_name.startswith('FX_'):
            return 'FX'
        else:
            return 'Unknown'

    target_pairs['class_a'] = target_pairs['asset_a'].apply(get_asset_class)
    target_pairs['class_b'] = target_pairs['asset_b'].apply(get_asset_class)
    target_pairs['pair_type'] = target_pairs['class_a'] + '_vs_' + target_pairs['class_b']

    print(f"   • Pair type distribution:")
    pair_type_counts = target_pairs['pair_type'].value_counts()
    for pair_type, count in pair_type_counts.items():
        print(f"     - {pair_type}: {count} targets")

    # Check for antisymmetric pairs (A-B vs B-A)
    print(f"\n4) ANTISYMMETRY ANALYSIS:")
    antisymmetric_pairs = []
    for idx, row in target_pairs.iterrows():
        reverse_pair = f"{row['asset_b']} - {row['asset_a']}"
        reverse_match = target_pairs[target_pairs['pair'] == reverse_pair]
        if len(reverse_match) > 0:
            antisymmetric_pairs.append((row['target'], reverse_match.iloc[0]['target']))

    print(f"   • Found {len(antisymmetric_pairs)} antisymmetric pairs")
    if len(antisymmetric_pairs) > 0:
        print(f"   • Examples:")
        for i, (target_a, target_b) in enumerate(antisymmetric_pairs[:5]):
            print(f"     - {target_a} ↔ {target_b}")

    # Save target taxonomy for later use
    target_taxonomy = target_pairs[['target', 'lag', 'pair_type', 'class_a', 'class_b']].copy()

    return train, train_labels, target_pairs, target_taxonomy

# =============================================================================
# CELL 2: Alignment Audit - Prevent Look-Ahead Bias
# =============================================================================
def cell_2_alignment_audit(train, train_labels, target_pairs):
    """Verify proper alignment between features and targets to prevent look-ahead bias"""
    print("="*80)
    print("ADVANCED EDA - CELL 2: ALIGNMENT AUDIT (LOOK-AHEAD BIAS PREVENTION)")
    print("="*80)

    print("1) TARGET CALCULATION VERIFICATION:")
    print("   Formula: target = log(A[t+lag+1]/A[t+1]) - log(B[t+lag+1]/B[t+1])")
    print("   This means at time t, target uses prices from t+1 to t+lag+1")
    print("   Features at time t must NOT include any info from t+1 onwards")

    # Check date alignment
    print(f"\n2) DATE ALIGNMENT CHECK:")
    train_dates = sorted(train['date_id'].unique())
    label_dates = sorted(train_labels['date_id'].unique())

    print(f"   • Train date range: {min(train_dates)} to {max(train_dates)} ({len(train_dates)} days)")
    print(f"   • Label date range: {min(label_dates)} to {max(label_dates)} ({len(label_dates)} days)")

    # Check for proper lag structure
    max_lag = target_pairs['lag'].max()
    print(f"   • Maximum lag: {max_lag} days")
    print(f"   • Last trainable date: {max(train_dates) - max_lag}")
    print(f"   • Last label date: {max(label_dates)}")

    # Verify no future leakage
    future_leak_risk = max(train_dates) - max_lag <= max(label_dates)
    print(f"   • Future leakage risk: {'⚠️  YES' if future_leak_risk else '✅ NO'}")

    if future_leak_risk:
        print("   ⚠️  WARNING: Potential look-ahead bias detected!")
        print("   Features may contain information from target calculation period")

    # Sample verification with actual target calculation
    print(f"\n3) SAMPLE TARGET CALCULATION VERIFICATION:")
    sample_target = target_pairs.iloc[0]
    print(f"   • Sample target: {sample_target['target']}")
    print(f"   • Pair: {sample_target['pair']}")
    print(f"   • Lag: {sample_target['lag']}")

    # Extract asset names with error handling
    try:
        assets = sample_target['pair'].split(' - ')
        if len(assets) >= 2:
            asset_a, asset_b = assets[0], assets[1]

            # Check if assets exist in train data
            asset_a_cols = [col for col in train.columns if col.startswith(asset_a)]
            asset_b_cols = [col for col in train.columns if col.startswith(asset_b)]

            print(f"   • Asset A ({asset_a}) columns: {len(asset_a_cols)}")
            print(f"   • Asset B ({asset_b}) columns: {len(asset_b_cols)}")

            if len(asset_a_cols) > 0 and len(asset_b_cols) > 0:
                print(f"   • Assets found in training data ✅")
            else:
                print(f"   • Assets NOT found in training data ⚠️")
        else:
            print(f"   • ⚠️ Could not parse pair format: {sample_target['pair']}")
    except Exception as e:
        print(f"   • ⚠️ Error parsing sample target: {str(e)}")

    return future_leak_risk

# =============================================================================
# CELL 3: Coverage & Integrity Dashboard
# =============================================================================
def cell_3_coverage_integrity_dashboard(train_labels, target_taxonomy):
    """Create comprehensive coverage and integrity dashboard per target"""
    print("="*80)
    print("ADVANCED EDA - CELL 3: COVERAGE & INTEGRITY DASHBOARD")
    print("="*80)

    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    print("1) PER-TARGET COVERAGE ANALYSIS:")
    coverage_stats = {}

    for target in target_cols:
        data = train_labels[target]
        non_missing = data.notna()

        if non_missing.sum() > 0:
            start_date = train_labels.loc[non_missing, 'date_id'].min()
            end_date = train_labels.loc[non_missing, 'date_id'].max()
            coverage_pct = (non_missing.sum() / len(train_labels)) * 100

            # Find gaps
            missing_mask = data.isna()
            gap_starts = []
            gap_lengths = []
            in_gap = False
            gap_start = None

            for i, is_missing in enumerate(missing_mask):
                if is_missing and not in_gap:
                    gap_start = i
                    in_gap = True
                elif not is_missing and in_gap:
                    gap_lengths.append(i - gap_start)
                    gap_starts.append(gap_start)
                    in_gap = False

            if in_gap:  # Gap at end
                gap_lengths.append(len(missing_mask) - gap_start)
                gap_starts.append(gap_start)

            max_gap = max(gap_lengths) if gap_lengths else 0
            n_gaps = len(gap_lengths)

            coverage_stats[target] = {
                'start_date': start_date,
                'end_date': end_date,
                'coverage_pct': coverage_pct,
                'max_gap': max_gap,
                'n_gaps': n_gaps,
                'observations': non_missing.sum()
            }

    coverage_df = pd.DataFrame(coverage_stats).T

    # Merge with taxonomy (handle potential missing targets)
    try:
        coverage_df = coverage_df.merge(target_taxonomy, left_index=True, right_on='target', how='left')
    except Exception as e:
        print(f"   ⚠️ Warning: Could not merge with taxonomy: {str(e)}")
        # Create dummy taxonomy columns
        coverage_df['pair_type'] = 'Unknown'
        coverage_df['lag'] = 0

    print(f"   • Coverage statistics by pair type:")
    try:
        coverage_by_type = coverage_df.groupby('pair_type')['coverage_pct'].agg(['mean', 'min', 'max', 'count'])
        print(coverage_by_type.round(1))
    except Exception as e:
        print(f"   ⚠️ Could not compute coverage by pair type: {str(e)}")

    print(f"\n   • Worst coverage targets:")
    worst_coverage = coverage_df.nsmallest(10, 'coverage_pct')
    for _, row in worst_coverage.iterrows():
        print(f"     {row['target']}: {row['coverage_pct']:.1f}% ({row['pair_type']})")

    print(f"\n2) GAP ANALYSIS:")
    print(f"   • Targets with gaps >10 days: {(coverage_df['max_gap'] > 10).sum()}")
    print(f"   • Targets with >5 gaps: {(coverage_df['n_gaps'] > 5).sum()}")

    # Visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Coverage distribution by pair type
    coverage_df.boxplot(column='coverage_pct', by='pair_type', ax=axes[0,0])
    axes[0,0].set_title('Coverage % by Pair Type')
    axes[0,0].set_xlabel('Pair Type')

    # Max gap distribution
    axes[0,1].hist(coverage_df['max_gap'], bins=30, alpha=0.7, edgecolor='black')
    axes[0,1].set_title('Distribution of Maximum Gaps')
    axes[0,1].set_xlabel('Max Gap (days)')

    # Coverage vs lag
    coverage_df.plot.scatter(x='lag', y='coverage_pct', ax=axes[1,0], alpha=0.6)
    axes[1,0].set_title('Coverage % vs Lag')
    axes[1,0].set_xlabel('Lag (days)')
    axes[1,0].set_ylabel('Coverage %')

    # Number of gaps vs coverage
    coverage_df.plot.scatter(x='coverage_pct', y='n_gaps', ax=axes[1,1], alpha=0.6)
    axes[1,1].set_title('Coverage % vs Number of Gaps')
    axes[1,1].set_xlabel('Coverage %')
    axes[1,1].set_ylabel('Number of Gaps')

    plt.tight_layout()
    plt.show()

    return coverage_df

# =============================================================================
# CELL 4: Small-N Day Anatomy
# =============================================================================
def cell_4_small_n_day_anatomy(train_labels, target_taxonomy):
    """Analyze days with very few available targets"""
    print("="*80)
    print("ADVANCED EDA - CELL 4: SMALL-N DAY ANATOMY")
    print("="*80)

    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    # Calculate daily target availability
    daily_availability = []
    for date_id in sorted(train_labels['date_id'].unique()):
        date_data = train_labels[train_labels['date_id'] == date_id]
        available_targets = date_data[target_cols].notna().sum(axis=1).iloc[0]
        available_target_names = date_data[target_cols].dropna(axis=1).columns.tolist()

        daily_availability.append({
            'date_id': date_id,
            'n_targets': available_targets,
            'available_targets': available_target_names
        })

    availability_df = pd.DataFrame(daily_availability)

    # Identify worst days
    worst_days = availability_df.nsmallest(10, 'n_targets')

    print("1) WORST AVAILABILITY DAYS ANALYSIS:")
    for _, row in worst_days.iterrows():
        date_id = row['date_id']
        n_targets = row['n_targets']
        available_targets = row['available_targets']

        print(f"\n   📅 DATE {date_id}: {n_targets} targets available")

        # Analyze by pair type
        target_analysis = []
        for target in available_targets:
            target_info = target_taxonomy[target_taxonomy['target'] == target]
            if len(target_info) > 0:
                pair_type = target_info.iloc[0]['pair_type']
                lag = target_info.iloc[0]['lag']
                target_analysis.append({'target': target, 'pair_type': pair_type, 'lag': lag})

        target_analysis_df = pd.DataFrame(target_analysis)
        if len(target_analysis_df) > 0:
            pair_type_counts = target_analysis_df['pair_type'].value_counts()
            lag_counts = target_analysis_df['lag'].value_counts()

            print(f"     • Pair types: {dict(pair_type_counts)}")
            print(f"     • Lags: {dict(lag_counts)}")

        # Calculate cross-sectional statistics for this day
        date_data = train_labels[train_labels['date_id'] == date_id][available_targets]
        if len(date_data.columns) > 1:
            cs_data = date_data.iloc[0]
            cs_mean = cs_data.mean()
            cs_std = cs_data.std()
            cs_skew = cs_data.skew()

            print(f"     • Cross-sectional stats: mean={cs_mean:.6f}, std={cs_std:.4f}, skew={cs_skew:.3f}")

        # Check rank persistence if previous day available
        if date_id > 0:
            prev_date_data = train_labels[train_labels['date_id'] == date_id - 1]
            if len(prev_date_data) > 0:
                prev_available = prev_date_data[target_cols].dropna(axis=1).columns.tolist()
                overlap_targets = list(set(available_targets) & set(prev_available))

                if len(overlap_targets) > 2:
                    current_ranks = date_data[overlap_targets].iloc[0].rank()
                    prev_ranks = prev_date_data[overlap_targets].iloc[0].rank()
                    rank_corr = current_ranks.corr(prev_ranks, method='spearman')
                    print(f"     • Rank persistence: {rank_corr:.3f} ({len(overlap_targets)} overlapping targets)")

    return availability_df

# =============================================================================
# CELL 5: FX Triangle Residuals Analysis
# =============================================================================
def cell_5_fx_triangle_residuals(train):
    """Analyze FX triangle arbitrage residuals for quality checks and features"""
    print("="*80)
    print("ADVANCED EDA - CELL 5: FX TRIANGLE RESIDUALS ANALYSIS")
    print("="*80)

    # Get FX columns
    fx_cols = [col for col in train.columns if col.startswith('FX_')]
    fx_pairs = list(set([col.split('_')[1] + '_' + col.split('_')[2] for col in fx_cols]))

    print(f"1) FX MARKET STRUCTURE:")
    print(f"   • Total FX columns: {len(fx_cols)}")
    print(f"   • Unique FX pairs: {len(fx_pairs)}")
    print(f"   • Sample pairs: {fx_pairs[:10]}")

    # Focus on close prices for triangle analysis
    fx_close_cols = [col for col in fx_cols if col.endswith('_close')]

    print(f"\n2) FX CLOSE PRICE ANALYSIS:")
    print(f"   • FX close columns: {len(fx_close_cols)}")

    # Extract currency pairs
    fx_close_pairs = {}
    for col in fx_close_cols:
        parts = col.split('_')
        if len(parts) >= 4:
            pair = parts[1] + parts[2]  # e.g., EURUSD
            fx_close_pairs[pair] = col

    print(f"   • Extracted pairs: {list(fx_close_pairs.keys())[:10]}")

    # Look for triangle opportunities (EUR/USD, USD/JPY, EUR/JPY)
    triangles_found = []
    common_triangles = [
        ('EURUSD', 'USDJPY', 'EURJPY'),
        ('GBPUSD', 'USDJPY', 'GBPJPY'),
        ('AUDUSD', 'USDJPY', 'AUDJPY'),
        ('USDCAD', 'CADJPY', 'USDJPY'),
    ]

    print(f"\n3) TRIANGLE ARBITRAGE ANALYSIS:")
    triangle_residuals = {}

    for base_quote, quote_cross, base_cross in common_triangles:
        if base_quote in fx_close_pairs and quote_cross in fx_close_pairs and base_cross in fx_close_pairs:
            print(f"   • Found triangle: {base_quote} × {quote_cross} = {base_cross}")

            # Get the data
            rate_1 = train[fx_close_pairs[base_quote]]
            rate_2 = train[fx_close_pairs[quote_cross]]
            rate_3 = train[fx_close_pairs[base_cross]]

            # Calculate synthetic rate and residual
            synthetic_rate = rate_1 * rate_2
            residual = np.log(rate_3) - np.log(synthetic_rate)

            # Store residual
            triangle_name = f"{base_quote}x{quote_cross}_{base_cross}"
            triangle_residuals[triangle_name] = residual

            # Statistics
            residual_clean = residual.dropna()
            if len(residual_clean) > 0:
                print(f"     - Residual mean: {residual_clean.mean():.6f}")
                print(f"     - Residual std: {residual_clean.std():.6f}")
                print(f"     - Max |residual|: {residual_clean.abs().max():.6f}")

                # Check for large residuals (potential data errors)
                large_residuals = residual_clean[residual_clean.abs() > 0.01]
                if len(large_residuals) > 0:
                    print(f"     - Large residuals (>1%): {len(large_residuals)} observations")

            triangles_found.append((base_quote, quote_cross, base_cross))

    # Visualization of triangle residuals
    if triangle_residuals:
        n_triangles = len(triangle_residuals)
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        for i, (triangle_name, residual) in enumerate(triangle_residuals.items()):
            if i < 4:  # Plot first 4 triangles
                residual_clean = residual.dropna()

                # Time series plot
                axes[i].plot(residual_clean.index, residual_clean.values, alpha=0.7)
                axes[i].set_title(f'Triangle Residual: {triangle_name}')
                axes[i].set_xlabel('Date Index')
                axes[i].set_ylabel('Log Residual')
                axes[i].grid(True, alpha=0.3)
                axes[i].axhline(0, color='red', linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

    print(f"\n4) TRIANGLE RESIDUALS AS FEATURES:")
    print(f"   • Created {len(triangle_residuals)} triangle residual features")
    print(f"   • These can be used as:")
    print(f"     - Data quality indicators (large residuals = potential errors)")
    print(f"     - Market stress indicators (residual volatility)")
    print(f"     - Arbitrage opportunity features")

    return triangle_residuals, triangles_found

# =============================================================================
# CELL 6: Leak-Free Signal Scouting with Rank-IC Analysis
# =============================================================================
def cell_6_leak_free_signal_scouting(train, train_labels, target_taxonomy):
    """Compute leak-free signals and their rank-IC with targets"""
    print("="*80)
    print("ADVANCED EDA - CELL 6: LEAK-FREE SIGNAL SCOUTING")
    print("="*80)

    print("1) CREATING LEAK-FREE FEATURES:")
    print("   • 1-5 day momentum (log-returns)")
    print("   • Cross-sectional z-scores within asset groups")
    print("   • Relative strength indicators")

    # Get asset classes
    asset_classes = ['LME', 'JPX', 'US_Stock', 'FX']

    # Create momentum features for each asset class
    momentum_features = {}

    for asset_class in asset_classes:
        print(f"\n   Processing {asset_class} assets:")

        # Get close price columns for this asset class
        close_cols = [col for col in train.columns
                     if col.startswith(f'{asset_class}_') and col.endswith('_close')]

        print(f"     • Found {len(close_cols)} close price columns")

        if len(close_cols) > 0:
            # Calculate 1-5 day momentum for each asset
            for lag in [1, 2, 3, 5]:
                for col in close_cols[:10]:  # Limit to first 10 for demo
                    asset_name = col.replace(f'{asset_class}_', '').replace('_close', '')
                    feature_name = f'{asset_class}_{asset_name}_mom_{lag}d'

                    # Calculate log return
                    prices = train[col]
                    momentum = np.log(prices / prices.shift(lag))
                    momentum_features[feature_name] = momentum

    print(f"\n   • Created {len(momentum_features)} momentum features")

    # Calculate rank-IC between features and targets
    print(f"\n2) RANK-IC ANALYSIS:")
    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    # Sample analysis on subset for performance
    sample_targets = target_cols[:20]  # First 20 targets
    sample_features = list(momentum_features.keys())[:50]  # First 50 features

    print(f"   • Analyzing {len(sample_targets)} targets vs {len(sample_features)} features")

    # Align data by date_id
    feature_df = pd.DataFrame(momentum_features)
    feature_df['date_id'] = train['date_id']

    # Merge with labels
    merged_data = train_labels[['date_id'] + sample_targets].merge(
        feature_df[['date_id'] + sample_features], on='date_id', how='inner'
    )

    print(f"   • Merged data shape: {merged_data.shape}")

    # Calculate daily rank correlations
    daily_rank_ics = []

    for date_id in sorted(merged_data['date_id'].unique())[:100]:  # First 100 days for demo
        date_data = merged_data[merged_data['date_id'] == date_id]

        if len(date_data) > 0:
            # Get available targets and features for this day
            available_targets = [col for col in sample_targets
                               if not date_data[col].isna().all()]
            available_features = [col for col in sample_features
                                if not date_data[col].isna().all()]

            if len(available_targets) > 5 and len(available_features) > 5:
                target_data = date_data[available_targets].iloc[0]
                feature_data = date_data[available_features].iloc[0]

                # Calculate rank correlation
                target_ranks = target_data.rank()
                feature_ranks = feature_data.rank()

                # Compute IC for each feature
                feature_ics = {}
                for feature in available_features:
                    if feature in feature_ranks.index:
                        # This is a simplified IC calculation
                        # In practice, you'd correlate each feature with each target
                        ic = np.corrcoef(target_ranks.values,
                                       np.full(len(target_ranks), feature_ranks[feature]))[0,1]
                        if not np.isnan(ic):
                            feature_ics[feature] = ic

                daily_rank_ics.append({
                    'date_id': date_id,
                    'n_targets': len(available_targets),
                    'n_features': len(available_features),
                    'mean_ic': np.mean(list(feature_ics.values())) if feature_ics else 0
                })

    ic_df = pd.DataFrame(daily_rank_ics)

    if len(ic_df) > 0:
        print(f"\n3) RANK-IC RESULTS:")
        print(f"   • Mean daily IC: {ic_df['mean_ic'].mean():.4f}")
        print(f"   • IC standard deviation: {ic_df['mean_ic'].std():.4f}")
        print(f"   • IC Sharpe ratio: {ic_df['mean_ic'].mean() / ic_df['mean_ic'].std():.4f}")

        # Visualization
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))

        # IC over time
        axes[0].plot(ic_df['date_id'], ic_df['mean_ic'], alpha=0.7)
        axes[0].set_title('Daily Mean Rank-IC Over Time')
        axes[0].set_xlabel('Date ID')
        axes[0].set_ylabel('Mean Rank-IC')
        axes[0].grid(True, alpha=0.3)
        axes[0].axhline(0, color='red', linestyle='--', alpha=0.7)

        # IC distribution
        axes[1].hist(ic_df['mean_ic'], bins=20, alpha=0.7, edgecolor='black')
        axes[1].set_title('Distribution of Daily Mean Rank-IC')
        axes[1].set_xlabel('Mean Rank-IC')
        axes[1].set_ylabel('Count')
        axes[1].axvline(0, color='red', linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

    return momentum_features, ic_df

# =============================================================================
# CELL 7: Regime Detection and Market Structure Analysis
# =============================================================================
def cell_7_regime_detection(train_labels, target_taxonomy):
    """Detect market regimes based on dispersion and volatility patterns"""
    print("="*80)
    print("ADVANCED EDA - CELL 7: REGIME DETECTION & MARKET STRUCTURE")
    print("="*80)

    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    print("1) CROSS-SECTIONAL DISPERSION REGIMES:")

    # Calculate daily cross-sectional statistics
    daily_stats = []
    for date_id in sorted(train_labels['date_id'].unique()):
        date_data = train_labels[train_labels['date_id'] == date_id]
        available_data = date_data[target_cols].dropna(axis=1).iloc[0]

        if len(available_data) > 1:
            cs_std = available_data.std()
            cs_mean = available_data.mean()
            cs_skew = available_data.skew()
            cs_kurt = available_data.kurtosis()
            n_targets = len(available_data)

            # Calculate extreme value metrics
            abs_values = available_data.abs()
            p95_abs = abs_values.quantile(0.95)
            max_abs = abs_values.max()

            daily_stats.append({
                'date_id': date_id,
                'cs_std': cs_std,
                'cs_mean': cs_mean,
                'cs_skew': cs_skew,
                'cs_kurt': cs_kurt,
                'n_targets': n_targets,
                'p95_abs': p95_abs,
                'max_abs': max_abs
            })

    regime_df = pd.DataFrame(daily_stats)

    # Define regimes based on dispersion
    regime_df['dispersion_regime'] = pd.cut(regime_df['cs_std'],
                                          bins=3,
                                          labels=['Low', 'Medium', 'High'])

    # Define volatility regimes based on extreme values
    vol_threshold_95 = regime_df['p95_abs'].quantile(0.9)
    regime_df['volatility_regime'] = regime_df['p95_abs'].apply(
        lambda x: 'High' if x > vol_threshold_95 else 'Normal'
    )

    print(f"   • Dispersion regime distribution:")
    disp_counts = regime_df['dispersion_regime'].value_counts()
    for regime, count in disp_counts.items():
        print(f"     - {regime}: {count} days ({count/len(regime_df)*100:.1f}%)")

    print(f"\n   • Volatility regime distribution:")
    vol_counts = regime_df['volatility_regime'].value_counts()
    for regime, count in vol_counts.items():
        print(f"     - {regime}: {count} days ({count/len(regime_df)*100:.1f}%)")

    # Identify the stress period (dates 562-575 from previous analysis)
    stress_period = regime_df[(regime_df['date_id'] >= 560) & (regime_df['date_id'] <= 580)]
    normal_period = regime_df[(regime_df['date_id'] < 560) | (regime_df['date_id'] > 580)]

    print(f"\n2) STRESS PERIOD ANALYSIS:")
    print(f"   • Stress period (dates 560-580): {len(stress_period)} days")
    print(f"   • Normal period: {len(normal_period)} days")

    if len(stress_period) > 0 and len(normal_period) > 0:
        print(f"   • Stress vs Normal dispersion:")
        print(f"     - Normal mean std: {normal_period['cs_std'].mean():.4f}")
        print(f"     - Stress mean std: {stress_period['cs_std'].mean():.4f}")
        print(f"     - Ratio: {stress_period['cs_std'].mean() / normal_period['cs_std'].mean():.1f}x")

        print(f"   • Stress vs Normal extreme values:")
        print(f"     - Normal mean p95: {normal_period['p95_abs'].mean():.4f}")
        print(f"     - Stress mean p95: {stress_period['p95_abs'].mean():.4f}")
        print(f"     - Ratio: {stress_period['p95_abs'].mean() / normal_period['p95_abs'].mean():.1f}x")

    # Regime transition analysis
    print(f"\n3) REGIME TRANSITION ANALYSIS:")
    regime_df['prev_disp_regime'] = regime_df['dispersion_regime'].shift(1)
    regime_df['prev_vol_regime'] = regime_df['volatility_regime'].shift(1)

    # Calculate transition probabilities
    transitions = regime_df.groupby(['prev_disp_regime', 'dispersion_regime']).size().unstack(fill_value=0)
    transition_probs = transitions.div(transitions.sum(axis=1), axis=0)

    print(f"   • Dispersion regime transition probabilities:")
    print(transition_probs.round(3))

    # Visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # Dispersion over time
    axes[0,0].plot(regime_df['date_id'], regime_df['cs_std'], alpha=0.7)
    axes[0,0].set_title('Cross-Sectional Dispersion Over Time')
    axes[0,0].set_xlabel('Date ID')
    axes[0,0].set_ylabel('Cross-Sectional Std')
    axes[0,0].grid(True, alpha=0.3)

    # Extreme values over time
    axes[0,1].plot(regime_df['date_id'], regime_df['p95_abs'], alpha=0.7, label='95th percentile')
    axes[0,1].plot(regime_df['date_id'], regime_df['max_abs'], alpha=0.7, label='Maximum')
    axes[0,1].set_title('Extreme Values Over Time')
    axes[0,1].set_xlabel('Date ID')
    axes[0,1].set_ylabel('|Target Value|')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)

    # Regime distribution
    regime_df['dispersion_regime'].value_counts().plot(kind='bar', ax=axes[0,2])
    axes[0,2].set_title('Dispersion Regime Distribution')
    axes[0,2].set_xlabel('Regime')
    axes[0,2].set_ylabel('Count')

    # Dispersion vs number of targets
    axes[1,0].scatter(regime_df['n_targets'], regime_df['cs_std'], alpha=0.6)
    axes[1,0].set_title('Dispersion vs Number of Targets')
    axes[1,0].set_xlabel('Number of Available Targets')
    axes[1,0].set_ylabel('Cross-Sectional Std')
    axes[1,0].grid(True, alpha=0.3)

    # Regime heatmap
    regime_counts = regime_df.groupby(['dispersion_regime', 'volatility_regime']).size().unstack(fill_value=0)
    sns.heatmap(regime_counts, annot=True, fmt='d', ax=axes[1,1])
    axes[1,1].set_title('Regime Combination Heatmap')

    # Stress period highlighting
    axes[1,2].plot(regime_df['date_id'], regime_df['cs_std'], alpha=0.7)
    stress_mask = (regime_df['date_id'] >= 560) & (regime_df['date_id'] <= 580)
    axes[1,2].scatter(regime_df.loc[stress_mask, 'date_id'],
                     regime_df.loc[stress_mask, 'cs_std'],
                     color='red', s=50, alpha=0.8, label='Stress Period')
    axes[1,2].set_title('Stress Period Identification')
    axes[1,2].set_xlabel('Date ID')
    axes[1,2].set_ylabel('Cross-Sectional Std')
    axes[1,2].legend()
    axes[1,2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    return regime_df

# =============================================================================
# CELL 8: Series Health Check and Structural Breaks
# =============================================================================
def cell_8_series_health_check(train):
    """Identify problematic series with low coverage or structural breaks"""
    print("="*80)
    print("ADVANCED EDA - CELL 8: SERIES HEALTH CHECK")
    print("="*80)

    # Exclude date_id column
    feature_cols = [col for col in train.columns if col != 'date_id']

    print("1) COVERAGE ANALYSIS:")
    coverage_stats = {}

    for col in feature_cols:
        data = train[col]
        non_missing = data.notna()
        coverage_pct = (non_missing.sum() / len(data)) * 100

        # Find longest gap
        missing_mask = data.isna()
        gap_lengths = []
        current_gap = 0

        for is_missing in missing_mask:
            if is_missing:
                current_gap += 1
            else:
                if current_gap > 0:
                    gap_lengths.append(current_gap)
                current_gap = 0

        if current_gap > 0:
            gap_lengths.append(current_gap)

        max_gap = max(gap_lengths) if gap_lengths else 0

        coverage_stats[col] = {
            'coverage_pct': coverage_pct,
            'max_gap': max_gap,
            'n_gaps': len(gap_lengths)
        }

    coverage_df = pd.DataFrame(coverage_stats).T

    # Identify problematic series
    low_coverage_threshold = 50
    high_gap_threshold = 100

    low_coverage_series = coverage_df[coverage_df['coverage_pct'] < low_coverage_threshold]
    high_gap_series = coverage_df[coverage_df['max_gap'] > high_gap_threshold]

    print(f"   • Total features analyzed: {len(coverage_df)}")
    print(f"   • Low coverage (<{low_coverage_threshold}%): {len(low_coverage_series)}")
    print(f"   • High gaps (>{high_gap_threshold} days): {len(high_gap_series)}")

    print(f"\n   • Worst coverage series:")
    worst_coverage = coverage_df.nsmallest(10, 'coverage_pct')
    for series, row in worst_coverage.iterrows():
        print(f"     {series}: {row['coverage_pct']:.1f}% coverage, max gap: {row['max_gap']} days")

    # Asset class breakdown
    print(f"\n2) COVERAGE BY ASSET CLASS:")
    asset_classes = ['LME', 'JPX', 'US_Stock', 'FX']

    for asset_class in asset_classes:
        asset_cols = [col for col in feature_cols if col.startswith(f'{asset_class}_')]
        if len(asset_cols) > 0:
            asset_coverage = coverage_df.loc[asset_cols, 'coverage_pct']
            print(f"   • {asset_class}: {len(asset_cols)} series")
            print(f"     - Mean coverage: {asset_coverage.mean():.1f}%")
            print(f"     - Min coverage: {asset_coverage.min():.1f}%")
            print(f"     - Series <50% coverage: {(asset_coverage < 50).sum()}")

    # Simple structural break detection using variance changes
    print(f"\n3) STRUCTURAL BREAK DETECTION (SIMPLIFIED):")

    # Sample a few series for break detection
    sample_series = feature_cols[:20]  # First 20 series for demo
    break_results = {}

    for col in sample_series:
        data = train[col].dropna()
        if len(data) > 100:  # Need sufficient data
            # Split into two halves and compare variances
            mid_point = len(data) // 2
            first_half = data.iloc[:mid_point]
            second_half = data.iloc[mid_point:]

            if len(first_half) > 10 and len(second_half) > 10:
                var_ratio = second_half.var() / first_half.var()
                mean_shift = abs(second_half.mean() - first_half.mean()) / first_half.std()

                break_results[col] = {
                    'variance_ratio': var_ratio,
                    'mean_shift_zscore': mean_shift,
                    'potential_break': var_ratio > 2 or var_ratio < 0.5 or mean_shift > 2
                }

    break_df = pd.DataFrame(break_results).T
    potential_breaks = break_df[break_df['potential_break'] == True]

    print(f"   • Series analyzed for breaks: {len(break_df)}")
    print(f"   • Potential structural breaks: {len(potential_breaks)}")

    if len(potential_breaks) > 0:
        print(f"   • Series with potential breaks:")
        for series, row in potential_breaks.head().iterrows():
            print(f"     {series}: var_ratio={row['variance_ratio']:.2f}, mean_shift={row['mean_shift_zscore']:.2f}")

    # Visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Coverage distribution
    axes[0,0].hist(coverage_df['coverage_pct'], bins=30, alpha=0.7, edgecolor='black')
    axes[0,0].axvline(low_coverage_threshold, color='red', linestyle='--', label=f'{low_coverage_threshold}% threshold')
    axes[0,0].set_title('Distribution of Series Coverage %')
    axes[0,0].set_xlabel('Coverage %')
    axes[0,0].set_ylabel('Count')
    axes[0,0].legend()

    # Max gap distribution
    axes[0,1].hist(coverage_df['max_gap'], bins=30, alpha=0.7, edgecolor='black')
    axes[0,1].axvline(high_gap_threshold, color='red', linestyle='--', label=f'{high_gap_threshold} day threshold')
    axes[0,1].set_title('Distribution of Maximum Gaps')
    axes[0,1].set_xlabel('Max Gap (days)')
    axes[0,1].set_ylabel('Count')
    axes[0,1].legend()

    # Coverage vs gaps
    axes[1,0].scatter(coverage_df['coverage_pct'], coverage_df['max_gap'], alpha=0.6)
    axes[1,0].set_title('Coverage % vs Maximum Gap')
    axes[1,0].set_xlabel('Coverage %')
    axes[1,0].set_ylabel('Max Gap (days)')
    axes[1,0].grid(True, alpha=0.3)

    # Structural break indicators
    if len(break_df) > 0:
        axes[1,1].scatter(break_df['variance_ratio'], break_df['mean_shift_zscore'], alpha=0.6)
        axes[1,1].axvline(2, color='red', linestyle='--', alpha=0.7)
        axes[1,1].axvline(0.5, color='red', linestyle='--', alpha=0.7)
        axes[1,1].axhline(2, color='red', linestyle='--', alpha=0.7)
        axes[1,1].set_title('Structural Break Indicators')
        axes[1,1].set_xlabel('Variance Ratio (2nd half / 1st half)')
        axes[1,1].set_ylabel('Mean Shift (Z-score)')
        axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # Create drop list
    drop_list = list(low_coverage_series.index) + list(potential_breaks.index)
    drop_list = list(set(drop_list))  # Remove duplicates

    print(f"\n4) RECOMMENDED ACTIONS:")
    print(f"   • Series to consider dropping: {len(drop_list)}")
    print(f"   • Low coverage series: {len(low_coverage_series)}")
    print(f"   • Structural break series: {len(potential_breaks)}")

    return coverage_df, break_df, drop_list

# =============================================================================
# CELL 9: Industry-Grounded Feature Engineering
# =============================================================================
def cell_9_industry_feature_engineering(train, target_pairs):
    """Create industry-specific features based on economic relationships"""
    print("="*80)
    print("ADVANCED EDA - CELL 9: INDUSTRY-GROUNDED FEATURE ENGINEERING")
    print("="*80)

    print("1) METALS-MINERS LINKAGE FEATURES:")

    # Get LME metals and US mining stocks
    lme_cols = [col for col in train.columns if col.startswith('LME_') and col.endswith('_close')]
    mining_stocks = [col for col in train.columns
                    if col.startswith('US_Stock_') and col.endswith('_close') and
                    any(metal in col.upper() for metal in ['COPPER', 'GOLD', 'SILVER', 'FCX', 'NEM', 'SCCO'])]

    print(f"   • LME metals found: {len(lme_cols)}")
    print(f"   • Mining stocks found: {len(mining_stocks)}")

    # Create metal-miner spread features
    metal_miner_features = {}

    # Copper-related features
    copper_cols = [col for col in lme_cols if 'COPPER' in col.upper()]
    copper_miners = [col for col in mining_stocks if any(ticker in col for ticker in ['FCX', 'SCCO'])]

    if len(copper_cols) > 0 and len(copper_miners) > 0:
        print(f"   • Creating copper-miner features:")
        for copper_col in copper_cols:
            for miner_col in copper_miners:
                # Log price ratio
                copper_prices = train[copper_col]
                miner_prices = train[miner_col]

                feature_name = f"copper_miner_spread_{copper_col.split('_')[1]}_{miner_col.split('_')[2]}"
                spread = np.log(copper_prices) - np.log(miner_prices)
                metal_miner_features[feature_name] = spread
                print(f"     - {feature_name}")

    print(f"\n2) FX PASS-THROUGH FEATURES:")

    # Get commodity currencies and related commodities
    commodity_fx = [col for col in train.columns
                   if col.startswith('FX_') and col.endswith('_close') and
                   any(curr in col for curr in ['AUD', 'CAD', 'NZD', 'NOK'])]

    fx_passthrough_features = {}

    # AUD vs metals (Australia is major metals exporter)
    aud_cols = [col for col in commodity_fx if 'AUD' in col]
    if len(aud_cols) > 0 and len(lme_cols) > 0:
        print(f"   • Creating AUD-metals pass-through features:")
        for aud_col in aud_cols:
            for metal_col in lme_cols[:3]:  # First 3 metals
                feature_name = f"aud_metal_passthrough_{aud_col.split('_')[1]}_{metal_col.split('_')[1]}"
                aud_returns = np.log(train[aud_col] / train[aud_col].shift(1))
                metal_returns = np.log(train[metal_col] / train[metal_col].shift(1))

                # Rolling correlation as feature
                rolling_corr = aud_returns.rolling(20).corr(metal_returns)
                fx_passthrough_features[feature_name] = rolling_corr
                print(f"     - {feature_name}")

    print(f"\n3) TERM STRUCTURE FEATURES:")

    # Look for different contract months in JPX futures
    jpx_cols = [col for col in train.columns if col.startswith('JPX_') and col.endswith('_close')]

    term_structure_features = {}

    # Group by underlying asset
    jpx_assets = {}
    for col in jpx_cols:
        parts = col.split('_')
        if len(parts) >= 3:
            asset_base = '_'.join(parts[1:-2])  # Remove JPX prefix and _close suffix
            if asset_base not in jpx_assets:
                jpx_assets[asset_base] = []
            jpx_assets[asset_base].append(col)

    print(f"   • JPX asset groups found: {len(jpx_assets)}")

    # Create term structure spreads for assets with multiple contracts
    for asset, contracts in jpx_assets.items():
        if len(contracts) >= 2:
            print(f"   • Creating term structure for {asset}: {len(contracts)} contracts")

            # Sort contracts (assuming naming convention includes month/year)
            contracts_sorted = sorted(contracts)

            # Create front-back spread
            front_contract = train[contracts_sorted[0]]
            back_contract = train[contracts_sorted[-1]]

            spread_name = f"jpx_term_spread_{asset}"
            term_spread = np.log(back_contract) - np.log(front_contract)
            term_structure_features[spread_name] = term_spread

    print(f"\n4) CROSS-ASSET MOMENTUM FEATURES:")

    # Create momentum features across asset classes
    momentum_features = {}

    asset_classes = {
        'LME': [col for col in train.columns if col.startswith('LME_') and col.endswith('_close')],
        'US_Stock': [col for col in train.columns if col.startswith('US_Stock_') and col.endswith('_close')],
        'FX': [col for col in train.columns if col.startswith('FX_') and col.endswith('_close')]
    }

    # Calculate asset class momentum
    for asset_class, cols in asset_classes.items():
        if len(cols) > 0:
            # Take first few assets for demo
            sample_cols = cols[:5]

            for lag in [1, 5, 10]:
                class_momentum = []

                for col in sample_cols:
                    momentum = np.log(train[col] / train[col].shift(lag))
                    class_momentum.append(momentum)

                if len(class_momentum) > 0:
                    # Average momentum across asset class
                    avg_momentum = pd.concat(class_momentum, axis=1).mean(axis=1)
                    feature_name = f"{asset_class}_momentum_{lag}d"
                    momentum_features[feature_name] = avg_momentum
                    print(f"   • Created {feature_name}")

    # Combine all features
    all_features = {}
    all_features.update(metal_miner_features)
    all_features.update(fx_passthrough_features)
    all_features.update(term_structure_features)
    all_features.update(momentum_features)

    print(f"\n5) FEATURE SUMMARY:")
    print(f"   • Metal-miner features: {len(metal_miner_features)}")
    print(f"   • FX pass-through features: {len(fx_passthrough_features)}")
    print(f"   • Term structure features: {len(term_structure_features)}")
    print(f"   • Cross-asset momentum features: {len(momentum_features)}")
    print(f"   • Total engineered features: {len(all_features)}")

    return all_features

# =============================================================================
# CELL 10: Final Summary and Modeling Recommendations
# =============================================================================
def cell_10_final_summary_and_recommendations():
    """Provide final summary and modeling recommendations"""
    print("="*80)
    print("ADVANCED EDA - CELL 10: FINAL SUMMARY & MODELING RECOMMENDATIONS")
    print("="*80)

    print("🎯 KEY FINDINGS SUMMARY:")
    print("="*50)

    print("\n1) DATA STRUCTURE:")
    print("   ✅ Target calculation verified: log(A[t+lag+1]/A[t+1]) - log(B[t+lag+1]/B[t+1])")
    print("   ✅ Look-ahead structure understood: targets use future prices")
    print("   ✅ 424 targets across 4 lag periods (1-4 days)")
    print("   ⚠️  10.6% missing data overall, some targets 17.9% missing")

    print("\n2) MARKET REGIMES:")
    print("   🔥 Major stress period identified: dates 562-575")
    print("   📊 3.7x dispersion increase during stress")
    print("   🔄 Moderate rank persistence: 0.452 Spearman correlation")
    print("   ⚡ Short half-lives: 1.3 days average")

    print("\n3) DATA QUALITY HIERARCHY:")
    print("   🥇 FX: Excellent coverage, low noise")
    print("   🥈 LME: Good coverage, moderate noise")
    print("   🥉 US_Stock: Variable coverage, some delisted assets")
    print("   🔴 JPX: Moderate issues, gaps present")

    print("\n4) FEATURE ENGINEERING OPPORTUNITIES:")
    print("   🏭 Metal-miner linkages: Strong correlations (0.77-0.89)")
    print("   💱 FX pass-through effects: Commodity currencies")
    print("   📈 Term structure signals: JPX futures spreads")
    print("   🌊 Cross-asset momentum: Asset class rotation")

    print("\n" + "="*50)
    print("🚀 MODELING RECOMMENDATIONS:")
    print("="*50)

    print("\n1) TIME-BASED CROSS-VALIDATION:")
    print("   • Use expanding window CV with stress period isolation")
    print("   • Separate validation for normal vs stress regimes")
    print("   • Respect lag structure: no future leakage")

    print("\n2) FEATURE ENGINEERING PRIORITIES:")
    print("   • Relative returns following target_calculation.py patterns")
    print("   • Cross-sectional z-scores within asset groups")
    print("   • Regime-aware features (normal vs stress indicators)")
    print("   • Industry-grounded relationships (metals-miners, FX-commodities)")

    print("\n3) MODEL ARCHITECTURE:")
    print("   • Ensemble approach: separate models for each regime")
    print("   • Pair-aware models understanding cross-asset relationships")
    print("   • Online learning for adaptation to regime changes")
    print("   • Spearman-optimized loss functions")

    print("\n4) RISK MANAGEMENT:")
    print("   • Monitor for regime transitions using dispersion metrics")
    print("   • Implement coverage-aware predictions (handle missing targets)")
    print("   • Stress-test on identified spike periods")
    print("   • Use triangle residuals for FX data quality monitoring")

    print("\n5) EVALUATION STRATEGY:")
    print("   • Daily Spearman rank correlation as primary metric")
    print("   • Sharpe ratio of daily correlations for stability")
    print("   • Regime-specific performance analysis")
    print("   • Coverage-adjusted metrics for missing targets")

    print("\n" + "="*50)
    print("⚡ IMMEDIATE NEXT STEPS:")
    print("="*50)

    print("\n1. Load target_pairs.csv and verify all target mappings")
    print("2. Implement leak-free feature engineering pipeline")
    print("3. Create regime detection system using dispersion metrics")
    print("4. Build time-based CV framework with proper lag handling")
    print("5. Develop Spearman-optimized baseline model")
    print("6. Test on stress period (dates 562-575) for robustness")

    print("\n" + "="*80)
    print("🎉 ADVANCED EDA COMPLETE - READY FOR MODELING!")
    print("="*80)

# =============================================================================
# MAIN EXECUTION FUNCTION
# =============================================================================
def run_advanced_eda():
    """Execute all advanced EDA cells in sequence"""
    print("🚀 STARTING ADVANCED EDA FOR COMMODITY PRICES COMPETITION")
    print("="*80)

    try:
        # Cell 1: Load data and analyze target pairs
        train, train_labels, target_pairs, target_taxonomy = cell_1_load_and_target_pairs()

        # Cell 2: Alignment audit
        future_leak_risk = cell_2_alignment_audit(train, train_labels, target_pairs)

        # Cell 3: Coverage dashboard
        coverage_df = cell_3_coverage_integrity_dashboard(train_labels, target_taxonomy)

        # Cell 4: Small-N day anatomy
        availability_df = cell_4_small_n_day_anatomy(train_labels, target_taxonomy)

        # Cell 5: FX triangle residuals
        triangle_residuals, triangles_found = cell_5_fx_triangle_residuals(train)

        # Cell 6: Leak-free signal scouting
        momentum_features, ic_df = cell_6_leak_free_signal_scouting(train, train_labels, target_taxonomy)

        # Cell 7: Regime detection
        regime_df = cell_7_regime_detection(train_labels, target_taxonomy)

        # Cell 8: Series health check
        coverage_df_features, break_df, drop_list = cell_8_series_health_check(train)

        # Cell 9: Industry feature engineering
        engineered_features = cell_9_industry_feature_engineering(train, target_pairs)

        # Cell 10: Final summary
        cell_10_final_summary_and_recommendations()

        print("\n✅ ALL ADVANCED EDA CELLS COMPLETED SUCCESSFULLY!")

        return {
            'train': train,
            'train_labels': train_labels,
            'target_pairs': target_pairs,
            'target_taxonomy': target_taxonomy,
            'coverage_df': coverage_df,
            'availability_df': availability_df,
            'triangle_residuals': triangle_residuals,
            'momentum_features': momentum_features,
            'ic_df': ic_df,
            'regime_df': regime_df,
            'engineered_features': engineered_features,
            'drop_list': drop_list,
            'future_leak_risk': future_leak_risk
        }

    except Exception as e:
        print(f"❌ ERROR in advanced EDA: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if __name__ == "__main__":
    # Run the complete advanced EDA
    results = run_advanced_eda()

    if results:
        print("\n🎯 EDA Results available in 'results' dictionary")
        print("📊 Key objects: train, train_labels, target_pairs, regime_df, engineered_features")
    else:
        print("\n❌ EDA failed - check error messages above")