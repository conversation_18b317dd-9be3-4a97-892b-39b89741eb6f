"""
Exploratory Data Analysis for Mi<PERSON>i Commodity Prediction Challenge

This script follows the structured EDA plan to understand:
1. Basic dataset structure and integrity
2. Time coverage and temporal patterns  
3. Identifiers and asset grouping
4. Feature exploration
5. Target and pair exploration
6. Lagged labels and online dynamics
7. Metric and stability diagnostics
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
from typing import Dict, List, Tuple, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from scipy import stats
from statsmodels.tsa.stattools import adfuller
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import networkx as nx

warnings.filterwarnings('ignore')

# Set up paths
DATA_PATH = Path("../../input/mitsui-commodity-prediction-challenge")
OUTPUT_PATH = Path("./eda_outputs")
OUTPUT_PATH.mkdir(exist_ok=True)

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class CommodityEDA:
    """Comprehensive EDA for commodity prediction challenge"""
    
    def __init__(self, data_path: Path):
        self.data_path = data_path
        self.train_df = None
        self.train_labels_df = None
        self.target_pairs_df = None
        self.lagged_dfs = {}
        self.feature_prefixes = ['LME', 'JPX', 'US_Stock', 'FX']
        
    def load_data(self):
        """Load all data files"""
        print("Loading data files...")
        
        # Load main files
        self.train_df = pd.read_csv(self.data_path / "train.csv")
        self.train_labels_df = pd.read_csv(self.data_path / "train_labels.csv")
        self.target_pairs_df = pd.read_csv(self.data_path / "target_pairs.csv")
        
        # Load lagged files
        lagged_path = self.data_path / "lagged_test_labels"
        for lag_file in lagged_path.glob("test_labels_lag_*.csv"):
            lag_num = lag_file.stem.split('_')[-1]
            self.lagged_dfs[f'lag_{lag_num}'] = pd.read_csv(lag_file)
            
        print(f"✓ Loaded train data: {self.train_df.shape}")
        print(f"✓ Loaded train labels: {self.train_labels_df.shape}")
        print(f"✓ Loaded target pairs: {self.target_pairs_df.shape}")
        print(f"✓ Loaded {len(self.lagged_dfs)} lagged files")
        
    def basic_structure_analysis(self):
        """1. Basic Dataset Structure and Integrity"""
        print("\n" + "="*60)
        print("1. BASIC DATASET STRUCTURE AND INTEGRITY")
        print("="*60)
        
        # Dataset shapes and basic info
        datasets = {
            'train': self.train_df,
            'train_labels': self.train_labels_df,
            'target_pairs': self.target_pairs_df
        }
        
        for name, df in datasets.items():
            print(f"\n{name.upper()} Dataset:")
            print(f"  Shape: {df.shape}")
            print(f"  Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
            print(f"  Columns: {list(df.columns[:5])}{'...' if len(df.columns) > 5 else ''}")
            
        # Check for duplicates and missing values
        print(f"\nData Quality Checks:")
        print(f"  Train duplicates: {self.train_df.duplicated().sum()}")
        print(f"  Train missing values: {self.train_df.isnull().sum().sum()}")
        print(f"  Labels missing values: {self.train_labels_df.isnull().sum().sum()}")
        
        # Date ID alignment
        train_dates = set(self.train_df['date_id'])
        label_dates = set(self.train_labels_df['date_id'])
        print(f"  Date ID alignment: {len(train_dates & label_dates)} / {len(train_dates | label_dates)}")
        
        # Feature prefix analysis
        feature_cols = [col for col in self.train_df.columns if col != 'date_id']
        prefix_counts = {}
        for prefix in self.feature_prefixes:
            prefix_counts[prefix] = len([col for col in feature_cols if col.startswith(prefix)])
        
        print(f"\nFeature Distribution by Prefix:")
        for prefix, count in prefix_counts.items():
            print(f"  {prefix}: {count} features")
            
        return prefix_counts
        
    def temporal_analysis(self):
        """2. Time Coverage and Temporal Patterns"""
        print("\n" + "="*60)
        print("2. TIME COVERAGE AND TEMPORAL PATTERNS")
        print("="*60)
        
        # Date range analysis
        date_ids = sorted(self.train_df['date_id'].unique())
        print(f"Date range: {min(date_ids)} to {max(date_ids)}")
        print(f"Total unique dates: {len(date_ids)}")
        print(f"Date gaps: {len(range(min(date_ids), max(date_ids) + 1)) - len(date_ids)}")
        
        # Check for consecutive dates
        gaps = []
        for i in range(1, len(date_ids)):
            gap = date_ids[i] - date_ids[i-1]
            if gap > 1:
                gaps.append((date_ids[i-1], date_ids[i], gap))
        
        if gaps:
            print(f"\nFound {len(gaps)} gaps in date sequence:")
            for start, end, gap_size in gaps[:5]:  # Show first 5 gaps
                print(f"  Gap from {start} to {end}: {gap_size} days")
        
        # Coverage by prefix over time
        self._plot_temporal_coverage()
        
        return date_ids, gaps
        
    def _plot_temporal_coverage(self):
        """Plot feature availability over time by prefix"""
        # Sample every 10th date for performance
        sample_dates = sorted(self.train_df['date_id'].unique())[::10]
        
        coverage_data = []
        for date_id in sample_dates:
            date_data = self.train_df[self.train_df['date_id'] == date_id]
            for prefix in self.feature_prefixes:
                prefix_cols = [col for col in date_data.columns if col.startswith(prefix)]
                if prefix_cols:
                    non_null_count = date_data[prefix_cols].notna().sum().sum()
                    total_count = len(prefix_cols)
                    coverage = non_null_count / total_count if total_count > 0 else 0
                    coverage_data.append({
                        'date_id': date_id,
                        'prefix': prefix,
                        'coverage': coverage
                    })
        
        coverage_df = pd.DataFrame(coverage_data)
        
        # Create coverage heatmap
        fig, ax = plt.subplots(figsize=(15, 8))
        pivot_data = coverage_df.pivot(index='prefix', columns='date_id', values='coverage')
        sns.heatmap(pivot_data, cmap='viridis', cbar_kws={'label': 'Coverage Ratio'}, ax=ax)
        ax.set_title('Feature Coverage Over Time by Asset Class')
        ax.set_xlabel('Date ID (sampled)')
        plt.tight_layout()
        plt.savefig(OUTPUT_PATH / 'temporal_coverage.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def identifier_analysis(self):
        """3. Identifiers and Asset Grouping"""
        print("\n" + "="*60)
        print("3. IDENTIFIERS AND ASSET GROUPING")
        print("="*60)
        
        # Extract unique identifiers by prefix
        identifier_stats = {}
        feature_cols = [col for col in self.train_df.columns if col != 'date_id']
        
        for prefix in self.feature_prefixes:
            prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
            
            # Extract base identifiers (remove suffixes like _open, _close, etc.)
            base_identifiers = set()
            for col in prefix_cols:
                if prefix == 'LME':
                    # LME format: LME_SYMBOL_Close
                    parts = col.split('_')
                    if len(parts) >= 3:
                        base_identifiers.add('_'.join(parts[:2]))
                elif prefix == 'JPX':
                    # JPX format: JPX_Product_Type_Field
                    parts = col.split('_')
                    if len(parts) >= 4:
                        base_identifiers.add('_'.join(parts[:-1]))
                elif prefix == 'US_Stock':
                    # US_Stock format: US_Stock_SYMBOL_adj_field
                    parts = col.split('_')
                    if len(parts) >= 4:
                        base_identifiers.add('_'.join(parts[:3]))
                elif prefix == 'FX':
                    # FX format: FX_PAIR
                    base_identifiers.add(col)
            
            identifier_stats[prefix] = {
                'unique_identifiers': len(base_identifiers),
                'total_features': len(prefix_cols),
                'identifiers': list(base_identifiers)[:10]  # Show first 10
            }
            
        # Print statistics
        for prefix, stats in identifier_stats.items():
            print(f"\n{prefix}:")
            print(f"  Unique identifiers: {stats['unique_identifiers']}")
            print(f"  Total features: {stats['total_features']}")
            print(f"  Avg features per identifier: {stats['total_features']/stats['unique_identifiers']:.1f}")
            print(f"  Sample identifiers: {stats['identifiers']}")
            
        return identifier_stats

    def feature_exploration(self):
        """4. Feature Exploration"""
        print("\n" + "="*60)
        print("4. FEATURE EXPLORATION")
        print("="*60)

        feature_cols = [col for col in self.train_df.columns if col != 'date_id']

        # Overall statistics
        print(f"Total features: {len(feature_cols)}")
        print(f"Missing value statistics:")
        missing_stats = self.train_df[feature_cols].isnull().sum()
        print(f"  Features with no missing values: {(missing_stats == 0).sum()}")
        print(f"  Features with >50% missing: {(missing_stats > len(self.train_df) * 0.5).sum()}")
        print(f"  Average missing rate: {missing_stats.mean() / len(self.train_df):.3f}")

        # Distribution analysis by prefix
        self._analyze_feature_distributions()

        # Correlation analysis
        self._analyze_correlations()

        # Stationarity tests (sample)
        self._test_stationarity()

    def _analyze_feature_distributions(self):
        """Analyze feature distributions by prefix"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, prefix in enumerate(self.feature_prefixes):
            prefix_cols = [col for col in self.train_df.columns if col.startswith(prefix)]
            if prefix_cols:
                # Sample a few columns for distribution analysis
                sample_cols = prefix_cols[:min(5, len(prefix_cols))]
                data = self.train_df[sample_cols].values.flatten()
                data = data[~np.isnan(data)]  # Remove NaN values

                if len(data) > 0:
                    axes[i].hist(data, bins=50, alpha=0.7, density=True)
                    axes[i].set_title(f'{prefix} Feature Distributions')
                    axes[i].set_xlabel('Value')
                    axes[i].set_ylabel('Density')

                    # Add statistics
                    axes[i].axvline(np.mean(data), color='red', linestyle='--', label=f'Mean: {np.mean(data):.2f}')
                    axes[i].axvline(np.median(data), color='green', linestyle='--', label=f'Median: {np.median(data):.2f}')
                    axes[i].legend()

        plt.tight_layout()
        plt.savefig(OUTPUT_PATH / 'feature_distributions.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _analyze_correlations(self):
        """Analyze correlations within and across prefixes"""
        # Sample features for correlation analysis (too many to analyze all)
        sample_features = {}
        for prefix in self.feature_prefixes:
            prefix_cols = [col for col in self.train_df.columns if col.startswith(prefix)]
            sample_features[prefix] = prefix_cols[:min(10, len(prefix_cols))]

        # Create correlation matrix for sampled features
        all_sample_cols = []
        for cols in sample_features.values():
            all_sample_cols.extend(cols)

        if all_sample_cols:
            corr_data = self.train_df[all_sample_cols].corr()

            # Plot correlation heatmap
            fig, ax = plt.subplots(figsize=(12, 10))
            mask = np.triu(np.ones_like(corr_data, dtype=bool))
            sns.heatmap(corr_data, mask=mask, cmap='coolwarm', center=0,
                       square=True, ax=ax, cbar_kws={'label': 'Correlation'})
            ax.set_title('Feature Correlation Matrix (Sample)')
            plt.tight_layout()
            plt.savefig(OUTPUT_PATH / 'correlation_matrix.png', dpi=300, bbox_inches='tight')
            plt.show()

            # Print high correlations
            high_corr_pairs = []
            for i in range(len(corr_data.columns)):
                for j in range(i+1, len(corr_data.columns)):
                    corr_val = corr_data.iloc[i, j]
                    if abs(corr_val) > 0.8 and not np.isnan(corr_val):
                        high_corr_pairs.append((corr_data.columns[i], corr_data.columns[j], corr_val))

            print(f"\nHigh correlations (|r| > 0.8): {len(high_corr_pairs)}")
            for col1, col2, corr in high_corr_pairs[:10]:
                print(f"  {col1} - {col2}: {corr:.3f}")

    def _test_stationarity(self):
        """Test stationarity for sample features"""
        print(f"\nStationarity Tests (ADF):")

        # Sample a few features from each prefix
        test_features = []
        for prefix in self.feature_prefixes:
            prefix_cols = [col for col in self.train_df.columns if col.startswith(prefix)]
            if prefix_cols:
                test_features.extend(prefix_cols[:2])  # Test 2 features per prefix

        stationary_count = 0
        for feature in test_features[:8]:  # Limit to 8 features
            data = self.train_df[feature].dropna()
            if len(data) > 10:
                try:
                    adf_stat, p_value = adfuller(data)[:2]
                    is_stationary = p_value < 0.05
                    stationary_count += is_stationary
                    print(f"  {feature}: {'Stationary' if is_stationary else 'Non-stationary'} (p={p_value:.3f})")
                except:
                    print(f"  {feature}: Test failed")

        print(f"  Stationary features: {stationary_count}/{len(test_features[:8])}")

    def target_exploration(self):
        """5. Target and Pair Exploration"""
        print("\n" + "="*60)
        print("5. TARGET AND PAIR EXPLORATION")
        print("="*60)

        # Target basic statistics
        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')]
        print(f"Total targets: {len(target_cols)}")

        # Missing values in targets
        target_missing = self.train_labels_df[target_cols].isnull().sum()
        print(f"Targets with missing values: {(target_missing > 0).sum()}")
        print(f"Average missing rate in targets: {target_missing.mean() / len(self.train_labels_df):.3f}")

        # Target distribution analysis
        self._analyze_target_distributions()

        # Target pair analysis
        self._analyze_target_pairs()

        # Target correlation analysis
        self._analyze_target_correlations()

    def _analyze_target_distributions(self):
        """Analyze target value distributions"""
        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')]

        # Sample targets for distribution analysis
        sample_targets = target_cols[:20]  # Analyze first 20 targets
        target_data = self.train_labels_df[sample_targets]

        # Plot distributions
        fig, axes = plt.subplots(4, 5, figsize=(20, 16))
        axes = axes.flatten()

        for i, target in enumerate(sample_targets):
            data = target_data[target].dropna()
            if len(data) > 0:
                axes[i].hist(data, bins=30, alpha=0.7, density=True)
                axes[i].set_title(f'{target}')
                axes[i].axvline(data.mean(), color='red', linestyle='--', alpha=0.7)
                axes[i].axvline(data.median(), color='green', linestyle='--', alpha=0.7)

                # Add statistics text
                stats_text = f'μ={data.mean():.3f}\nσ={data.std():.3f}\nSkew={data.skew():.2f}'
                axes[i].text(0.02, 0.98, stats_text, transform=axes[i].transAxes,
                           verticalalignment='top', fontsize=8, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.savefig(OUTPUT_PATH / 'target_distributions.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Overall target statistics
        all_target_data = target_data.values.flatten()
        all_target_data = all_target_data[~np.isnan(all_target_data)]

        print(f"\nOverall Target Statistics:")
        print(f"  Mean: {np.mean(all_target_data):.6f}")
        print(f"  Std: {np.std(all_target_data):.6f}")
        print(f"  Skewness: {stats.skew(all_target_data):.3f}")
        print(f"  Kurtosis: {stats.kurtosis(all_target_data):.3f}")
        print(f"  Range: [{np.min(all_target_data):.6f}, {np.max(all_target_data):.6f}]")

    def _analyze_target_pairs(self):
        """Analyze target pair construction"""
        print(f"\nTarget Pair Analysis:")
        print(f"  Total target pairs defined: {len(self.target_pairs_df)}")

        # Lag distribution
        lag_counts = self.target_pairs_df['lag'].value_counts().sort_index()
        print(f"  Lag distribution:")
        for lag, count in lag_counts.items():
            print(f"    Lag {lag}: {count} targets")

        # Pair type analysis
        single_asset_count = 0
        pair_count = 0

        for _, row in self.target_pairs_df.iterrows():
            if ' - ' in row['pair']:
                pair_count += 1
            else:
                single_asset_count += 1

        print(f"  Single asset targets: {single_asset_count}")
        print(f"  Asset pair targets: {pair_count}")

        # Asset involvement analysis
        all_assets = set()
        for _, row in self.target_pairs_df.iterrows():
            if ' - ' in row['pair']:
                asset1, asset2 = row['pair'].split(' - ')
                all_assets.add(asset1.strip())
                all_assets.add(asset2.strip())
            else:
                all_assets.add(row['pair'].strip())

        print(f"  Unique assets involved: {len(all_assets)}")

        # Most common assets
        asset_counts = {}
        for _, row in self.target_pairs_df.iterrows():
            if ' - ' in row['pair']:
                asset1, asset2 = row['pair'].split(' - ')
                asset_counts[asset1.strip()] = asset_counts.get(asset1.strip(), 0) + 1
                asset_counts[asset2.strip()] = asset_counts.get(asset2.strip(), 0) + 1
            else:
                asset = row['pair'].strip()
                asset_counts[asset] = asset_counts.get(asset, 0) + 1

        top_assets = sorted(asset_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        print(f"  Most frequently used assets:")
        for asset, count in top_assets:
            print(f"    {asset}: {count} times")

    def _analyze_target_correlations(self):
        """Analyze correlations between targets"""
        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')]

        # Sample targets for correlation analysis (too many to analyze all)
        sample_targets = target_cols[:50]  # Analyze first 50 targets
        target_corr = self.train_labels_df[sample_targets].corr()

        # Plot correlation heatmap
        fig, ax = plt.subplots(figsize=(12, 10))
        sns.heatmap(target_corr, cmap='coolwarm', center=0, square=True, ax=ax,
                   cbar_kws={'label': 'Correlation'})
        ax.set_title('Target Correlation Matrix (First 50 Targets)')
        plt.tight_layout()
        plt.savefig(OUTPUT_PATH / 'target_correlations.png', dpi=300, bbox_inches='tight')
        plt.show()

        # High correlation analysis
        high_corr_targets = []
        for i in range(len(target_corr.columns)):
            for j in range(i+1, len(target_corr.columns)):
                corr_val = target_corr.iloc[i, j]
                if abs(corr_val) > 0.7 and not np.isnan(corr_val):
                    high_corr_targets.append((target_corr.columns[i], target_corr.columns[j], corr_val))

        print(f"\nHigh target correlations (|r| > 0.7): {len(high_corr_targets)}")
        for target1, target2, corr in high_corr_targets[:10]:
            print(f"  {target1} - {target2}: {corr:.3f}")

    def lagged_analysis(self):
        """6. Lagged Labels and Online Dynamics"""
        print("\n" + "="*60)
        print("6. LAGGED LABELS AND ONLINE DYNAMICS")
        print("="*60)

        print(f"Available lagged files: {list(self.lagged_dfs.keys())}")

        for lag_name, lag_df in self.lagged_dfs.items():
            print(f"\n{lag_name.upper()}:")
            print(f"  Shape: {lag_df.shape}")
            print(f"  Date range: {lag_df['date_id'].min()} to {lag_df['date_id'].max()}")
            print(f"  Missing values: {lag_df.isnull().sum().sum()}")

        # Analyze lag alignment with main data
        self._analyze_lag_alignment()

        # Autocorrelation analysis
        self._analyze_autocorrelations()

    def _analyze_lag_alignment(self):
        """Analyze alignment between lagged data and main data"""
        print(f"\nLag Alignment Analysis:")

        main_dates = set(self.train_labels_df['date_id'])

        for lag_name, lag_df in self.lagged_dfs.items():
            lag_dates = set(lag_df['date_id'])
            overlap = len(main_dates & lag_dates)
            total_unique = len(main_dates | lag_dates)

            print(f"  {lag_name}: {overlap}/{total_unique} dates overlap ({overlap/total_unique:.1%})")

    def _analyze_autocorrelations(self):
        """Analyze autocorrelations in target series"""
        print(f"\nAutocorrelation Analysis:")

        # Sample a few targets for autocorrelation analysis
        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')]
        sample_targets = target_cols[:5]

        fig, axes = plt.subplots(1, len(sample_targets), figsize=(20, 4))
        if len(sample_targets) == 1:
            axes = [axes]

        for i, target in enumerate(sample_targets):
            data = self.train_labels_df[target].dropna()
            if len(data) > 20:
                # Calculate autocorrelation
                autocorr = [data.autocorr(lag=lag) for lag in range(1, min(21, len(data)//2))]
                lags = list(range(1, len(autocorr) + 1))

                axes[i].bar(lags, autocorr, alpha=0.7)
                axes[i].set_title(f'{target} Autocorrelation')
                axes[i].set_xlabel('Lag')
                axes[i].set_ylabel('Autocorrelation')
                axes[i].axhline(y=0, color='black', linestyle='-', alpha=0.3)
                axes[i].axhline(y=0.05, color='red', linestyle='--', alpha=0.5)
                axes[i].axhline(y=-0.05, color='red', linestyle='--', alpha=0.5)

        plt.tight_layout()
        plt.savefig(OUTPUT_PATH / 'autocorrelations.png', dpi=300, bbox_inches='tight')
        plt.show()

    def metric_analysis(self):
        """7. Metric and Stability Diagnostics"""
        print("\n" + "="*60)
        print("7. METRIC AND STABILITY DIAGNOSTICS")
        print("="*60)

        # Simulate basic metric calculations
        self._simulate_baseline_metrics()

        # Analyze target stability over time
        self._analyze_target_stability()

    def _simulate_baseline_metrics(self):
        """Simulate baseline metric calculations"""
        print("Baseline Metric Simulation:")

        # Get sample targets
        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')]
        sample_targets = target_cols[:10]

        # Simulate predictions (zeros, means, random)
        target_data = self.train_labels_df[sample_targets].dropna()

        if len(target_data) > 0:
            # Zero predictions
            zero_corrs = []
            mean_corrs = []
            random_corrs = []

            for target in sample_targets:
                true_values = target_data[target].values

                # Zero prediction
                zero_pred = np.zeros_like(true_values)
                if np.std(true_values) > 0:
                    zero_corr = np.corrcoef(true_values, zero_pred)[0, 1]
                    zero_corrs.append(zero_corr if not np.isnan(zero_corr) else 0)

                # Mean prediction
                mean_pred = np.full_like(true_values, np.mean(true_values))
                if np.std(true_values) > 0:
                    mean_corr = np.corrcoef(true_values, mean_pred)[0, 1]
                    mean_corrs.append(mean_corr if not np.isnan(mean_corr) else 0)

                # Random prediction
                random_pred = np.random.normal(np.mean(true_values), np.std(true_values), len(true_values))
                if np.std(true_values) > 0:
                    random_corr = np.corrcoef(true_values, random_pred)[0, 1]
                    random_corrs.append(random_corr if not np.isnan(random_corr) else 0)

            print(f"  Zero prediction avg correlation: {np.mean(zero_corrs):.4f}")
            print(f"  Mean prediction avg correlation: {np.mean(mean_corrs):.4f}")
            print(f"  Random prediction avg correlation: {np.mean(random_corrs):.4f}")

    def _analyze_target_stability(self):
        """Analyze target stability over time"""
        print(f"\nTarget Stability Analysis:")

        # Split data into time periods
        date_ids = sorted(self.train_labels_df['date_id'].unique())
        mid_point = len(date_ids) // 2

        early_dates = date_ids[:mid_point]
        late_dates = date_ids[mid_point:]

        early_data = self.train_labels_df[self.train_labels_df['date_id'].isin(early_dates)]
        late_data = self.train_labels_df[self.train_labels_df['date_id'].isin(late_dates)]

        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')][:20]

        stability_metrics = []
        for target in target_cols:
            early_mean = early_data[target].mean()
            late_mean = late_data[target].mean()
            early_std = early_data[target].std()
            late_std = late_data[target].std()

            mean_change = abs(late_mean - early_mean) if not (np.isnan(early_mean) or np.isnan(late_mean)) else np.nan
            std_change = abs(late_std - early_std) if not (np.isnan(early_std) or np.isnan(late_std)) else np.nan

            stability_metrics.append({
                'target': target,
                'mean_change': mean_change,
                'std_change': std_change
            })

        stability_df = pd.DataFrame(stability_metrics)
        avg_mean_change = stability_df['mean_change'].mean()
        avg_std_change = stability_df['std_change'].mean()

        print(f"  Average mean change between periods: {avg_mean_change:.6f}")
        print(f"  Average std change between periods: {avg_std_change:.6f}")

        # Most/least stable targets
        stable_targets = stability_df.nsmallest(3, 'mean_change')
        unstable_targets = stability_df.nlargest(3, 'mean_change')

        print(f"  Most stable targets (by mean):")
        for _, row in stable_targets.iterrows():
            print(f"    {row['target']}: {row['mean_change']:.6f}")

        print(f"  Least stable targets (by mean):")
        for _, row in unstable_targets.iterrows():
            print(f"    {row['target']}: {row['mean_change']:.6f}")

    def advanced_analysis(self):
        """Additional advanced analysis"""
        print("\n" + "="*60)
        print("8. ADVANCED ANALYSIS")
        print("="*60)

        # PCA analysis on features
        self._pca_analysis()

        # Network analysis of target pairs
        self._network_analysis()

        # Feature importance via correlation with targets
        self._feature_target_correlation()

        # Additional commodity-specific analysis
        self._commodity_specific_analysis()

    def _pca_analysis(self):
        """Principal Component Analysis on features"""
        print("PCA Analysis on Features:")

        # Sample features for PCA (memory constraints)
        feature_cols = [col for col in self.train_df.columns if col != 'date_id']
        sample_features = feature_cols[:100]  # Use first 100 features

        # Prepare data
        pca_data = self.train_df[sample_features].fillna(0)  # Fill NaN with 0 for PCA

        if len(pca_data) > 0:
            # Standardize data
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(pca_data)

            # Perform PCA
            pca = PCA()
            pca_result = pca.fit_transform(scaled_data)

            # Plot explained variance
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # Explained variance ratio
            ax1.plot(range(1, min(21, len(pca.explained_variance_ratio_) + 1)),
                    pca.explained_variance_ratio_[:20], 'bo-')
            ax1.set_xlabel('Principal Component')
            ax1.set_ylabel('Explained Variance Ratio')
            ax1.set_title('PCA Explained Variance')
            ax1.grid(True)

            # Cumulative explained variance
            cumsum = np.cumsum(pca.explained_variance_ratio_)
            ax2.plot(range(1, min(21, len(cumsum) + 1)), cumsum[:20], 'ro-')
            ax2.set_xlabel('Principal Component')
            ax2.set_ylabel('Cumulative Explained Variance')
            ax2.set_title('PCA Cumulative Explained Variance')
            ax2.grid(True)

            plt.tight_layout()
            plt.savefig(OUTPUT_PATH / 'pca_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()

            print(f"  First 10 components explain {cumsum[9]:.1%} of variance")
            print(f"  First 20 components explain {cumsum[19] if len(cumsum) > 19 else cumsum[-1]:.1%} of variance")

    def _network_analysis(self):
        """Network analysis of target pairs"""
        print(f"\nNetwork Analysis of Target Pairs:")

        # Create network graph
        G = nx.Graph()

        # Add nodes and edges based on target pairs
        for _, row in self.target_pairs_df.iterrows():
            if ' - ' in row['pair']:
                asset1, asset2 = row['pair'].split(' - ')
                asset1, asset2 = asset1.strip(), asset2.strip()
                G.add_edge(asset1, asset2, target=row['target'], lag=row['lag'])
            else:
                # Single asset - add as isolated node
                G.add_node(row['pair'].strip())

        print(f"  Network nodes (assets): {G.number_of_nodes()}")
        print(f"  Network edges (pairs): {G.number_of_edges()}")

        if G.number_of_nodes() > 0:
            # Calculate network metrics
            if G.number_of_edges() > 0:
                avg_degree = sum(dict(G.degree()).values()) / G.number_of_nodes()
                print(f"  Average degree: {avg_degree:.2f}")

                # Find most connected assets
                degree_centrality = nx.degree_centrality(G)
                top_central = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
                print(f"  Most connected assets:")
                for asset, centrality in top_central:
                    print(f"    {asset}: {centrality:.3f}")

                # Connected components
                components = list(nx.connected_components(G))
                print(f"  Connected components: {len(components)}")
                if components:
                    largest_component = max(components, key=len)
                    print(f"  Largest component size: {len(largest_component)}")

    def _feature_target_correlation(self):
        """Analyze correlation between features and targets"""
        print(f"\nFeature-Target Correlation Analysis:")

        # Sample features and targets for correlation analysis
        feature_cols = [col for col in self.train_df.columns if col != 'date_id']
        target_cols = [col for col in self.train_labels_df.columns if col.startswith('target_')]

        sample_features = feature_cols[:50]  # Sample 50 features
        sample_targets = target_cols[:20]    # Sample 20 targets

        # Merge data on date_id
        merged_data = pd.merge(self.train_df[['date_id'] + sample_features],
                              self.train_labels_df[['date_id'] + sample_targets],
                              on='date_id', how='inner')

        if len(merged_data) > 0:
            # Calculate correlations
            feature_data = merged_data[sample_features]
            target_data = merged_data[sample_targets]

            # Find highest correlations
            max_corrs = []
            for feature in sample_features:
                for target in sample_targets:
                    if feature in merged_data.columns and target in merged_data.columns:
                        corr = merged_data[feature].corr(merged_data[target])
                        if not np.isnan(corr):
                            max_corrs.append((feature, target, abs(corr), corr))

            # Sort by absolute correlation
            max_corrs.sort(key=lambda x: x[2], reverse=True)

            print(f"  Highest feature-target correlations:")
            for feature, target, abs_corr, corr in max_corrs[:10]:
                print(f"    {feature} - {target}: {corr:.3f}")

    def _commodity_specific_analysis(self):
        """Commodity-specific analysis based on domain knowledge"""
        print(f"\nCommodity-Specific Analysis:")

        # Analyze volatility patterns by asset class
        self._analyze_volatility_patterns()

        # Currency impact analysis
        self._analyze_currency_impact()

        # Cross-asset spillover analysis
        self._analyze_spillover_effects()

    def _analyze_volatility_patterns(self):
        """Analyze volatility patterns across asset classes"""
        print("Volatility Analysis by Asset Class:")

        volatility_stats = {}
        for prefix in self.feature_prefixes:
            prefix_cols = [col for col in self.train_df.columns if col.startswith(prefix)]
            if prefix_cols:
                # Sample a few columns for volatility analysis
                sample_cols = prefix_cols[:min(5, len(prefix_cols))]

                volatilities = []
                for col in sample_cols:
                    data = self.train_df[col].dropna()
                    if len(data) > 1:
                        # Calculate rolling volatility (std of returns)
                        returns = data.pct_change().dropna()
                        if len(returns) > 0:
                            volatility = returns.std()
                            volatilities.append(volatility)

                if volatilities:
                    volatility_stats[prefix] = {
                        'mean_volatility': np.mean(volatilities),
                        'median_volatility': np.median(volatilities),
                        'max_volatility': np.max(volatilities)
                    }

        for prefix, stats in volatility_stats.items():
            print(f"  {prefix}:")
            print(f"    Mean volatility: {stats['mean_volatility']:.4f}")
            print(f"    Median volatility: {stats['median_volatility']:.4f}")
            print(f"    Max volatility: {stats['max_volatility']:.4f}")

    def _analyze_currency_impact(self):
        """Analyze impact of currency movements on commodities"""
        print(f"\nCurrency Impact Analysis:")

        # Get FX and commodity features
        fx_cols = [col for col in self.train_df.columns if col.startswith('FX_')]
        lme_cols = [col for col in self.train_df.columns if col.startswith('LME_')]

        if fx_cols and lme_cols:
            # Sample for analysis
            sample_fx = fx_cols[:5]
            sample_lme = lme_cols[:3]

            # Calculate correlations between FX and commodities
            fx_commodity_corrs = []
            for fx_col in sample_fx:
                for lme_col in sample_lme:
                    corr = self.train_df[fx_col].corr(self.train_df[lme_col])
                    if not np.isnan(corr):
                        fx_commodity_corrs.append((fx_col, lme_col, corr))

            # Sort by absolute correlation
            fx_commodity_corrs.sort(key=lambda x: abs(x[2]), reverse=True)

            print(f"  Strongest FX-Commodity correlations:")
            for fx_col, lme_col, corr in fx_commodity_corrs[:5]:
                print(f"    {fx_col} - {lme_col}: {corr:.3f}")

    def _analyze_spillover_effects(self):
        """Analyze spillover effects between asset classes"""
        print(f"\nSpillover Effects Analysis:")

        # Calculate lagged correlations between asset classes
        spillover_results = {}

        for prefix1 in self.feature_prefixes:
            for prefix2 in self.feature_prefixes:
                if prefix1 != prefix2:
                    cols1 = [col for col in self.train_df.columns if col.startswith(prefix1)]
                    cols2 = [col for col in self.train_df.columns if col.startswith(prefix2)]

                    if cols1 and cols2:
                        # Sample one representative from each
                        col1 = cols1[0]
                        col2 = cols2[0]

                        # Calculate lagged correlation
                        data1 = self.train_df[col1].dropna()
                        data2 = self.train_df[col2].dropna()

                        if len(data1) > 10 and len(data2) > 10:
                            # Align data by index
                            common_idx = data1.index.intersection(data2.index)
                            if len(common_idx) > 5:
                                aligned_data1 = data1.loc[common_idx]
                                aligned_data2 = data2.loc[common_idx]

                                # Calculate correlation with 1-day lag
                                if len(aligned_data1) > 1:
                                    lagged_corr = aligned_data1[:-1].corr(aligned_data2[1:])
                                    if not np.isnan(lagged_corr):
                                        spillover_results[f"{prefix1}->{prefix2}"] = lagged_corr

        print(f"  Lagged correlations (1-day lag):")
        for pair, corr in sorted(spillover_results.items(), key=lambda x: abs(x[1]), reverse=True):
            print(f"    {pair}: {corr:.3f}")

    def run_full_analysis(self):
        """Run complete EDA analysis"""
        print("MITSUI COMMODITY PREDICTION CHALLENGE - EXPLORATORY DATA ANALYSIS")
        print("="*80)

        # Load data
        self.load_data()

        # Run all analysis sections
        self.basic_structure_analysis()
        self.temporal_analysis()
        self.identifier_analysis()
        self.feature_exploration()
        self.target_exploration()
        self.lagged_analysis()
        self.metric_analysis()
        self.advanced_analysis()

        print("\n" + "="*80)
        print("EDA ANALYSIS COMPLETE")
        print("="*80)
        print(f"Results saved to: {OUTPUT_PATH}")
        print("\nKey Findings Summary:")
        print("- Check the generated plots and statistics above")
        print("- Pay attention to missing value patterns")
        print("- Note high correlations between features/targets")
        print("- Consider stationarity issues for time series modeling")
        print("- Examine target pair relationships for multi-task learning")


def main():
    """Main execution function"""
    # Initialize EDA
    eda = CommodityEDA(DATA_PATH)

    # Run full analysis
    eda.run_full_analysis()


if __name__ == "__main__":
    main()
