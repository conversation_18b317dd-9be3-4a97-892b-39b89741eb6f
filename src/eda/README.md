# Commodity Prediction Challenge - Exploratory Data Analysis

This directory contains a comprehensive EDA script for the Mitsui Commodity Prediction Challenge, following the structured plan outlined in `plan/eda_plan.md`.

## Files

- `eda.py` - Main EDA script with comprehensive analysis
- `run_eda.py` - Simple runner script
- `requirements.txt` - Required Python packages
- `README.md` - This file

## Features

The EDA script performs the following analyses:

### 1. Basic Dataset Structure and Integrity
- Dataset shapes, memory usage, and basic info
- Data quality checks (duplicates, missing values)
- Date ID alignment between files
- Feature distribution by asset class prefix

### 2. Time Coverage and Temporal Patterns
- Date range analysis and gap detection
- Feature availability over time by asset class
- Temporal coverage heatmaps

### 3. Identifiers and Asset Grouping
- Unique identifier extraction by prefix
- Asset class statistics and distributions
- Cross-asset relationships

### 4. Feature Exploration
- Distribution analysis by asset class
- Correlation analysis within and across prefixes
- Stationarity testing (ADF tests)
- Missing value patterns

### 5. Target and Pair Exploration
- Target distribution analysis
- Target pair construction analysis
- Target correlation analysis
- Asset involvement in target pairs

### 6. Lagged Labels and Online Dynamics
- Lag alignment analysis
- Autocorrelation analysis
- Temporal dependencies

### 7. Metric and Stability Diagnostics
- Baseline metric simulations
- Target stability over time
- Regime change detection

### 8. Advanced Analysis
- Principal Component Analysis (PCA)
- Network analysis of target pairs
- Feature-target correlation analysis

## Usage

### Prerequisites

Install required packages:
```bash
pip install -r requirements.txt
```

### Running the Analysis

From the `src/eda` directory:

```bash
python run_eda.py
```

Or directly:
```bash
python eda.py
```

### Output

The script will:
1. Print comprehensive analysis results to the console
2. Generate visualization plots saved to `./eda_outputs/` directory
3. Provide key insights and recommendations

### Generated Plots

- `temporal_coverage.png` - Feature availability over time
- `feature_distributions.png` - Feature distribution by asset class
- `correlation_matrix.png` - Feature correlation heatmap
- `target_distributions.png` - Target value distributions
- `target_correlations.png` - Target correlation matrix
- `autocorrelations.png` - Target autocorrelation analysis
- `pca_analysis.png` - PCA explained variance plots

## Key Insights Expected

Based on the EDA plan, the analysis will help identify:

1. **Data Quality Issues**: Missing values, gaps, inconsistencies
2. **Asset Class Patterns**: Different behaviors across LME, JPX, US_Stock, FX
3. **Temporal Dynamics**: Non-stationarity, regime changes, seasonality
4. **Cross-Asset Dependencies**: Correlations and spillover effects
5. **Target Relationships**: Pair structures and multi-task learning opportunities
6. **Feature Engineering Opportunities**: Based on correlations and PCA

## Customization

The script is modular and can be easily extended:

- Modify `feature_prefixes` to analyze different asset classes
- Adjust sampling parameters for performance vs. detail trade-offs
- Add custom analysis methods to the `CommodityEDA` class
- Customize visualization parameters and output formats

## Performance Notes

- The script samples features/targets for correlation analysis to manage memory
- Large correlation matrices are computed on subsets
- PCA is performed on first 100 features for computational efficiency
- Adjust sampling parameters based on available computational resources
