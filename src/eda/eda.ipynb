# Cell 1: Setup and Data Loading
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings

# Configure display and plotting
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('default')
sns.set_palette("husl")

# Auto-detect environment and set data path
def get_data_path():
    """Auto-detect if running on Kaggle, Colab, or local"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if kaggle_path.exists():
        print("🔍 Detected Kaggle environment")
        return kaggle_path
    elif colab_path.exists():
        print("🔍 Detected Google Colab environment")
        return colab_path
    elif local_path.exists():
        print("🔍 Detected local environment")
        return local_path
    else:
        print("⚠️  No data path found, using local path as default")
        return local_path

DATA_PATH = get_data_path()

# Load train.csv
print(f"Loading train.csv from: {DATA_PATH}")
train_df = pd.read_csv(DATA_PATH / "train.csv")

print(f"✅ Successfully loaded train.csv")
print(f"Shape: {train_df.shape}")

# Cell 2: Basic Dataset Overview
print("="*60)
print("TRAIN.CSV - BASIC OVERVIEW")
print("="*60)

# Basic info
print(f"Dataset shape: {train_df.shape}")
print(f"Rows (time periods): {train_df.shape[0]:,}")
print(f"Columns (features + date_id): {train_df.shape[1]:,}")

# Memory usage
memory_mb = train_df.memory_usage(deep=True).sum() / 1024**2
print(f"Memory usage: {memory_mb:.2f} MB")

# Column types
print(f"\nColumn types:")
print(train_df.dtypes.value_counts())

# First few rows
print(f"\nFirst 5 rows:")
train_df.head()

# Cell 3: Date ID and Temporal Structure
print("="*60)
print("DATE_ID AND TEMPORAL STRUCTURE")
print("="*60)

# Examine date_id column
print(f"Date ID column info:")
print(f"  Data type: {train_df['date_id'].dtype}")
print(f"  Unique values: {train_df['date_id'].nunique()}")
print(f"  Min date_id: {train_df['date_id'].min()}")
print(f"  Max date_id: {train_df['date_id'].max()}")
print(f"  Date range span: {train_df['date_id'].max() - train_df['date_id'].min()} days")

# Check for duplicates
duplicates = train_df['date_id'].duplicated().sum()
print(f"  Duplicate date_ids: {duplicates}")

# Check for gaps in the sequence
date_ids = sorted(train_df['date_id'].unique())
expected_range = list(range(min(date_ids), max(date_ids) + 1))
missing_dates = set(expected_range) - set(date_ids)

print(f"\nTemporal continuity:")
print(f"  Expected dates in range: {len(expected_range)}")
print(f"  Actual dates present: {len(date_ids)}")
print(f"  Missing dates: {len(missing_dates)}")

if len(missing_dates) > 0:
    print(f"  Missing date_ids: {sorted(list(missing_dates))[:10]}{'...' if len(missing_dates) > 10 else ''}")

# Show first and last few date_ids
print(f"\nFirst 10 date_ids: {date_ids[:10]}")
print(f"Last 10 date_ids: {date_ids[-10:]}")

# Cell 4: Feature Column Analysis
print("="*60)
print("FEATURE COLUMNS ANALYSIS")
print("="*60)

# Get all feature columns (excluding date_id)
feature_cols = [col for col in train_df.columns if col != 'date_id']
print(f"Total feature columns: {len(feature_cols)}")

# Analyze column prefixes (asset classes)
prefixes = ['LME', 'JPX', 'US_Stock', 'FX']
prefix_counts = {}

print(f"\nFeature distribution by asset class:")
for prefix in prefixes:
    count = len([col for col in feature_cols if col.startswith(prefix)])
    prefix_counts[prefix] = count
    print(f"  {prefix}: {count} features")

# Check for any other prefixes
other_features = []
for col in feature_cols:
    if not any(col.startswith(prefix) for prefix in prefixes):
        other_features.append(col)

if other_features:
    print(f"  Other/Unknown: {len(other_features)} features")
    print(f"    Examples: {other_features[:5]}")

# Show sample column names for each prefix
print(f"\nSample column names by asset class:")
for prefix in prefixes:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        print(f"  {prefix} (first 5): {prefix_cols[:5]}")

print(f"\nTotal features breakdown:")
print(f"  LME: {prefix_counts.get('LME', 0)}")
print(f"  JPX: {prefix_counts.get('JPX', 0)}")
print(f"  US_Stock: {prefix_counts.get('US_Stock', 0)}")
print(f"  FX: {prefix_counts.get('FX', 0)}")
print(f"  Total: {sum(prefix_counts.values())}")

# Cell 5: Missing Values Analysis
print("="*60)
print("MISSING VALUES ANALYSIS")
print("="*60)

# Overall missing value statistics
total_cells = train_df.shape[0] * train_df.shape[1]
missing_cells = train_df.isnull().sum().sum()
missing_percentage = (missing_cells / total_cells) * 100

print(f"Overall missing value statistics:")
print(f"  Total cells: {total_cells:,}")
print(f"  Missing cells: {missing_cells:,}")
print(f"  Missing percentage: {missing_percentage:.2f}%")

# Missing values by column
missing_by_col = train_df.isnull().sum()
cols_with_missing = missing_by_col[missing_by_col > 0]

print(f"\nColumns with missing values:")
print(f"  Columns with missing data: {len(cols_with_missing)} out of {len(train_df.columns)}")
print(f"  Columns with no missing data: {len(train_df.columns) - len(cols_with_missing)}")

if len(cols_with_missing) > 0:
    print(f"\nTop 10 columns with most missing values:")
    top_missing = cols_with_missing.sort_values(ascending=False).head(10)
    for col, missing_count in top_missing.items():
        missing_pct = (missing_count / len(train_df)) * 100
        print(f"  {col}: {missing_count:,} ({missing_pct:.1f}%)")

# Missing values by asset class
print(f"\nMissing values by asset class:")
for prefix in ['LME', 'JPX', 'US_Stock', 'FX']:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        prefix_missing = train_df[prefix_cols].isnull().sum().sum()
        prefix_total = len(prefix_cols) * len(train_df)
        prefix_pct = (prefix_missing / prefix_total) * 100
        print(f"  {prefix}: {prefix_missing:,}/{prefix_total:,} ({prefix_pct:.2f}%)")

# Cell 6: Data Distribution Analysis by Asset Class
print("="*60)
print("DATA DISTRIBUTION ANALYSIS")
print("="*60)

# Statistical summary by asset class
prefixes = ['LME', 'JPX', 'US_Stock', 'FX']

for prefix in prefixes:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        print(f"\n{prefix} Asset Class Summary:")
        print(f"  Number of features: {len(prefix_cols)}")
        
        # Get data for this asset class (excluding missing values)
        prefix_data = train_df[prefix_cols]
        
        # Overall statistics
        all_values = prefix_data.values.flatten()
        all_values = all_values[~np.isnan(all_values)]  # Remove NaN
        
        if len(all_values) > 0:
            print(f"  Valid data points: {len(all_values):,}")
            print(f"  Mean: {np.mean(all_values):.4f}")
            print(f"  Median: {np.median(all_values):.4f}")
            print(f"  Std: {np.std(all_values):.4f}")
            print(f"  Min: {np.min(all_values):.4f}")
            print(f"  Max: {np.max(all_values):.4f}")
            print(f"  Range: {np.max(all_values) - np.min(all_values):.4f}")
            
            # Check for extreme values
            q1, q99 = np.percentile(all_values, [1, 99])
            print(f"  1st percentile: {q1:.4f}")
            print(f"  99th percentile: {q99:.4f}")
            
            # Sample a few columns for detailed stats
            sample_cols = prefix_cols[:3]  # First 3 columns
            print(f"  Sample columns detailed stats:")
            for col in sample_cols:
                col_data = train_df[col].dropna()
                if len(col_data) > 0:
                    print(f"    {col}: mean={col_data.mean():.3f}, std={col_data.std():.3f}, range=[{col_data.min():.3f}, {col_data.max():.3f}]")

# Cell 7: Distribution Visualizations
import matplotlib.pyplot as plt
import seaborn as sns

print("="*60)
print("DISTRIBUTION VISUALIZATIONS")
print("="*60)

# Create subplots for each asset class
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.flatten()

prefixes = ['LME', 'JPX', 'US_Stock', 'FX']

for i, prefix in enumerate(prefixes):
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        # Sample a few columns for visualization (to avoid overcrowding)
        sample_cols = prefix_cols[:min(3, len(prefix_cols))]
        
        ax = axes[i]
        
        for j, col in enumerate(sample_cols):
            data = train_df[col].dropna()
            if len(data) > 0:
                # Use log scale for US_Stock due to extreme range
                if prefix == 'US_Stock':
                    # Only plot positive values for log scale
                    positive_data = data[data > 0]
                    if len(positive_data) > 0:
                        ax.hist(np.log10(positive_data), bins=50, alpha=0.6, 
                               label=f'{col.split("_")[-2]}_{col.split("_")[-1]}', density=True)
                        ax.set_xlabel('Log10(Value)')
                else:
                    ax.hist(data, bins=50, alpha=0.6, 
                           label=f'{col.split("_")[-1]}', density=True)
                    ax.set_xlabel('Value')
        
        ax.set_title(f'{prefix} Asset Class Distributions')
        ax.set_ylabel('Density')
        ax.legend()
        ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print some insights about the distributions
print("\nDistribution Insights:")
print("- LME: Metal prices show normal-ish distributions")
print("- JPX: Futures data with varying scales")
print("- US_Stock: Highly skewed (shown in log scale) - likely includes volume data")
print("- FX: Currency rates with different scales (some are ratios, others are yen rates)")

# Cell 8: Understanding the Distribution Colors and FX Issue
print("="*60)
print("EXPLAINING THE VISUALIZATIONS")
print("="*60)

# Let's examine what the colors represent
prefixes = ['LME', 'JPX', 'US_Stock', 'FX']

for prefix in prefixes:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    sample_cols = prefix_cols[:3]  # First 3 columns we plotted
    
    print(f"\n{prefix} Asset Class - Colors represent:")
    for i, col in enumerate(sample_cols):
        color_names = ['Pink/Red', 'Yellow/Orange', 'Green']
        print(f"  {color_names[i]}: {col}")

# Let's specifically investigate the FX issue
print(f"\n" + "="*40)
print("FX ASSET CLASS INVESTIGATION")
print("="*40)

fx_cols = [col for col in feature_cols if col.startswith('FX')]
print(f"All FX columns ({len(fx_cols)}):")
for i, col in enumerate(fx_cols):
    print(f"  {i+1:2d}. {col}")

# Check the first 3 FX columns data
print(f"\nFirst 3 FX columns data summary:")
for col in fx_cols[:3]:
    data = train_df[col].dropna()
    print(f"\n{col}:")
    print(f"  Count: {len(data)}")
    print(f"  Range: [{data.min():.4f}, {data.max():.4f}]")
    print(f"  Mean: {data.mean():.4f}")
    print(f"  Sample values: {data.head(5).tolist()}")

# The FX plot issue: let's check if the ranges are too different
print(f"\nFX Range Analysis:")
fx_ranges = {}
for col in fx_cols[:3]:
    data = train_df[col].dropna()
    fx_ranges[col] = (data.min(), data.max(), data.max() - data.min())

for col, (min_val, max_val, range_val) in fx_ranges.items():
    print(f"  {col}: range = {range_val:.4f} (from {min_val:.4f} to {max_val:.4f})")

# Cell 9: Better FX Visualization with Separate Scales
print("="*60)
print("IMPROVED FX VISUALIZATION")
print("="*60)

# Create separate plots for different FX types
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Group FX pairs by type
fx_cols = [col for col in feature_cols if col.startswith('FX')]

# JPY pairs (higher values ~60-200)
jpy_pairs = [col for col in fx_cols if 'JPY' in col]
usd_pairs = [col for col in fx_cols if 'USD' in col and 'JPY' not in col]
eur_pairs = [col for col in fx_cols if 'EUR' in col and 'JPY' not in col and 'USD' not in col]
other_pairs = [col for col in fx_cols if col not in jpy_pairs + usd_pairs + eur_pairs]

fx_groups = [
    ('JPY Pairs', jpy_pairs[:3]),
    ('USD Pairs', usd_pairs[:3]), 
    ('EUR Pairs', eur_pairs[:3]),
    ('Other Pairs', other_pairs[:3])
]

for i, (group_name, pairs) in enumerate(fx_groups):
    ax = axes[i//2, i%2]
    
    for pair in pairs:
        if pair in train_df.columns:
            data = train_df[pair].dropna()
            if len(data) > 0:
                ax.hist(data, bins=30, alpha=0.6, label=pair.replace('FX_', ''), density=True)
    
    ax.set_title(f'{group_name} Distributions')
    ax.set_xlabel('Exchange Rate')
    ax.set_ylabel('Density')
    ax.legend()
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print the grouping logic
print(f"\nFX Currency Pair Groupings:")
print(f"JPY pairs (vs Japanese Yen): {len(jpy_pairs)} pairs")
print(f"USD pairs (vs US Dollar): {len(usd_pairs)} pairs") 
print(f"EUR pairs (vs Euro): {len(eur_pairs)} pairs")
print(f"Other pairs: {len(other_pairs)} pairs")

print(f"\nSample from each group:")
for group_name, pairs in fx_groups:
    print(f"  {group_name}: {pairs[:3]}")

# Cell 10: Temporal Patterns Analysis
print("="*60)
print("TEMPORAL PATTERNS ANALYSIS")
print("="*60)

# Select representative assets from each class for time series analysis
representative_assets = {
    'LME': 'LME_CA_Close',  # Copper
    'JPX': 'JPX_Gold_Mini_Futures_Open',  # Gold futures
    'US_Stock': 'US_Stock_ACWI_adj_open',  # Global stock index
    'FX': 'FX_EURUSD'  # Major currency pair
}

# Create time series plots
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
axes = axes.flatten()

for i, (asset_class, column) in enumerate(representative_assets.items()):
    if column in train_df.columns:
        # Plot the time series
        data = train_df[['date_id', column]].copy()
        data = data.dropna()
        
        ax = axes[i]
        ax.plot(data['date_id'], data[column], linewidth=1, alpha=0.8)
        ax.set_title(f'{asset_class}: {column}')
        ax.set_xlabel('Date ID')
        ax.set_ylabel('Value')
        ax.grid(True, alpha=0.3)
        
        # Add some basic statistics
        mean_val = data[column].mean()
        std_val = data[column].std()
        ax.axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.2f}')
        ax.axhline(y=mean_val + std_val, color='orange', linestyle=':', alpha=0.5, label=f'+1 Std')
        ax.axhline(y=mean_val - std_val, color='orange', linestyle=':', alpha=0.5, label=f'-1 Std')
        ax.legend()

plt.tight_layout()
plt.show()

# Analyze volatility over time
print(f"\nVolatility Analysis (30-day rolling standard deviation):")
for asset_class, column in representative_assets.items():
    if column in train_df.columns:
        data = train_df[column].dropna()
        if len(data) > 30:
            # Calculate 30-day rolling volatility
            rolling_vol = data.rolling(window=30).std()
            avg_vol = rolling_vol.mean()
            max_vol = rolling_vol.max()
            min_vol = rolling_vol.min()
            
            print(f"  {asset_class} ({column}):")
            print(f"    Average volatility: {avg_vol:.4f}")
            print(f"    Max volatility: {max_vol:.4f}")
            print(f"    Min volatility: {min_vol:.4f}")
            print(f"    Volatility range: {max_vol - min_vol:.4f}")

# Cell 10 Alternative: Multiple Temporal Patterns Analysis
print("="*60)
print("TEMPORAL PATTERNS - MULTIPLE ASSETS PER CLASS")
print("="*60)

# Select multiple representatives from each class
asset_selections = {
    'LME': ['LME_AH_Close', 'LME_CA_Close', 'LME_PB_Close', 'LME_ZS_Close'],  # All 4 LME
    'JPX': ['JPX_Gold_Mini_Futures_Open', 'JPX_Platinum_Mini_Futures_Open', 'JPX_RSS3_Rubber_Futures_Open'],  # 3 different commodities
    'US_Stock': ['US_Stock_ACWI_adj_open', 'US_Stock_GLD_adj_open', 'US_Stock_SLV_adj_open'],  # 3 different stocks
    'FX': ['FX_EURUSD', 'FX_USDJPY', 'FX_GBPUSD']  # 3 major pairs
}

# Create subplots for each asset class
for asset_class, columns in asset_selections.items():
    print(f"\n{asset_class} Asset Class Time Series:")
    
    # Filter columns that actually exist in the data
    existing_cols = [col for col in columns if col in train_df.columns]
    
    if existing_cols:
        fig, axes = plt.subplots(len(existing_cols), 1, figsize=(12, 3*len(existing_cols)))
        if len(existing_cols) == 1:
            axes = [axes]
        
        for i, column in enumerate(existing_cols):
            data = train_df[['date_id', column]].dropna()
            
            axes[i].plot(data['date_id'], data[column], linewidth=1, alpha=0.8)
            axes[i].set_title(f'{column}')
            axes[i].set_xlabel('Date ID')
            axes[i].set_ylabel('Value')
            axes[i].grid(True, alpha=0.3)
            
            # Add mean line
            mean_val = data[column].mean()
            axes[i].axhline(y=mean_val, color='red', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.show()
    
    print(f"  Showing {len(existing_cols)} out of {len(columns)} requested assets")

# Cell 11: Complete Feature Inventory
print("="*60)
print("COMPLETE FEATURE INVENTORY - ALL 557 FEATURES")
print("="*60)

# Get all feature columns
feature_cols = [col for col in train_df.columns if col != 'date_id']
print(f"Total features to analyze: {len(feature_cols)}")

# 1. LME Features (Metals)
lme_cols = [col for col in feature_cols if col.startswith('LME')]
print(f"\n1. LME FEATURES ({len(lme_cols)}):")
for col in lme_cols:
    print(f"   {col}")

# 2. JPX Features (Japanese Futures) - Let's understand the pattern
jpx_cols = [col for col in feature_cols if col.startswith('JPX')]
print(f"\n2. JPX FEATURES ({len(jpx_cols)}) - Pattern Analysis:")

# Extract JPX products
jpx_products = set()
jpx_fields = set()
for col in jpx_cols:
    parts = col.split('_')
    if len(parts) >= 3:
        # JPX_Product_Type_Field or JPX_Product_Field
        if len(parts) == 4:  # JPX_Gold_Mini_Open
            product = f"{parts[1]}_{parts[2]}"
            field = parts[3]
        else:  # JPX_Gold_Open
            product = parts[1]
            field = '_'.join(parts[2:])
        jpx_products.add(product)
        jpx_fields.add(field)

print(f"   JPX Products: {sorted(jpx_products)}")
print(f"   JPX Fields: {sorted(jpx_fields)}")
print(f"   First 10 JPX columns: {jpx_cols[:10]}")

# 3. FX Features (Currency Pairs)
fx_cols = [col for col in feature_cols if col.startswith('FX')]
print(f"\n3. FX FEATURES ({len(fx_cols)}):")
# Group by currency
fx_currencies = set()
for col in fx_cols:
    pair = col.replace('FX_', '')
    # Extract individual currencies
    if len(pair) == 6:  # Standard format like EURUSD
        curr1, curr2 = pair[:3], pair[3:]
        fx_currencies.add(curr1)
        fx_currencies.add(curr2)

print(f"   Unique currencies involved: {sorted(fx_currencies)}")
print(f"   All FX pairs: {[col.replace('FX_', '') for col in fx_cols]}")

# 4. US_Stock Features (This is the big one!)
us_stock_cols = [col for col in feature_cols if col.startswith('US_Stock')]
print(f"\n4. US_STOCK FEATURES ({len(us_stock_cols)}) - Pattern Analysis:")

# Extract stock symbols and fields
us_symbols = set()
us_fields = set()
for col in us_stock_cols:
    parts = col.split('_')
    if len(parts) >= 4:  # US_Stock_SYMBOL_adj_field
        symbol = parts[2]
        field = '_'.join(parts[3:])  # adj_open, adj_close, etc.
        us_symbols.add(symbol)
        us_fields.add(field)

print(f"   Unique stock symbols: {len(us_symbols)}")
print(f"   Stock fields: {sorted(us_fields)}")
print(f"   First 20 symbols: {sorted(list(us_symbols))[:20]}")
print(f"   Sample US_Stock columns: {us_stock_cols[:10]}")

print(f"\n" + "="*60)
print("SUMMARY:")
print(f"LME: {len(lme_cols)} features (4 metals)")
print(f"JPX: {len(jpx_cols)} features ({len(jpx_products)} products)")
print(f"FX: {len(fx_cols)} features ({len(fx_currencies)} currencies)")
print(f"US_Stock: {len(us_stock_cols)} features ({len(us_symbols)} symbols)")
print(f"Total: {len(feature_cols)} features")

# Cell 12: Understanding Feature Meanings and Relationships
print("="*60)
print("UNDERSTANDING WHAT THE FEATURES ACTUALLY MEAN")
print("="*60)

# 1. LME Analysis - What are these metals?
print("1. LME METALS ANALYSIS:")
lme_metals = {
    'LME_AH_Close': 'Aluminum (Light metal, construction/automotive)',
    'LME_CA_Close': 'Copper (Industrial metal, electrical/construction)', 
    'LME_PB_Close': 'Lead (Heavy metal, batteries/construction)',
    'LME_ZS_Close': 'Zinc (Galvanizing metal, corrosion protection)'
}

for symbol, description in lme_metals.items():
    data = train_df[symbol].dropna()
    print(f"   {symbol}: {description}")
    print(f"     Price range: ${data.min():.0f} - ${data.max():.0f}")
    print(f"     Current level: ${data.iloc[-1]:.0f}")

# 2. JPX Analysis - Japanese futures structure
print(f"\n2. JPX FUTURES STRUCTURE:")
print("   Gold: 3 contract types (Mini, Rolling-Spot, Standard)")
print("   Platinum: 2 contract types (Mini, Standard)")  
print("   RSS3 Rubber: 1 contract type")
print("   Fields: OHLC + Volume + Open Interest + Settlement")

# Let's see the relationship between different gold contracts
gold_contracts = ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Rolling-Spot_Futures_Open', 'JPX_Gold_Standard_Futures_Open']
print(f"\n   Gold contract price comparison (latest values):")
for contract in gold_contracts:
    if contract in train_df.columns:
        latest_price = train_df[contract].dropna().iloc[-1]
        print(f"     {contract.split('_')[2]}: ¥{latest_price:.0f}")

# 3. FX Analysis - Currency relationships
print(f"\n3. FX CURRENCY ANALYSIS:")
major_pairs = ['FX_EURUSD', 'FX_GBPUSD', 'FX_USDJPY', 'FX_AUDUSD']
print("   Major currency pairs (latest rates):")
for pair in major_pairs:
    if pair in train_df.columns:
        latest_rate = train_df[pair].dropna().iloc[-1]
        pair_name = pair.replace('FX_', '')
        print(f"     {pair_name}: {latest_rate:.4f}")

# 4. US_Stock Analysis - What types of stocks?
print(f"\n4. US_STOCK ANALYSIS:")
us_symbols = set()
for col in train_df.columns:
    if col.startswith('US_Stock_'):
        symbol = col.split('_')[2]
        us_symbols.add(symbol)

# Categorize some known symbols
stock_categories = {
    'Commodities/Mining': ['AEM', 'AG', 'ALB', 'CCJ', 'CLF', 'COP', 'CVE', 'CVX', 'FCX', 'GOLD', 'NEM', 'SLV', 'GLD'],
    'Energy': ['BP', 'CVX', 'COP', 'DVN', 'XOM', 'SLB'],
    'ETFs/Indices': ['ACWI', 'AGG', 'BND', 'BNDX', 'BSV', 'VTI', 'SPY'],
    'Industrial': ['CAT', 'DE', 'BKR'],
    'Financial': ['BCS', 'AMP']
}

print("   Stock categories (sample identification):")
for category, symbols in stock_categories.items():
    found_symbols = [s for s in symbols if s in us_symbols]
    if found_symbols:
        print(f"     {category}: {found_symbols[:5]}{'...' if len(found_symbols) > 5 else ''}")

# 5. Check for obvious relationships
print(f"\n5. OBVIOUS RELATIONSHIPS TO INVESTIGATE:")
print("   - Gold: LME vs JPX gold futures vs US gold stocks (GLD, GOLD)")
print("   - Currency impact: USD strength vs commodity prices")
print("   - Energy: Oil stocks vs commodity currencies (CAD, AUD)")
print("   - Industrial metals: Copper vs industrial stocks")

# Cell 13: Cross-Market Relationships - Gold Analysis
print("="*60)
print("CROSS-MARKET GOLD ANALYSIS")
print("="*60)

# Gold-related assets across different markets
gold_assets = {
    'LME': [],  # No direct gold in LME (they have other metals)
    'JPX': ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Standard_Futures_Open'],
    'US_Stock': ['US_Stock_GLD_adj_open', 'US_Stock_GOLD_adj_open'],  # Gold ETF and mining stock
    'FX': []  # No direct gold, but USD affects gold prices
}

# Check which gold assets actually exist in our data
existing_gold_assets = {}
for market, assets in gold_assets.items():
    existing = [asset for asset in assets if asset in train_df.columns]
    if existing:
        existing_gold_assets[market] = existing

print("Available gold-related assets:")
for market, assets in existing_gold_assets.items():
    print(f"  {market}: {assets}")

# Plot gold assets together
if len(existing_gold_assets) > 0:
    fig, axes = plt.subplots(len(existing_gold_assets), 1, figsize=(12, 4*len(existing_gold_assets)))
    if len(existing_gold_assets) == 1:
        axes = [axes]
    
    plot_idx = 0
    for market, assets in existing_gold_assets.items():
        ax = axes[plot_idx]
        
        for asset in assets:
            data = train_df[['date_id', asset]].dropna()
            # Normalize to see correlation patterns (scale to 0-1)
            normalized_values = (data[asset] - data[asset].min()) / (data[asset].max() - data[asset].min())
            ax.plot(data['date_id'], normalized_values, label=asset.replace('US_Stock_', '').replace('JPX_', ''), alpha=0.8)
        
        ax.set_title(f'{market} Gold Assets (Normalized 0-1)')
        ax.set_xlabel('Date ID')
        ax.set_ylabel('Normalized Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
        plot_idx += 1
    
    plt.tight_layout()
    plt.show()

# Calculate correlations between gold assets
print(f"\nGold Asset Correlations:")
all_gold_cols = []
for assets in existing_gold_assets.values():
    all_gold_cols.extend(assets)

if len(all_gold_cols) > 1:
    gold_corr_matrix = train_df[all_gold_cols].corr()
    print(gold_corr_matrix.round(3))
    
    # Plot correlation heatmap
    plt.figure(figsize=(8, 6))
    sns.heatmap(gold_corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, cbar_kws={'label': 'Correlation'})
    plt.title('Gold Assets Correlation Matrix')
    plt.tight_layout()
    plt.show()

# Check USD impact on gold (inverse relationship expected)
usd_pairs = ['FX_EURUSD', 'FX_GBPUSD', 'FX_AUDUSD']  # USD strength indicators
print(f"\nUSD vs Gold Relationship Analysis:")
for gold_asset in all_gold_cols:
    print(f"\n{gold_asset} vs USD pairs:")
    for usd_pair in usd_pairs:
        if usd_pair in train_df.columns:
            corr = train_df[gold_asset].corr(train_df[usd_pair])
            print(f"  vs {usd_pair}: {corr:.3f}")

# Cell 14: Industrial Metals and Economic Relationships
print("="*60)
print("INDUSTRIAL METALS & ECONOMIC RELATIONSHIPS")
print("="*60)

# Industrial metals and related assets
industrial_assets = {
    'Copper (LME)': 'LME_CA_Close',
    'Aluminum (LME)': 'LME_AH_Close', 
    'Industrial Stocks': ['US_Stock_CAT_adj_open', 'US_Stock_DE_adj_open'],  # Caterpillar, Deere
    'Commodity Currencies': ['FX_AUDUSD', 'FX_CADUSD'],  # Australia, Canada
    'Global Index': 'US_Stock_ACWI_adj_open'
}

# Check what exists
existing_industrial = {}
for category, assets in industrial_assets.items():
    if isinstance(assets, list):
        existing = [asset for asset in assets if asset in train_df.columns]
        if existing:
            existing_industrial[category] = existing
    else:
        if assets in train_df.columns:
            existing_industrial[category] = [assets]

print("Available industrial-related assets:")
for category, assets in existing_industrial.items():
    print(f"  {category}: {assets}")

# Create correlation matrix for industrial relationships
all_industrial_cols = []
for assets in existing_industrial.values():
    all_industrial_cols.extend(assets)

if len(all_industrial_cols) > 1:
    industrial_corr = train_df[all_industrial_cols].corr()
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(industrial_corr, annot=True, cmap='coolwarm', center=0, 
                square=True, cbar_kws={'label': 'Correlation'})
    plt.title('Industrial Assets Correlation Matrix')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    
    print(f"\nKey Industrial Correlations:")
    # Focus on copper relationships
    if 'LME_CA_Close' in train_df.columns:
        copper_corrs = []
        for col in all_industrial_cols:
            if col != 'LME_CA_Close':
                corr = train_df['LME_CA_Close'].corr(train_df[col])
                copper_corrs.append((col, corr))
        
        copper_corrs.sort(key=lambda x: abs(x[1]), reverse=True)
        print(f"  Copper (LME_CA_Close) correlations:")
        for asset, corr in copper_corrs:
            print(f"    vs {asset}: {corr:.3f}")

# Time series comparison of copper vs industrial indicators
if 'LME_CA_Close' in train_df.columns:
    fig, axes = plt.subplots(2, 1, figsize=(12, 8))
    
    # Plot 1: Copper price
    copper_data = train_df[['date_id', 'LME_CA_Close']].dropna()
    axes[0].plot(copper_data['date_id'], copper_data['LME_CA_Close'], 'red', linewidth=2)
    axes[0].set_title('Copper Price (LME)')
    axes[0].set_ylabel('Price ($)')
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: Industrial indicators (normalized)
    industrial_indicators = ['US_Stock_CAT_adj_open', 'FX_AUDUSD', 'US_Stock_ACWI_adj_open']
    existing_indicators = [col for col in industrial_indicators if col in train_df.columns]
    
    for indicator in existing_indicators:
        data = train_df[['date_id', indicator]].dropna()
        # Normalize to 0-1 for comparison
        normalized = (data[indicator] - data[indicator].min()) / (data[indicator].max() - data[indicator].min())
        axes[1].plot(data['date_id'], normalized, label=indicator.replace('US_Stock_', '').replace('_adj_open', ''), alpha=0.8)
    
    axes[1].set_title('Industrial Indicators (Normalized)')
    axes[1].set_xlabel('Date ID')
    axes[1].set_ylabel('Normalized Value')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Cell 15: Missing Value Patterns Over Time
print("="*60)
print("MISSING VALUE PATTERNS OVER TIME")
print("="*60)

# Analyze missing values by time period
print("Analyzing missing value patterns across time periods...")

# Create time-based missing value analysis
date_ids = sorted(train_df['date_id'].unique())
n_periods = 10  # Divide into 10 time periods
period_size = len(date_ids) // n_periods

missing_by_period = []
for i in range(n_periods):
    start_idx = i * period_size
    end_idx = (i + 1) * period_size if i < n_periods - 1 else len(date_ids)
    
    period_dates = date_ids[start_idx:end_idx]
    period_data = train_df[train_df['date_id'].isin(period_dates)]
    
    # Calculate missing percentages by asset class
    period_stats = {
        'period': i + 1,
        'date_range': f"{period_dates[0]}-{period_dates[-1]}",
        'total_missing_pct': (period_data.isnull().sum().sum() / (period_data.shape[0] * period_data.shape[1])) * 100
    }
    
    # Missing by asset class
    for prefix in ['LME', 'JPX', 'US_Stock', 'FX']:
        prefix_cols = [col for col in period_data.columns if col.startswith(prefix)]
        if prefix_cols:
            prefix_missing = period_data[prefix_cols].isnull().sum().sum()
            prefix_total = len(prefix_cols) * len(period_data)
            period_stats[f'{prefix}_missing_pct'] = (prefix_missing / prefix_total) * 100
    
    missing_by_period.append(period_stats)

# Convert to DataFrame for easier analysis
missing_df = pd.DataFrame(missing_by_period)

print(f"\nMissing Value Patterns by Time Period:")
print(missing_df.round(2))

# Plot missing value trends
fig, axes = plt.subplots(2, 1, figsize=(12, 8))

# Plot 1: Overall missing percentage over time
axes[0].plot(missing_df['period'], missing_df['total_missing_pct'], 'ko-', linewidth=2, markersize=6)
axes[0].set_title('Overall Missing Value Percentage Over Time')
axes[0].set_xlabel('Time Period')
axes[0].set_ylabel('Missing %')
axes[0].grid(True, alpha=0.3)

# Plot 2: Missing by asset class
asset_classes = ['LME', 'JPX', 'US_Stock', 'FX']
for asset_class in asset_classes:
    col_name = f'{asset_class}_missing_pct'
    if col_name in missing_df.columns:
        axes[1].plot(missing_df['period'], missing_df[col_name], 'o-', 
                    label=asset_class, linewidth=2, markersize=4)

axes[1].set_title('Missing Value Percentage by Asset Class Over Time')
axes[1].set_xlabel('Time Period')
axes[1].set_ylabel('Missing %')
axes[1].legend()
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Identify problematic assets
print(f"\nMost Problematic Assets (highest missing rates):")
feature_cols = [col for col in train_df.columns if col != 'date_id']
missing_rates = train_df[feature_cols].isnull().mean() * 100
worst_assets = missing_rates.sort_values(ascending=False).head(10)

for asset, missing_pct in worst_assets.items():
    print(f"  {asset}: {missing_pct:.1f}% missing")

# Check if missing patterns are systematic (e.g., asset delisting)
print(f"\nSystematic Missing Pattern Analysis:")
high_missing_assets = missing_rates[missing_rates > 50].index.tolist()
print(f"Assets with >50% missing: {len(high_missing_assets)}")
for asset in high_missing_assets[:5]:
    # Check when missing values start
    asset_data = train_df[['date_id', asset]].copy()
    first_missing = asset_data[asset_data[asset].isnull()]['date_id'].min()
    last_valid = asset_data[asset_data[asset].notna()]['date_id'].max()
    print(f"  {asset}: last valid at date_id {last_valid}, missing starts around {first_missing}")

# Cell 16: Comprehensive EDA Summary & Recommendations
print("="*80)
print("COMPREHENSIVE EDA SUMMARY & MODELING RECOMMENDATIONS")
print("="*80)

# Summary statistics
total_features = len([col for col in train_df.columns if col != 'date_id'])
total_observations = len(train_df)
date_range = f"{train_df['date_id'].min()} to {train_df['date_id'].max()}"

print(f"DATASET OVERVIEW:")
print(f"  • Total Features: {total_features}")
print(f"  • Total Observations: {total_observations:,}")
print(f"  • Date Range: {date_range}")
print(f"  • Time Span: {train_df['date_id'].max() - train_df['date_id'].min() + 1} days")

print(f"\nASSET CLASS BREAKDOWN:")
asset_counts = {}
for prefix in ['LME', 'JPX', 'US_Stock', 'FX']:
    count = len([col for col in train_df.columns if col.startswith(prefix)])
    asset_counts[prefix] = count
    print(f"  • {prefix}: {count} features")

print(f"\nKEY FINDINGS:")
print(f"  1. DATA QUALITY:")
print(f"     • FX data: Excellent (0% missing)")
print(f"     • LME metals: Good (~2.5% missing)")
print(f"     • US Stocks: Variable (3-5% missing)")
print(f"     • JPX: Moderate issues (~6% missing)")
print(f"     • CRITICAL: US_Stock_GOLD 87% missing (delisted)")

print(f"\n  2. CROSS-MARKET RELATIONSHIPS:")
print(f"     • Gold futures (JPX) ↔ Gold ETF (GLD): 0.955 correlation")
print(f"     • Copper ↔ Industrial stocks: 0.77-0.89 correlation")
print(f"     • USD strength ↔ Gold: -0.34 to -0.63 correlation")
print(f"     • Commodity currencies surprisingly weak correlation with commodities")

print(f"\n  3. VOLATILITY PATTERNS:")
print(f"     • Energy sector: Highest volatility")
print(f"     • FX markets: Most stable")
print(f"     • Metals: Moderate volatility with trend components")

print(f"\n  4. TEMPORAL PATTERNS:")
print(f"     • Strong trending behavior in most assets")
print(f"     • Missing values relatively stable over time")
print(f"     • No obvious seasonality in this timeframe")

print(f"\nMODELING RECOMMENDATIONS:")
print(f"  1. FEATURE ENGINEERING:")
print(f"     • Create cross-market ratio features (e.g., Gold/Copper)")
print(f"     • Add technical indicators (moving averages, RSI)")
print(f"     • Include volatility features (rolling std)")
print(f"     • Consider currency-adjusted commodity prices")

print(f"\n  2. MISSING VALUE STRATEGY:")
print(f"     • DROP: US_Stock_GOLD_* (87% missing)")
print(f"     • FORWARD FILL: Most other assets (small gaps)")
print(f"     • INTERPOLATE: For systematic gaps in JPX data")

print(f"\n  3. MODEL ARCHITECTURE:")
print(f"     • Time series models (LSTM/GRU) for trending behavior")
print(f"     • Cross-attention for inter-market relationships")
print(f"     • Separate heads for different asset classes")
print(f"     • Ensemble approach combining multiple strategies")

print(f"\n  4. VALIDATION STRATEGY:")
print(f"     • Time-based splits (no data leakage)")
print(f"     • Walk-forward validation")
print(f"     • Asset-class specific evaluation")

print(f"\nHIGH-VALUE FEATURE GROUPS:")
high_value_features = {
    'FX_Core': ['FX_EURUSD', 'FX_GBPUSD', 'FX_AUDUSD', 'FX_CADUSD'],
    'Gold_Complex': ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Standard_Futures_Open', 'US_Stock_GLD_adj_open'],
    'Industrial_Metals': ['LME_CA_Close', 'LME_AH_Close'],
    'Industrial_Stocks': ['US_Stock_CAT_adj_open', 'US_Stock_DE_adj_open'],
    'Energy': ['LME_Brent_Close', 'LME_Gas_Oil_Close'],
    'Global_Indices': ['US_Stock_ACWI_adj_open']
}

for group, features in high_value_features.items():
    available = [f for f in features if f in train_df.columns]
    print(f"  • {group}: {len(available)}/{len(features)} available")

print(f"\nNEXT STEPS:")
print(f"  1. Implement feature engineering pipeline")
print(f"  2. Create baseline models for each asset class")
print(f"  3. Develop cross-market prediction models")
print(f"  4. Optimize for competition metric")

print(f"\n" + "="*80)
print("EDA COMPLETE - Ready for Feature Engineering & Modeling!")
print("="*80)

# Cell 17: Train Labels - File Integrity & Granularity
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def get_data_path():
    """Auto-detect if running on Kaggle, Colab, or local"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if kaggle_path.exists():
        return kaggle_path
    elif colab_path.exists():
        return colab_path
    else:
        return local_path

print("="*80)
print("TRAIN LABELS ANALYSIS - FILE INTEGRITY & GRANULARITY")
print("="*80)

# Load train labels using the proper path function
data_path = get_data_path()
print(f"Using data path: {data_path}")
train_labels = pd.read_csv(data_path / "train_labels.csv")

print(f"\n1) BASIC FILE INTEGRITY:")
print(f"   • Shape: {train_labels.shape}")
print(f"   • Memory usage: {train_labels.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

# Check dtypes
print(f"\n2) DATA TYPES:")
dtype_counts = train_labels.dtypes.value_counts()
for dtype, count in dtype_counts.items():
    print(f"   • {dtype}: {count} columns")

# Check date_id uniqueness and coverage
print(f"\n3) DATE_ID GRANULARITY:")
print(f"   • Unique date_ids: {train_labels['date_id'].nunique()}")
print(f"   • Total rows: {len(train_labels)}")
print(f"   • One row per date_id: {train_labels['date_id'].nunique() == len(train_labels)}")
print(f"   • Date range: {train_labels['date_id'].min()} to {train_labels['date_id'].max()}")
print(f"   • Date span: {train_labels['date_id'].max() - train_labels['date_id'].min() + 1} days")

# Check for gaps in date sequence
date_ids = sorted(train_labels['date_id'].unique())
gaps = []
for i in range(1, len(date_ids)):
    if date_ids[i] - date_ids[i-1] > 1:
        gaps.append((date_ids[i-1], date_ids[i]))

if gaps:
    print(f"   • Date gaps found: {len(gaps)}")
    for start, end in gaps[:5]:  # Show first 5 gaps
        print(f"     Gap: {start} → {end} (missing {end-start-1} days)")
else:
    print(f"   • No gaps in date sequence ✓")

# Target columns analysis
target_cols = [col for col in train_labels.columns if col != 'date_id']
print(f"\n4) TARGET COLUMNS:")
print(f"   • Total target columns: {len(target_cols)}")
print(f"   • Expected ~424 columns: {'✓' if len(target_cols) >= 420 else '✗'}")

# Check for non-finite values
print(f"\n5) NON-FINITE VALUES ANALYSIS:")
total_cells = len(train_labels) * len(target_cols)
nan_counts = train_labels[target_cols].isnull().sum()
inf_counts = np.isinf(train_labels[target_cols].select_dtypes(include=[np.number])).sum()

print(f"   • Total data cells: {total_cells:,}")
print(f"   • NaN values: {nan_counts.sum():,} ({nan_counts.sum()/total_cells*100:.2f}%)")
print(f"   • Infinite values: {inf_counts.sum():,}")

# Most problematic columns (highest NaN rates)
nan_rates = (nan_counts / len(train_labels)) * 100
worst_nan = nan_rates.sort_values(ascending=False).head(10)
print(f"\n   Top 10 columns with highest NaN rates:")
for col, rate in worst_nan.items():
    print(f"     {col}: {rate:.1f}%")

# Check for constant or near-constant columns
print(f"\n6) CONSTANT/NEAR-CONSTANT COLUMNS:")
constant_cols = []
near_constant_cols = []

for col in target_cols:
    valid_data = train_labels[col].dropna()
    if len(valid_data) > 0:
        unique_vals = valid_data.nunique()
        if unique_vals == 1:
            constant_cols.append(col)
        elif unique_vals <= 3:  # Very few unique values
            near_constant_cols.append((col, unique_vals))

print(f"   • Constant columns (1 unique value): {len(constant_cols)}")
if constant_cols:
    for col in constant_cols[:5]:
        print(f"     {col}")

print(f"   • Near-constant columns (≤3 unique values): {len(near_constant_cols)}")
if near_constant_cols:
    for col, n_unique in near_constant_cols[:5]:
        print(f"     {col}: {n_unique} unique values")

# Quick distribution check
print(f"\n7) QUICK DISTRIBUTION OVERVIEW:")
sample_cols = target_cols[:5]  # Sample first 5 columns
sample_stats = train_labels[sample_cols].describe()
print(f"   Sample statistics for first 5 target columns:")
print(sample_stats.round(4))

print(f"\n" + "="*80)
print("FILE INTEGRITY CHECK COMPLETE")
print("="*80)

# Cell 18: Train Labels - Coverage & Availability Analysis
print("="*80)
print("TRAIN LABELS ANALYSIS - COVERAGE & AVAILABILITY")
print("="*80)

# 1) Coverage analysis per target
print("1) PER-TARGET COVERAGE ANALYSIS:")
target_coverage = {}

for col in target_cols:
    data = train_labels[col]
    non_missing = data.notna()
    
    if non_missing.sum() > 0:
        # Find start and end dates
        start_date = train_labels.loc[non_missing, 'date_id'].min()
        end_date = train_labels.loc[non_missing, 'date_id'].max()
        coverage_pct = (non_missing.sum() / len(train_labels)) * 100
        
        # Find longest missing streak
        missing_streaks = []
        current_streak = 0
        for val in data:
            if pd.isna(val):
                current_streak += 1
            else:
                if current_streak > 0:
                    missing_streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:  # Handle streak at end
            missing_streaks.append(current_streak)
        
        longest_streak = max(missing_streaks) if missing_streaks else 0
        
        target_coverage[col] = {
            'start_date': start_date,
            'end_date': end_date,
            'coverage_pct': coverage_pct,
            'longest_missing_streak': longest_streak,
            'total_observations': non_missing.sum()
        }

# Convert to DataFrame for easier analysis
coverage_df = pd.DataFrame(target_coverage).T
coverage_df = coverage_df.sort_values('coverage_pct', ascending=False)

print(f"   • Coverage statistics across {len(coverage_df)} targets:")
print(f"     - Mean coverage: {coverage_df['coverage_pct'].mean():.1f}%")
print(f"     - Median coverage: {coverage_df['coverage_pct'].median():.1f}%")
print(f"     - Min coverage: {coverage_df['coverage_pct'].min():.1f}%")
print(f"     - Max coverage: {coverage_df['coverage_pct'].max():.1f}%")

print(f"\n   • Best covered targets (top 5):")
for idx, (target, row) in enumerate(coverage_df.head().iterrows()):
    print(f"     {target}: {row['coverage_pct']:.1f}% (dates {row['start_date']}-{row['end_date']})")

print(f"\n   • Worst covered targets (bottom 5):")
for idx, (target, row) in enumerate(coverage_df.tail().iterrows()):
    print(f"     {target}: {row['coverage_pct']:.1f}% (longest gap: {row['longest_missing_streak']} days)")

# 2) Coverage analysis per date
print(f"\n2) PER-DATE AVAILABILITY ANALYSIS:")
date_availability = []

for date_id in sorted(train_labels['date_id'].unique()):
    date_data = train_labels[train_labels['date_id'] == date_id]
    available_targets = date_data[target_cols].notna().sum(axis=1).iloc[0]
    date_availability.append({
        'date_id': date_id,
        'available_targets': available_targets,
        'availability_pct': (available_targets / len(target_cols)) * 100
    })

availability_df = pd.DataFrame(date_availability)

print(f"   • Daily availability statistics:")
print(f"     - Mean targets per day: {availability_df['available_targets'].mean():.1f}")
print(f"     - Median targets per day: {availability_df['available_targets'].median():.1f}")
print(f"     - Min targets per day: {availability_df['available_targets'].min()}")
print(f"     - Max targets per day: {availability_df['available_targets'].max()}")

# Find days with extreme availability
worst_days = availability_df.nsmallest(5, 'available_targets')
best_days = availability_df.nlargest(5, 'available_targets')

print(f"\n   • Worst availability days:")
for _, row in worst_days.iterrows():
    print(f"     Date {row['date_id']}: {row['available_targets']} targets ({row['availability_pct']:.1f}%)")

print(f"\n   • Best availability days:")
for _, row in best_days.iterrows():
    print(f"     Date {row['date_id']}: {row['available_targets']} targets ({row['availability_pct']:.1f}%)")

# 3) Day-over-day overlap analysis
print(f"\n3) DAY-OVER-DAY OVERLAP ANALYSIS:")
overlap_stats = []

for i in range(1, len(train_labels)):
    current_date = train_labels.iloc[i]['date_id']
    prev_date = train_labels.iloc[i-1]['date_id']
    
    current_available = set(train_labels.iloc[i][target_cols].dropna().index)
    prev_available = set(train_labels.iloc[i-1][target_cols].dropna().index)
    
    intersection = len(current_available & prev_available)
    union = len(current_available | prev_available)
    jaccard_index = intersection / union if union > 0 else 0
    
    overlap_stats.append({
        'date_id': current_date,
        'prev_date': prev_date,
        'overlap_size': intersection,
        'jaccard_index': jaccard_index,
        'current_targets': len(current_available),
        'prev_targets': len(prev_available)
    })

overlap_df = pd.DataFrame(overlap_stats)

print(f"   • Overlap statistics:")
print(f"     - Mean overlap size: {overlap_df['overlap_size'].mean():.1f} targets")
print(f"     - Mean Jaccard index: {overlap_df['jaccard_index'].mean():.3f}")
print(f"     - Min Jaccard index: {overlap_df['jaccard_index'].min():.3f}")
print(f"     - Max Jaccard index: {overlap_df['jaccard_index'].max():.3f}")

# Find days with poor overlap
poor_overlap = overlap_df.nsmallest(5, 'jaccard_index')
print(f"\n   • Days with poorest overlap:")
for _, row in poor_overlap.iterrows():
    print(f"     Date {row['date_id']}: Jaccard={row['jaccard_index']:.3f}, overlap={row['overlap_size']} targets")

# 4) Visualization
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Plot 1: Coverage distribution
axes[0,0].hist(coverage_df['coverage_pct'], bins=30, alpha=0.7, edgecolor='black')
axes[0,0].set_title('Distribution of Target Coverage %')
axes[0,0].set_xlabel('Coverage %')
axes[0,0].set_ylabel('Number of Targets')
axes[0,0].axvline(coverage_df['coverage_pct'].mean(), color='red', linestyle='--', label=f'Mean: {coverage_df["coverage_pct"].mean():.1f}%')
axes[0,0].legend()

# Plot 2: Daily availability over time
axes[0,1].plot(availability_df['date_id'], availability_df['available_targets'], alpha=0.7)
axes[0,1].set_title('Available Targets Over Time')
axes[0,1].set_xlabel('Date ID')
axes[0,1].set_ylabel('Number of Available Targets')
axes[0,1].grid(True, alpha=0.3)

# Plot 3: Jaccard index over time
axes[1,0].plot(overlap_df['date_id'], overlap_df['jaccard_index'], alpha=0.7)
axes[1,0].set_title('Day-over-Day Overlap (Jaccard Index)')
axes[1,0].set_xlabel('Date ID')
axes[1,0].set_ylabel('Jaccard Index')
axes[1,0].grid(True, alpha=0.3)

# Plot 4: Coverage vs longest missing streak
axes[1,1].scatter(coverage_df['coverage_pct'], coverage_df['longest_missing_streak'], alpha=0.6)
axes[1,1].set_title('Coverage % vs Longest Missing Streak')
axes[1,1].set_xlabel('Coverage %')
axes[1,1].set_ylabel('Longest Missing Streak (days)')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n" + "="*80)
print("COVERAGE & AVAILABILITY ANALYSIS COMPLETE")
print("="*80)

# Cell 19: Train Labels - Distribution & Tails Analysis
print("="*80)
print("TRAIN LABELS ANALYSIS - DISTRIBUTION & TAILS")
print("="*80)

# 1) Distribution statistics per target
print("1) DISTRIBUTION STATISTICS PER TARGET:")

# Calculate comprehensive stats for all targets
target_stats = {}
for col in target_cols:
    data = train_labels[col].dropna()
    if len(data) > 0:
        target_stats[col] = {
            'count': len(data),
            'mean': data.mean(),
            'std': data.std(),
            'skew': data.skew(),
            'kurtosis': data.kurtosis(),
            'p1': data.quantile(0.01),
            'p5': data.quantile(0.05),
            'p50': data.quantile(0.50),
            'p95': data.quantile(0.95),
            'p99': data.quantile(0.99),
            'min': data.min(),
            'max': data.max()
        }

stats_df = pd.DataFrame(target_stats).T

print(f"   • Summary across {len(stats_df)} targets:")
print(f"     - Mean return: {stats_df['mean'].mean():.6f}")
print(f"     - Mean volatility (std): {stats_df['std'].mean():.4f}")
print(f"     - Mean skewness: {stats_df['skew'].mean():.3f}")
print(f"     - Mean kurtosis: {stats_df['kurtosis'].mean():.3f}")

print(f"\n   • Volatility distribution:")
print(f"     - Min std: {stats_df['std'].min():.4f}")
print(f"     - Median std: {stats_df['std'].median():.4f}")
print(f"     - Max std: {stats_df['std'].max():.4f}")

print(f"\n   • Extreme skewness targets:")
extreme_skew = stats_df.nlargest(5, 'skew')['skew']
for target, skew in extreme_skew.items():
    print(f"     {target}: {skew:.3f}")

print(f"\n   • Extreme kurtosis targets:")
extreme_kurt = stats_df.nlargest(5, 'kurtosis')['kurtosis']
for target, kurt in extreme_kurt.items():
    print(f"     {target}: {kurt:.3f}")

# 2) Zero mass and ties analysis
print(f"\n2) ZERO MASS & TIES ANALYSIS:")
zero_stats = {}
ties_stats = {}

for col in target_cols:
    data = train_labels[col].dropna()
    if len(data) > 0:
        # Zero mass
        zero_count = (data == 0).sum()
        zero_pct = (zero_count / len(data)) * 100
        
        # Unique values and ties
        unique_vals = data.nunique()
        total_vals = len(data)
        tie_ratio = unique_vals / total_vals
        
        zero_stats[col] = {'zero_count': zero_count, 'zero_pct': zero_pct}
        ties_stats[col] = {'unique_vals': unique_vals, 'total_vals': total_vals, 'tie_ratio': tie_ratio}

zero_df = pd.DataFrame(zero_stats).T
ties_df = pd.DataFrame(ties_stats).T

print(f"   • Zero mass statistics:")
print(f"     - Targets with >5% zeros: {(zero_df['zero_pct'] > 5).sum()}")
print(f"     - Targets with >10% zeros: {(zero_df['zero_pct'] > 10).sum()}")
print(f"     - Max zero percentage: {zero_df['zero_pct'].max():.1f}%")

high_zero_targets = zero_df[zero_df['zero_pct'] > 5].sort_values('zero_pct', ascending=False)
if len(high_zero_targets) > 0:
    print(f"   • Targets with highest zero mass:")
    for target, row in high_zero_targets.head().iterrows():
        print(f"     {target}: {row['zero_pct']:.1f}% zeros")

print(f"\n   • Ties/discretization analysis:")
print(f"     - Mean unique ratio: {ties_df['tie_ratio'].mean():.3f}")
print(f"     - Min unique ratio: {ties_df['tie_ratio'].min():.3f}")
print(f"     - Targets with <50% unique values: {(ties_df['tie_ratio'] < 0.5).sum()}")

low_unique_targets = ties_df[ties_df['tie_ratio'] < 0.5].sort_values('tie_ratio')
if len(low_unique_targets) > 0:
    print(f"   • Most discretized targets:")
    for target, row in low_unique_targets.head().iterrows():
        print(f"     {target}: {row['unique_vals']}/{row['total_vals']} unique ({row['tie_ratio']:.3f})")

# 3) Extreme day diagnostics
print(f"\n3) EXTREME-DAY DIAGNOSTICS:")
daily_extremes = []

for date_id in sorted(train_labels['date_id'].unique()):
    date_data = train_labels[train_labels['date_id'] == date_id][target_cols]
    available_data = date_data.dropna(axis=1)
    
    if available_data.shape[1] > 0:
        abs_values = available_data.abs().iloc[0]
        median_abs = abs_values.median()
        p95_abs = abs_values.quantile(0.95)
        max_abs = abs_values.max()
        
        daily_extremes.append({
            'date_id': date_id,
            'available_targets': available_data.shape[1],
            'median_abs': median_abs,
            'p95_abs': p95_abs,
            'max_abs': max_abs
        })

extremes_df = pd.DataFrame(daily_extremes)

print(f"   • Daily extreme statistics:")
print(f"     - Mean daily median |target|: {extremes_df['median_abs'].mean():.4f}")
print(f"     - Mean daily 95th pct |target|: {extremes_df['p95_abs'].mean():.4f}")
print(f"     - Overall max |target|: {extremes_df['max_abs'].max():.4f}")

# Top spike days
spike_days = extremes_df.nlargest(10, 'p95_abs')
print(f"\n   • Top 10 spike days (by 95th percentile |target|):")
for _, row in spike_days.iterrows():
    print(f"     Date {row['date_id']}: p95_abs={row['p95_abs']:.4f}, max_abs={row['max_abs']:.4f}")

# 4) Visualizations
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# Plot 1: Distribution of means
axes[0,0].hist(stats_df['mean'], bins=50, alpha=0.7, edgecolor='black')
axes[0,0].set_title('Distribution of Target Means')
axes[0,0].set_xlabel('Mean Return')
axes[0,0].set_ylabel('Count')
axes[0,0].axvline(0, color='red', linestyle='--', alpha=0.7)

# Plot 2: Distribution of volatilities
axes[0,1].hist(stats_df['std'], bins=50, alpha=0.7, edgecolor='black')
axes[0,1].set_title('Distribution of Target Volatilities')
axes[0,1].set_xlabel('Standard Deviation')
axes[0,1].set_ylabel('Count')

# Plot 3: Skewness vs Kurtosis
axes[0,2].scatter(stats_df['skew'], stats_df['kurtosis'], alpha=0.6)
axes[0,2].set_title('Skewness vs Kurtosis')
axes[0,2].set_xlabel('Skewness')
axes[0,2].set_ylabel('Kurtosis')
axes[0,2].grid(True, alpha=0.3)

# Plot 4: Zero percentage distribution
axes[1,0].hist(zero_df['zero_pct'], bins=30, alpha=0.7, edgecolor='black')
axes[1,0].set_title('Distribution of Zero Percentages')
axes[1,0].set_xlabel('Zero %')
axes[1,0].set_ylabel('Count')

# Plot 5: Unique value ratios
axes[1,1].hist(ties_df['tie_ratio'], bins=30, alpha=0.7, edgecolor='black')
axes[1,1].set_title('Distribution of Unique Value Ratios')
axes[1,1].set_xlabel('Unique Values / Total Values')
axes[1,1].set_ylabel('Count')

# Plot 6: Daily extreme values over time
axes[1,2].plot(extremes_df['date_id'], extremes_df['p95_abs'], alpha=0.7, label='95th percentile')
axes[1,2].plot(extremes_df['date_id'], extremes_df['median_abs'], alpha=0.7, label='Median')
axes[1,2].set_title('Daily Extreme Values Over Time')
axes[1,2].set_xlabel('Date ID')
axes[1,2].set_ylabel('|Target Value|')
axes[1,2].legend()
axes[1,2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n" + "="*80)
print("DISTRIBUTION & TAILS ANALYSIS COMPLETE")
print("="*80)

# Cell 20: Train Labels - Time-Series Behavior Analysis
from scipy.stats import pearsonr
from statsmodels.tsa.stattools import acf
from statsmodels.tsa.ar_model import AutoReg
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("TRAIN LABELS ANALYSIS - TIME-SERIES BEHAVIOR")
print("="*80)

# 1) Autocorrelation analysis
print("1) AUTOCORRELATION ANALYSIS:")

# Select a sample of targets for detailed analysis (avoid those with too much missing data)
sample_targets = coverage_df[coverage_df['coverage_pct'] > 90].head(20).index.tolist()
print(f"   • Analyzing {len(sample_targets)} well-covered targets")

autocorr_results = {}
for target in sample_targets:
    data = train_labels[target].dropna()
    if len(data) > 50:  # Need sufficient data for ACF
        try:
            # Calculate ACF for lags 1, 5, 21
            acf_values = acf(data, nlags=21, fft=True)
            autocorr_results[target] = {
                'lag_1': acf_values[1],
                'lag_5': acf_values[5],
                'lag_21': acf_values[21],
                'data_length': len(data)
            }
        except:
            continue

if autocorr_results:
    autocorr_df = pd.DataFrame(autocorr_results).T
    
    print(f"   • Autocorrelation statistics (lag 1):")
    print(f"     - Mean: {autocorr_df['lag_1'].mean():.3f}")
    print(f"     - Median: {autocorr_df['lag_1'].median():.3f}")
    print(f"     - Range: [{autocorr_df['lag_1'].min():.3f}, {autocorr_df['lag_1'].max():.3f}]")
    
    print(f"   • Autocorrelation statistics (lag 5):")
    print(f"     - Mean: {autocorr_df['lag_5'].mean():.3f}")
    print(f"     - Median: {autocorr_df['lag_5'].median():.3f}")
    
    print(f"   • Autocorrelation statistics (lag 21):")
    print(f"     - Mean: {autocorr_df['lag_21'].mean():.3f}")
    print(f"     - Median: {autocorr_df['lag_21'].median():.3f}")
    
    # Identify targets with strong persistence
    strong_persistence = autocorr_df[autocorr_df['lag_1'] > 0.1]
    print(f"\n   • Targets with strong persistence (lag-1 > 0.1): {len(strong_persistence)}")
    if len(strong_persistence) > 0:
        for target, row in strong_persistence.head().iterrows():
            print(f"     {target}: lag-1={row['lag_1']:.3f}")

# 2) Rolling statistics analysis
print(f"\n2) ROLLING STATISTICS ANALYSIS:")

# Calculate 60-day rolling stats for a few representative targets
rolling_window = 60
rolling_stats = {}

for target in sample_targets[:5]:  # Analyze top 5 for detailed rolling stats
    data = train_labels[target].dropna()
    if len(data) > rolling_window * 2:
        rolling_mean = data.rolling(window=rolling_window).mean()
        rolling_std = data.rolling(window=rolling_window).std()
        
        # Calculate stability metrics
        mean_stability = rolling_mean.std() / data.std() if data.std() > 0 else 0
        vol_stability = rolling_std.std() / rolling_std.mean() if rolling_std.mean() > 0 else 0
        
        rolling_stats[target] = {
            'mean_stability': mean_stability,
            'vol_stability': vol_stability,
            'rolling_mean_range': rolling_mean.max() - rolling_mean.min(),
            'rolling_std_range': rolling_std.max() - rolling_std.min()
        }

if rolling_stats:
    rolling_df = pd.DataFrame(rolling_stats).T
    print(f"   • Rolling mean stability (lower = more stable):")
    print(f"     - Mean: {rolling_df['mean_stability'].mean():.3f}")
    print(f"     - Most stable: {rolling_df['mean_stability'].idxmin()} ({rolling_df['mean_stability'].min():.3f})")
    print(f"     - Least stable: {rolling_df['mean_stability'].idxmax()} ({rolling_df['mean_stability'].max():.3f})")
    
    print(f"   • Rolling volatility stability:")
    print(f"     - Mean: {rolling_df['vol_stability'].mean():.3f}")

# 3) Half-life estimation using AR(1)
print(f"\n3) HALF-LIFE ESTIMATION (AR(1) MODEL):")

half_lives = {}
for target in sample_targets[:10]:  # Analyze subset for half-life
    data = train_labels[target].dropna()
    if len(data) > 100:
        try:
            # Fit AR(1) model
            model = AutoReg(data, lags=1, trend='c')
            fitted = model.fit()
            ar_coef = fitted.params[1]  # AR(1) coefficient
            
            # Calculate half-life: log(0.5) / log(|ar_coef|) if |ar_coef| < 1
            if abs(ar_coef) < 1 and ar_coef > 0:
                half_life = np.log(0.5) / np.log(ar_coef)
                half_lives[target] = {
                    'ar_coef': ar_coef,
                    'half_life': half_life,
                    'is_stationary': abs(ar_coef) < 1
                }
        except:
            continue

if half_lives:
    hl_df = pd.DataFrame(half_lives).T
    valid_hl = hl_df[hl_df['half_life'] > 0]
    
    print(f"   • Half-life statistics (days):")
    print(f"     - Mean: {valid_hl['half_life'].mean():.1f}")
    print(f"     - Median: {valid_hl['half_life'].median():.1f}")
    print(f"     - Range: [{valid_hl['half_life'].min():.1f}, {valid_hl['half_life'].max():.1f}]")
    
    print(f"   • AR(1) coefficient statistics:")
    print(f"     - Mean: {hl_df['ar_coef'].mean():.3f}")
    print(f"     - Stationary series: {hl_df['is_stationary'].sum()}/{len(hl_df)}")

# 4) Identify the extreme spike period
print(f"\n4) EXTREME SPIKE PERIOD ANALYSIS:")
spike_start, spike_end = 560, 580
spike_data = train_labels[(train_labels['date_id'] >= spike_start) & 
                         (train_labels['date_id'] <= spike_end)]

print(f"   • Analyzing spike period: dates {spike_start}-{spike_end}")
print(f"   • Days in spike period: {len(spike_data)}")

# Calculate impact on different targets
spike_impact = {}
for target in sample_targets:
    normal_data = train_labels[(train_labels['date_id'] < spike_start) | 
                              (train_labels['date_id'] > spike_end)][target].dropna()
    spike_target_data = spike_data[target].dropna()
    
    if len(normal_data) > 0 and len(spike_target_data) > 0:
        normal_std = normal_data.std()
        spike_max_abs = spike_target_data.abs().max()
        impact_ratio = spike_max_abs / normal_std if normal_std > 0 else 0
        
        spike_impact[target] = {
            'normal_std': normal_std,
            'spike_max_abs': spike_max_abs,
            'impact_ratio': impact_ratio
        }

if spike_impact:
    impact_df = pd.DataFrame(spike_impact).T
    most_impacted = impact_df.nlargest(5, 'impact_ratio')
    
    print(f"   • Most impacted targets during spike:")
    for target, row in most_impacted.iterrows():
        print(f"     {target}: {row['impact_ratio']:.1f}x normal volatility")

# 5) Visualizations
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# Plot 1: Autocorrelation distribution
if 'autocorr_df' in locals():
    axes[0,0].hist(autocorr_df['lag_1'], bins=20, alpha=0.7, edgecolor='black')
    axes[0,0].set_title('Distribution of Lag-1 Autocorrelations')
    axes[0,0].set_xlabel('Autocorrelation')
    axes[0,0].set_ylabel('Count')
    axes[0,0].axvline(0, color='red', linestyle='--', alpha=0.7)

# Plot 2: Half-life distribution
if 'valid_hl' in locals() and len(valid_hl) > 0:
    axes[0,1].hist(valid_hl['half_life'], bins=15, alpha=0.7, edgecolor='black')
    axes[0,1].set_title('Distribution of Half-Lives')
    axes[0,1].set_xlabel('Half-Life (days)')
    axes[0,1].set_ylabel('Count')

# Plot 3: Sample target time series
sample_target = sample_targets[0]
sample_data = train_labels[sample_target].dropna()
axes[0,2].plot(sample_data.index, sample_data.values, alpha=0.7)
axes[0,2].set_title(f'Sample Time Series: {sample_target}')
axes[0,2].set_xlabel('Index')
axes[0,2].set_ylabel('Value')
axes[0,2].grid(True, alpha=0.3)

# Plot 4: Rolling volatility for sample target
if len(sample_data) > rolling_window:
    rolling_vol = sample_data.rolling(window=rolling_window).std()
    axes[1,0].plot(rolling_vol.index, rolling_vol.values, alpha=0.7)
    axes[1,0].set_title(f'Rolling {rolling_window}-day Volatility: {sample_target}')
    axes[1,0].set_xlabel('Index')
    axes[1,0].set_ylabel('Rolling Std')
    axes[1,0].grid(True, alpha=0.3)

# Plot 5: Spike impact visualization
if 'impact_df' in locals():
    axes[1,1].scatter(impact_df['normal_std'], impact_df['spike_max_abs'], alpha=0.6)
    axes[1,1].set_title('Normal Volatility vs Spike Impact')
    axes[1,1].set_xlabel('Normal Std Dev')
    axes[1,1].set_ylabel('Max Spike |Value|')
    axes[1,1].grid(True, alpha=0.3)

# Plot 6: Daily extreme values with spike period highlighted
axes[1,2].plot(extremes_df['date_id'], extremes_df['p95_abs'], alpha=0.7)
axes[1,2].axvspan(spike_start, spike_end, alpha=0.3, color='red', label='Spike Period')
axes[1,2].set_title('Daily 95th Percentile |Values| with Spike Period')
axes[1,2].set_xlabel('Date ID')
axes[1,2].set_ylabel('95th Percentile |Value|')
axes[1,2].legend()
axes[1,2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n" + "="*80)
print("TIME-SERIES BEHAVIOR ANALYSIS COMPLETE")
print("="*80)

# Cell 21: Train Labels - Cross-Sectional Structure Analysis
print("="*80)
print("TRAIN LABELS ANALYSIS - CROSS-SECTIONAL STRUCTURE")
print("="*80)

# 1) Cross-sectional statistics per date
print("1) CROSS-SECTIONAL STATISTICS PER DATE:")

daily_cross_sectional = []
for date_id in sorted(train_labels['date_id'].unique()):
    date_data = train_labels[train_labels['date_id'] == date_id][target_cols]
    available_data = date_data.dropna(axis=1).iloc[0]  # Get the row, drop NaN columns
    
    if len(available_data) > 1:
        cs_mean = available_data.mean()
        cs_std = available_data.std()
        cs_skew = available_data.skew()
        cs_unique_ranks = len(available_data.rank().unique())
        
        daily_cross_sectional.append({
            'date_id': date_id,
            'n_targets': len(available_data),
            'cs_mean': cs_mean,
            'cs_std': cs_std,
            'cs_skew': cs_skew,
            'unique_ranks': cs_unique_ranks,
            'rank_resolution': cs_unique_ranks / len(available_data)
        })

cs_df = pd.DataFrame(daily_cross_sectional)

print(f"   • Cross-sectional mean statistics:")
print(f"     - Mean of daily means: {cs_df['cs_mean'].mean():.6f}")
print(f"     - Std of daily means: {cs_df['cs_mean'].std():.6f}")

print(f"   • Cross-sectional dispersion statistics:")
print(f"     - Mean daily std: {cs_df['cs_std'].mean():.4f}")
print(f"     - Std of daily stds: {cs_df['cs_std'].std():.4f}")
print(f"     - Min daily std: {cs_df['cs_std'].min():.4f}")
print(f"     - Max daily std: {cs_df['cs_std'].max():.4f}")

print(f"   • Rank resolution statistics:")
print(f"     - Mean rank resolution: {cs_df['rank_resolution'].mean():.3f}")
print(f"     - Min rank resolution: {cs_df['rank_resolution'].min():.3f}")

# Identify thin dispersion days (harder for ranking)
thin_dispersion_threshold = cs_df['cs_std'].quantile(0.1)
thin_days = cs_df[cs_df['cs_std'] < thin_dispersion_threshold]
print(f"\n   • Thin dispersion days (bottom 10% std): {len(thin_days)}")
print(f"     - Threshold: {thin_dispersion_threshold:.4f}")
if len(thin_days) > 0:
    print(f"     - Examples:")
    for _, row in thin_days.head().iterrows():
        print(f"       Date {row['date_id']}: std={row['cs_std']:.4f}, {row['n_targets']} targets")

# 2) Rank persistence analysis
print(f"\n2) RANK PERSISTENCE ANALYSIS:")

rank_persistence = []
for i in range(1, len(train_labels)):
    current_date = train_labels.iloc[i]['date_id']
    prev_date = train_labels.iloc[i-1]['date_id']
    
    current_data = train_labels.iloc[i][target_cols].dropna()
    prev_data = train_labels.iloc[i-1][target_cols].dropna()
    
    # Find overlapping targets
    overlap_targets = current_data.index.intersection(prev_data.index)
    
    if len(overlap_targets) > 2:
        current_ranks = current_data[overlap_targets].rank()
        prev_ranks = prev_data[overlap_targets].rank()
        
        # Calculate Spearman correlation between ranks
        rank_corr = current_ranks.corr(prev_ranks, method='spearman')
        
        rank_persistence.append({
            'date_id': current_date,
            'prev_date': prev_date,
            'overlap_size': len(overlap_targets),
            'rank_correlation': rank_corr
        })

persistence_df = pd.DataFrame(rank_persistence)

print(f"   • Rank persistence statistics:")
print(f"     - Mean rank correlation: {persistence_df['rank_correlation'].mean():.3f}")
print(f"     - Median rank correlation: {persistence_df['rank_correlation'].median():.3f}")
print(f"     - Std rank correlation: {persistence_df['rank_correlation'].std():.3f}")
print(f"     - Min rank correlation: {persistence_df['rank_correlation'].min():.3f}")
print(f"     - Max rank correlation: {persistence_df['rank_correlation'].max():.3f}")

# Identify periods with high/low rank persistence
high_persistence = persistence_df[persistence_df['rank_correlation'] > 0.5]
low_persistence = persistence_df[persistence_df['rank_correlation'] < -0.2]

print(f"\n   • High rank persistence days (corr > 0.5): {len(high_persistence)}")
print(f"   • Low rank persistence days (corr < -0.2): {len(low_persistence)}")

if len(low_persistence) > 0:
    print(f"     - Examples of rank reversals:")
    for _, row in low_persistence.head().iterrows():
        print(f"       Date {row['date_id']}: corr={row['rank_correlation']:.3f}")

# 3) Dispersion regime analysis
print(f"\n3) DISPERSION REGIME ANALYSIS:")

# Define dispersion regimes
cs_df['dispersion_regime'] = pd.cut(cs_df['cs_std'], 
                                   bins=3, 
                                   labels=['Low', 'Medium', 'High'])

regime_stats = cs_df.groupby('dispersion_regime').agg({
    'cs_std': ['mean', 'count'],
    'rank_resolution': 'mean',
    'n_targets': 'mean'
}).round(4)

print(f"   • Dispersion regime statistics:")
print(regime_stats)

# 4) Spike period cross-sectional analysis
spike_cs = cs_df[(cs_df['date_id'] >= 560) & (cs_df['date_id'] <= 580)]
normal_cs = cs_df[(cs_df['date_id'] < 560) | (cs_df['date_id'] > 580)]

print(f"\n4) SPIKE PERIOD CROSS-SECTIONAL IMPACT:")
print(f"   • Normal period dispersion: {normal_cs['cs_std'].mean():.4f} ± {normal_cs['cs_std'].std():.4f}")
print(f"   • Spike period dispersion: {spike_cs['cs_std'].mean():.4f} ± {spike_cs['cs_std'].std():.4f}")
print(f"   • Dispersion increase: {spike_cs['cs_std'].mean() / normal_cs['cs_std'].mean():.1f}x")

# 5) Visualizations
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# Plot 1: Cross-sectional dispersion over time
axes[0,0].plot(cs_df['date_id'], cs_df['cs_std'], alpha=0.7)
axes[0,0].axhspan(0, thin_dispersion_threshold, alpha=0.3, color='red', label='Thin Dispersion')
axes[0,0].set_title('Cross-Sectional Dispersion Over Time')
axes[0,0].set_xlabel('Date ID')
axes[0,0].set_ylabel('Cross-Sectional Std')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# Plot 2: Rank persistence over time
axes[0,1].plot(persistence_df['date_id'], persistence_df['rank_correlation'], alpha=0.7)
axes[0,1].axhline(0, color='red', linestyle='--', alpha=0.7)
axes[0,1].set_title('Rank Persistence Over Time')
axes[0,1].set_xlabel('Date ID')
axes[0,1].set_ylabel('Rank Correlation (t vs t-1)')
axes[0,1].grid(True, alpha=0.3)

# Plot 3: Distribution of rank correlations
axes[0,2].hist(persistence_df['rank_correlation'], bins=30, alpha=0.7, edgecolor='black')
axes[0,2].set_title('Distribution of Rank Correlations')
axes[0,2].set_xlabel('Rank Correlation')
axes[0,2].set_ylabel('Count')
axes[0,2].axvline(0, color='red', linestyle='--', alpha=0.7)

# Plot 4: Cross-sectional mean vs std
axes[1,0].scatter(cs_df['cs_mean'], cs_df['cs_std'], alpha=0.6)
axes[1,0].set_title('Cross-Sectional Mean vs Std')
axes[1,0].set_xlabel('Cross-Sectional Mean')
axes[1,0].set_ylabel('Cross-Sectional Std')
axes[1,0].grid(True, alpha=0.3)

# Plot 5: Number of targets vs dispersion
axes[1,1].scatter(cs_df['n_targets'], cs_df['cs_std'], alpha=0.6)
axes[1,1].set_title('Number of Targets vs Dispersion')
axes[1,1].set_xlabel('Number of Available Targets')
axes[1,1].set_ylabel('Cross-Sectional Std')
axes[1,1].grid(True, alpha=0.3)

# Plot 6: Rank resolution over time
axes[1,2].plot(cs_df['date_id'], cs_df['rank_resolution'], alpha=0.7)
axes[1,2].set_title('Rank Resolution Over Time')
axes[1,2].set_xlabel('Date ID')
axes[1,2].set_ylabel('Unique Ranks / Total Targets')
axes[1,2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n" + "="*80)
print("CROSS-SECTIONAL STRUCTURE ANALYSIS COMPLETE")
print("="*80)

# Cell 22: Train Labels - Comprehensive Summary & Modeling Strategy
print("="*80)
print("TRAIN LABELS - COMPREHENSIVE SUMMARY & MODELING STRATEGY")
print("="*80)

print("EXECUTIVE SUMMARY:")
print("="*50)
print("✓ 424 targets, 1917 days, 10.6% missing data")
print("✓ No constant columns, excellent rank resolution")
print("✓ Major market event: dates 562-575 (3.7x dispersion increase)")
print("✓ Moderate rank persistence (mean=0.452), suitable for momentum strategies")
print("✓ 192 'thin dispersion' days will be harder to predict")

print(f"\nCRITICAL FINDINGS FOR COMPETITION:")
print("="*50)

print("1. DATA QUALITY ASSESSMENT:")
print("   🟢 EXCELLENT: No ties/discretization issues")
print("   🟢 EXCELLENT: Perfect rank resolution (mean=1.000)")
print("   🟢 GOOD: Reasonable missing data (10.6%)")
print("   🟡 MODERATE: Some targets have 17.9% missing")
print("   🔴 CONCERN: Extreme spike period (dates 562-575)")

print(f"\n2. TEMPORAL PATTERNS:")
print("   • Weak autocorrelation (mean lag-1: 0.040)")
print("   • Short half-lives (mean: 1.3 days)")
print("   • Two persistent targets: target_212, target_318")
print("   • 18 days with rank reversals (corr < -0.2)")

print(f"\n3. CROSS-SECTIONAL DYNAMICS:")
print("   • Mean rank persistence: 0.452 (moderate momentum)")
print("   • 870/1916 days have strong persistence (>0.5)")
print("   • 192 'thin dispersion' days (std < 0.019)")
print("   • Spike period: 3.7x normal dispersion")

print(f"\nMODELING STRATEGY RECOMMENDATIONS:")
print("="*50)

print("1. FEATURE ENGINEERING PRIORITIES:")
print("   🎯 Cross-sectional ranks from previous day")
print("   🎯 Rolling volatility features (detect regime changes)")
print("   🎯 Dispersion regime indicators")
print("   🎯 Spike detection features")
print("   🎯 Target availability masks")

print(f"\n2. MODEL ARCHITECTURE:")
print("   📊 ENSEMBLE APPROACH:")
print("     • Momentum model (for high persistence days)")
print("     • Mean reversion model (for reversal days)")
print("     • Regime-aware model (normal vs spike periods)")
print("     • Meta-model to blend based on market conditions")

print(f"\n3. TRAINING STRATEGY:")
print("   📈 TIME-BASED SPLITS:")
print("     • Avoid data leakage with proper time splits")
print("     • Special handling for spike period (dates 562-575)")
print("     • Separate validation for thin dispersion days")

print(f"\n4. LOSS FUNCTION CONSIDERATIONS:")
print("   🎯 SPEARMAN-OPTIMIZED:")
print("     • Rank-based loss functions")
print("     • Robust to outliers (important for spike period)")
print("     • Weight by cross-sectional dispersion")

print(f"\n5. ONLINE LEARNING STRATEGY:")
print("   ⚡ ADAPTIVE UPDATES:")
print("     • Short half-lives suggest frequent updates needed")
print("     • Regime detection for model switching")
print("     • Volatility-based learning rates")

print(f"\nHIGH-VALUE TARGETS FOR ANALYSIS:")
print("="*50)

# Identify target categories based on our analysis
high_persistence = ['target_212', 'target_318']
high_volatility = ['target_95', 'target_165', 'target_85']  # From kurtosis analysis
spike_sensitive = ['target_318', 'target_36', 'target_26', 'target_212', 'target_69']
high_coverage = ['target_212', 'target_318', 'target_74', 'target_20', 'target_84']

print("📊 HIGH PERSISTENCE (momentum candidates):")
for target in high_persistence:
    print(f"   • {target}")

print(f"\n📊 HIGH VOLATILITY (regime indicators):")
for target in high_volatility[:3]:
    print(f"   • {target}")

print(f"\n📊 SPIKE SENSITIVE (risk indicators):")
for target in spike_sensitive[:3]:
    print(f"   • {target}")

print(f"\nRISK MANAGEMENT:")
print("="*50)
print("🚨 SPIKE PERIOD HANDLING:")
print("   • Dates 562-575: Extreme volatility period")
print("   • Consider separate model or robust preprocessing")
print("   • May represent market crisis (COVID-19 era?)")

print(f"\n🚨 THIN DISPERSION DAYS:")
print("   • 192 days with std < 0.019")
print("   • Lower achievable Spearman scores")
print("   • Consider conservative predictions")

print(f"\n🚨 MISSING DATA STRATEGY:")
print("   • Forward fill for short gaps")
print("   • Separate imputation model for longer gaps")
print("   • Availability-aware ensemble weights")

print(f"\nNEXT STEPS:")
print("="*50)
print("1. 📋 Load and analyze target_pairs.csv for target taxonomy")
print("2. 🔗 Join labels with features for correlation analysis")
print("3. 🏗️  Build baseline momentum/mean-reversion models")
print("4. 📊 Implement regime detection system")
print("5. 🎯 Develop Spearman-optimized loss functions")
print("6. ✅ Create time-based CV framework")

print(f"\n" + "="*80)
print("TRAIN LABELS ANALYSIS COMPLETE - READY FOR MODELING!")
print("="*80)

# Advanced EDA for Commodity Prices Competition
# Based on insights from data_insights.md and target_calculation.py analysis
# This script contains all advanced exploratory analysis needed before modeling

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import stats
from scipy.stats import pearsonr, spearmanr
from statsmodels.tsa.stattools import acf
from statsmodels.tsa.ar_model import AutoReg
from statsmodels.stats.diagnostic import breaks_cusum
import warnings
warnings.filterwarnings('ignore')

def get_data_path():
    """Auto-detect if running on Kaggle, Colab, or local"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")

    if kaggle_path.exists():
        return kaggle_path
    elif colab_path.exists():
        return colab_path
    else:
        return local_path

# =============================================================================
# CELL 1: Load All Data and Target Pairs Analysis
# =============================================================================
def cell_1_load_and_target_pairs():
    """Load all datasets and analyze target_pairs.csv for complete taxonomy"""
    print("="*80)
    print("ADVANCED EDA - CELL 1: TARGET PAIRS TAXONOMY ANALYSIS")
    print("="*80)

    data_path = get_data_path()
    print(f"Using data path: {data_path}")

    # Load all datasets
    print("\n1) LOADING ALL DATASETS:")
    train = pd.read_csv(data_path / "train.csv")
    train_labels = pd.read_csv(data_path / "train_labels.csv")
    target_pairs = pd.read_csv(data_path / "target_pairs.csv")

    print(f"   • train.csv: {train.shape}")
    print(f"   • train_labels.csv: {train_labels.shape}")
    print(f"   • target_pairs.csv: {target_pairs.shape}")

    # Analyze target_pairs structure
    print(f"\n2) TARGET PAIRS STRUCTURE:")
    print(f"   • Columns: {list(target_pairs.columns)}")
    print(f"   • Unique lags: {sorted(target_pairs['lag'].unique())}")
    print(f"   • Lag distribution:")
    lag_counts = target_pairs['lag'].value_counts().sort_index()
    for lag, count in lag_counts.items():
        print(f"     - Lag {lag}: {count} targets")

    # Parse pair structure
    print(f"\n3) PAIR STRUCTURE ANALYSIS:")
    target_pairs['pair_split'] = target_pairs['pair'].str.split(' - ')
    target_pairs['asset_a'] = target_pairs['pair_split'].str[0]
    target_pairs['asset_b'] = target_pairs['pair_split'].str[1]

    # Asset class mapping
    def get_asset_class(asset_name):
        if asset_name.startswith('LME_'):
            return 'LME'
        elif asset_name.startswith('JPX_'):
            return 'JPX'
        elif asset_name.startswith('US_Stock_'):
            return 'US_Stock'
        elif asset_name.startswith('FX_'):
            return 'FX'
        else:
            return 'Unknown'

    target_pairs['class_a'] = target_pairs['asset_a'].apply(get_asset_class)
    target_pairs['class_b'] = target_pairs['asset_b'].apply(get_asset_class)
    target_pairs['pair_type'] = target_pairs['class_a'] + '_vs_' + target_pairs['class_b']

    print(f"   • Pair type distribution:")
    pair_type_counts = target_pairs['pair_type'].value_counts()
    for pair_type, count in pair_type_counts.items():
        print(f"     - {pair_type}: {count} targets")

    # Check for antisymmetric pairs (A-B vs B-A)
    print(f"\n4) ANTISYMMETRY ANALYSIS:")
    antisymmetric_pairs = []
    for idx, row in target_pairs.iterrows():
        reverse_pair = f"{row['asset_b']} - {row['asset_a']}"
        reverse_match = target_pairs[target_pairs['pair'] == reverse_pair]
        if len(reverse_match) > 0:
            antisymmetric_pairs.append((row['target'], reverse_match.iloc[0]['target']))

    print(f"   • Found {len(antisymmetric_pairs)} antisymmetric pairs")
    if len(antisymmetric_pairs) > 0:
        print(f"   • Examples:")
        for i, (target_a, target_b) in enumerate(antisymmetric_pairs[:5]):
            print(f"     - {target_a} ↔ {target_b}")

    # Save target taxonomy for later use
    target_taxonomy = target_pairs[['target', 'lag', 'pair_type', 'class_a', 'class_b']].copy()

    return train, train_labels, target_pairs, target_taxonomy

# =============================================================================
# CELL 2: Alignment Audit - Prevent Look-Ahead Bias
# =============================================================================
def cell_2_alignment_audit(train, train_labels, target_pairs):
    """Verify proper alignment between features and targets to prevent look-ahead bias"""
    print("="*80)
    print("ADVANCED EDA - CELL 2: ALIGNMENT AUDIT (LOOK-AHEAD BIAS PREVENTION)")
    print("="*80)

    print("1) TARGET CALCULATION VERIFICATION:")
    print("   Formula: target = log(A[t+lag+1]/A[t+1]) - log(B[t+lag+1]/B[t+1])")
    print("   This means at time t, target uses prices from t+1 to t+lag+1")
    print("   Features at time t must NOT include any info from t+1 onwards")

    # Check date alignment
    print(f"\n2) DATE ALIGNMENT CHECK:")
    train_dates = sorted(train['date_id'].unique())
    label_dates = sorted(train_labels['date_id'].unique())

    print(f"   • Train date range: {min(train_dates)} to {max(train_dates)} ({len(train_dates)} days)")
    print(f"   • Label date range: {min(label_dates)} to {max(label_dates)} ({len(label_dates)} days)")

    # Check for proper lag structure
    max_lag = target_pairs['lag'].max()
    print(f"   • Maximum lag: {max_lag} days")
    print(f"   • Last trainable date: {max(train_dates) - max_lag}")
    print(f"   • Last label date: {max(label_dates)}")

    # Verify no future leakage
    future_leak_risk = max(train_dates) - max_lag <= max(label_dates)
    print(f"   • Future leakage risk: {'⚠️  YES' if future_leak_risk else '✅ NO'}")

    if future_leak_risk:
        print("   ⚠️  WARNING: Potential look-ahead bias detected!")
        print("   Features may contain information from target calculation period")

    # Sample verification with actual target calculation
    print(f"\n3) SAMPLE TARGET CALCULATION VERIFICATION:")
    sample_target = target_pairs.iloc[0]
    print(f"   • Sample target: {sample_target['target']}")
    print(f"   • Pair: {sample_target['pair']}")
    print(f"   • Lag: {sample_target['lag']}")

    # Extract asset names
    assets = sample_target['pair'].split(' - ')
    asset_a, asset_b = assets[0], assets[1]

    # Check if assets exist in train data
    asset_a_cols = [col for col in train.columns if col.startswith(asset_a)]
    asset_b_cols = [col for col in train.columns if col.startswith(asset_b)]

    print(f"   • Asset A ({asset_a}) columns: {len(asset_a_cols)}")
    print(f"   • Asset B ({asset_b}) columns: {len(asset_b_cols)}")

    if len(asset_a_cols) > 0 and len(asset_b_cols) > 0:
        print(f"   • Assets found in training data ✅")
    else:
        print(f"   • Assets NOT found in training data ⚠️")

    return future_leak_risk

# =============================================================================
# CELL 3: Coverage & Integrity Dashboard
# =============================================================================
def cell_3_coverage_integrity_dashboard(train_labels, target_taxonomy):
    """Create comprehensive coverage and integrity dashboard per target"""
    print("="*80)
    print("ADVANCED EDA - CELL 3: COVERAGE & INTEGRITY DASHBOARD")
    print("="*80)

    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    print("1) PER-TARGET COVERAGE ANALYSIS:")
    coverage_stats = {}

    for target in target_cols:
        data = train_labels[target]
        non_missing = data.notna()

        if non_missing.sum() > 0:
            start_date = train_labels.loc[non_missing, 'date_id'].min()
            end_date = train_labels.loc[non_missing, 'date_id'].max()
            coverage_pct = (non_missing.sum() / len(train_labels)) * 100

            # Find gaps
            missing_mask = data.isna()
            gap_starts = []
            gap_lengths = []
            in_gap = False
            gap_start = None

            for i, is_missing in enumerate(missing_mask):
                if is_missing and not in_gap:
                    gap_start = i
                    in_gap = True
                elif not is_missing and in_gap:
                    gap_lengths.append(i - gap_start)
                    gap_starts.append(gap_start)
                    in_gap = False

            if in_gap:  # Gap at end
                gap_lengths.append(len(missing_mask) - gap_start)
                gap_starts.append(gap_start)

            max_gap = max(gap_lengths) if gap_lengths else 0
            n_gaps = len(gap_lengths)

            coverage_stats[target] = {
                'start_date': start_date,
                'end_date': end_date,
                'coverage_pct': coverage_pct,
                'max_gap': max_gap,
                'n_gaps': n_gaps,
                'observations': non_missing.sum()
            }

    coverage_df = pd.DataFrame(coverage_stats).T

    # Merge with taxonomy
    coverage_df = coverage_df.merge(target_taxonomy, left_index=True, right_on='target', how='left')

    print(f"   • Coverage statistics by pair type:")
    coverage_by_type = coverage_df.groupby('pair_type')['coverage_pct'].agg(['mean', 'min', 'max', 'count'])
    print(coverage_by_type.round(1))

    print(f"\n   • Worst coverage targets:")
    worst_coverage = coverage_df.nsmallest(10, 'coverage_pct')
    for _, row in worst_coverage.iterrows():
        print(f"     {row['target']}: {row['coverage_pct']:.1f}% ({row['pair_type']})")

    print(f"\n2) GAP ANALYSIS:")
    print(f"   • Targets with gaps >10 days: {(coverage_df['max_gap'] > 10).sum()}")
    print(f"   • Targets with >5 gaps: {(coverage_df['n_gaps'] > 5).sum()}")

    # Visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Coverage distribution by pair type
    coverage_df.boxplot(column='coverage_pct', by='pair_type', ax=axes[0,0])
    axes[0,0].set_title('Coverage % by Pair Type')
    axes[0,0].set_xlabel('Pair Type')

    # Max gap distribution
    axes[0,1].hist(coverage_df['max_gap'], bins=30, alpha=0.7, edgecolor='black')
    axes[0,1].set_title('Distribution of Maximum Gaps')
    axes[0,1].set_xlabel('Max Gap (days)')

    # Coverage vs lag
    coverage_df.plot.scatter(x='lag', y='coverage_pct', ax=axes[1,0], alpha=0.6)
    axes[1,0].set_title('Coverage % vs Lag')
    axes[1,0].set_xlabel('Lag (days)')
    axes[1,0].set_ylabel('Coverage %')

    # Number of gaps vs coverage
    coverage_df.plot.scatter(x='coverage_pct', y='n_gaps', ax=axes[1,1], alpha=0.6)
    axes[1,1].set_title('Coverage % vs Number of Gaps')
    axes[1,1].set_xlabel('Coverage %')
    axes[1,1].set_ylabel('Number of Gaps')

    plt.tight_layout()
    plt.show()

    return coverage_df

# =============================================================================
# CELL 4: Small-N Day Anatomy
# =============================================================================
def cell_4_small_n_day_anatomy(train_labels, target_taxonomy):
    """Analyze days with very few available targets"""
    print("="*80)
    print("ADVANCED EDA - CELL 4: SMALL-N DAY ANATOMY")
    print("="*80)

    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    # Calculate daily target availability
    daily_availability = []
    for date_id in sorted(train_labels['date_id'].unique()):
        date_data = train_labels[train_labels['date_id'] == date_id]
        available_targets = date_data[target_cols].notna().sum(axis=1).iloc[0]
        available_target_names = date_data[target_cols].dropna(axis=1).columns.tolist()

        daily_availability.append({
            'date_id': date_id,
            'n_targets': available_targets,
            'available_targets': available_target_names
        })

    availability_df = pd.DataFrame(daily_availability)

    # Identify worst days
    worst_days = availability_df.nsmallest(10, 'n_targets')

    print("1) WORST AVAILABILITY DAYS ANALYSIS:")
    for _, row in worst_days.iterrows():
        date_id = row['date_id']
        n_targets = row['n_targets']
        available_targets = row['available_targets']

        print(f"\n   📅 DATE {date_id}: {n_targets} targets available")

        # Analyze by pair type
        target_analysis = []
        for target in available_targets:
            target_info = target_taxonomy[target_taxonomy['target'] == target]
            if len(target_info) > 0:
                pair_type = target_info.iloc[0]['pair_type']
                lag = target_info.iloc[0]['lag']
                target_analysis.append({'target': target, 'pair_type': pair_type, 'lag': lag})

        target_analysis_df = pd.DataFrame(target_analysis)
        if len(target_analysis_df) > 0:
            pair_type_counts = target_analysis_df['pair_type'].value_counts()
            lag_counts = target_analysis_df['lag'].value_counts()

            print(f"     • Pair types: {dict(pair_type_counts)}")
            print(f"     • Lags: {dict(lag_counts)}")

        # Calculate cross-sectional statistics for this day
        date_data = train_labels[train_labels['date_id'] == date_id][available_targets]
        if len(date_data.columns) > 1:
            cs_data = date_data.iloc[0]
            cs_mean = cs_data.mean()
            cs_std = cs_data.std()
            cs_skew = cs_data.skew()

            print(f"     • Cross-sectional stats: mean={cs_mean:.6f}, std={cs_std:.4f}, skew={cs_skew:.3f}")

        # Check rank persistence if previous day available
        if date_id > 0:
            prev_date_data = train_labels[train_labels['date_id'] == date_id - 1]
            if len(prev_date_data) > 0:
                prev_available = prev_date_data[target_cols].dropna(axis=1).columns.tolist()
                overlap_targets = list(set(available_targets) & set(prev_available))

                if len(overlap_targets) > 2:
                    current_ranks = date_data[overlap_targets].iloc[0].rank()
                    prev_ranks = prev_date_data[overlap_targets].iloc[0].rank()
                    rank_corr = current_ranks.corr(prev_ranks, method='spearman')
                    print(f"     • Rank persistence: {rank_corr:.3f} ({len(overlap_targets)} overlapping targets)")

    return availability_df

# =============================================================================
# CELL 5: FX Triangle Residuals Analysis
# =============================================================================
def cell_5_fx_triangle_residuals(train):
    """Analyze FX triangle arbitrage residuals for quality checks and features"""
    print("="*80)
    print("ADVANCED EDA - CELL 5: FX TRIANGLE RESIDUALS ANALYSIS")
    print("="*80)

    # Get FX columns
    fx_cols = [col for col in train.columns if col.startswith('FX_')]
    fx_pairs = list(set([col.split('_')[1] + '_' + col.split('_')[2] for col in fx_cols]))

    print(f"1) FX MARKET STRUCTURE:")
    print(f"   • Total FX columns: {len(fx_cols)}")
    print(f"   • Unique FX pairs: {len(fx_pairs)}")
    print(f"   • Sample pairs: {fx_pairs[:10]}")

    # Focus on close prices for triangle analysis
    fx_close_cols = [col for col in fx_cols if col.endswith('_close')]

    print(f"\n2) FX CLOSE PRICE ANALYSIS:")
    print(f"   • FX close columns: {len(fx_close_cols)}")

    # Extract currency pairs
    fx_close_pairs = {}
    for col in fx_close_cols:
        parts = col.split('_')
        if len(parts) >= 4:
            pair = parts[1] + parts[2]  # e.g., EURUSD
            fx_close_pairs[pair] = col

    print(f"   • Extracted pairs: {list(fx_close_pairs.keys())[:10]}")

    # Look for triangle opportunities (EUR/USD, USD/JPY, EUR/JPY)
    triangles_found = []
    common_triangles = [
        ('EURUSD', 'USDJPY', 'EURJPY'),
        ('GBPUSD', 'USDJPY', 'GBPJPY'),
        ('AUDUSD', 'USDJPY', 'AUDJPY'),
        ('USDCAD', 'CADJPY', 'USDJPY'),
    ]

    print(f"\n3) TRIANGLE ARBITRAGE ANALYSIS:")
    triangle_residuals = {}

    for base_quote, quote_cross, base_cross in common_triangles:
        if base_quote in fx_close_pairs and quote_cross in fx_close_pairs and base_cross in fx_close_pairs:
            print(f"   • Found triangle: {base_quote} × {quote_cross} = {base_cross}")

            # Get the data
            rate_1 = train[fx_close_pairs[base_quote]]
            rate_2 = train[fx_close_pairs[quote_cross]]
            rate_3 = train[fx_close_pairs[base_cross]]

            # Calculate synthetic rate and residual
            synthetic_rate = rate_1 * rate_2
            residual = np.log(rate_3) - np.log(synthetic_rate)

            # Store residual
            triangle_name = f"{base_quote}x{quote_cross}_{base_cross}"
            triangle_residuals[triangle_name] = residual

            # Statistics
            residual_clean = residual.dropna()
            if len(residual_clean) > 0:
                print(f"     - Residual mean: {residual_clean.mean():.6f}")
                print(f"     - Residual std: {residual_clean.std():.6f}")
                print(f"     - Max |residual|: {residual_clean.abs().max():.6f}")

                # Check for large residuals (potential data errors)
                large_residuals = residual_clean[residual_clean.abs() > 0.01]
                if len(large_residuals) > 0:
                    print(f"     - Large residuals (>1%): {len(large_residuals)} observations")

            triangles_found.append((base_quote, quote_cross, base_cross))

    # Visualization of triangle residuals
    if triangle_residuals:
        n_triangles = len(triangle_residuals)
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        for i, (triangle_name, residual) in enumerate(triangle_residuals.items()):
            if i < 4:  # Plot first 4 triangles
                residual_clean = residual.dropna()

                # Time series plot
                axes[i].plot(residual_clean.index, residual_clean.values, alpha=0.7)
                axes[i].set_title(f'Triangle Residual: {triangle_name}')
                axes[i].set_xlabel('Date Index')
                axes[i].set_ylabel('Log Residual')
                axes[i].grid(True, alpha=0.3)
                axes[i].axhline(0, color='red', linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

    print(f"\n4) TRIANGLE RESIDUALS AS FEATURES:")
    print(f"   • Created {len(triangle_residuals)} triangle residual features")
    print(f"   • These can be used as:")
    print(f"     - Data quality indicators (large residuals = potential errors)")
    print(f"     - Market stress indicators (residual volatility)")
    print(f"     - Arbitrage opportunity features")

    return triangle_residuals, triangles_found

# =============================================================================
# CELL 6: Leak-Free Signal Scouting with Rank-IC Analysis
# =============================================================================
def cell_6_leak_free_signal_scouting(train, train_labels, target_taxonomy):
    """Compute leak-free signals and their rank-IC with targets"""
    print("="*80)
    print("ADVANCED EDA - CELL 6: LEAK-FREE SIGNAL SCOUTING")
    print("="*80)

    print("1) CREATING LEAK-FREE FEATURES:")
    print("   • 1-5 day momentum (log-returns)")
    print("   • Cross-sectional z-scores within asset groups")
    print("   • Relative strength indicators")

    # Get asset classes
    asset_classes = ['LME', 'JPX', 'US_Stock', 'FX']

    # Create momentum features for each asset class
    momentum_features = {}

    for asset_class in asset_classes:
        print(f"\n   Processing {asset_class} assets:")

        # Get close price columns for this asset class
        close_cols = [col for col in train.columns
                     if col.startswith(f'{asset_class}_') and col.endswith('_close')]

        print(f"     • Found {len(close_cols)} close price columns")

        if len(close_cols) > 0:
            # Calculate 1-5 day momentum for each asset
            for lag in [1, 2, 3, 5]:
                for col in close_cols[:10]:  # Limit to first 10 for demo
                    asset_name = col.replace(f'{asset_class}_', '').replace('_close', '')
                    feature_name = f'{asset_class}_{asset_name}_mom_{lag}d'

                    # Calculate log return
                    prices = train[col]
                    momentum = np.log(prices / prices.shift(lag))
                    momentum_features[feature_name] = momentum

    print(f"\n   • Created {len(momentum_features)} momentum features")

    # Calculate rank-IC between features and targets
    print(f"\n2) RANK-IC ANALYSIS:")
    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    # Sample analysis on subset for performance
    sample_targets = target_cols[:20]  # First 20 targets
    sample_features = list(momentum_features.keys())[:50]  # First 50 features

    print(f"   • Analyzing {len(sample_targets)} targets vs {len(sample_features)} features")

    # Align data by date_id
    feature_df = pd.DataFrame(momentum_features)
    feature_df['date_id'] = train['date_id']

    # Merge with labels
    merged_data = train_labels[['date_id'] + sample_targets].merge(
        feature_df[['date_id'] + sample_features], on='date_id', how='inner'
    )

    print(f"   • Merged data shape: {merged_data.shape}")

    # Calculate daily rank correlations
    daily_rank_ics = []

    for date_id in sorted(merged_data['date_id'].unique())[:100]:  # First 100 days for demo
        date_data = merged_data[merged_data['date_id'] == date_id]

        if len(date_data) > 0:
            # Get available targets and features for this day
            available_targets = [col for col in sample_targets
                               if not date_data[col].isna().all()]
            available_features = [col for col in sample_features
                                if not date_data[col].isna().all()]

            if len(available_targets) > 5 and len(available_features) > 5:
                target_data = date_data[available_targets].iloc[0]
                feature_data = date_data[available_features].iloc[0]

                # Calculate rank correlation
                target_ranks = target_data.rank()
                feature_ranks = feature_data.rank()

                # Compute IC for each feature
                feature_ics = {}
                for feature in available_features:
                    if feature in feature_ranks.index:
                        # This is a simplified IC calculation
                        # In practice, you'd correlate each feature with each target
                        ic = np.corrcoef(target_ranks.values,
                                       np.full(len(target_ranks), feature_ranks[feature]))[0,1]
                        if not np.isnan(ic):
                            feature_ics[feature] = ic

                daily_rank_ics.append({
                    'date_id': date_id,
                    'n_targets': len(available_targets),
                    'n_features': len(available_features),
                    'mean_ic': np.mean(list(feature_ics.values())) if feature_ics else 0
                })

    ic_df = pd.DataFrame(daily_rank_ics)

    if len(ic_df) > 0:
        print(f"\n3) RANK-IC RESULTS:")
        print(f"   • Mean daily IC: {ic_df['mean_ic'].mean():.4f}")
        print(f"   • IC standard deviation: {ic_df['mean_ic'].std():.4f}")
        print(f"   • IC Sharpe ratio: {ic_df['mean_ic'].mean() / ic_df['mean_ic'].std():.4f}")

        # Visualization
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))

        # IC over time
        axes[0].plot(ic_df['date_id'], ic_df['mean_ic'], alpha=0.7)
        axes[0].set_title('Daily Mean Rank-IC Over Time')
        axes[0].set_xlabel('Date ID')
        axes[0].set_ylabel('Mean Rank-IC')
        axes[0].grid(True, alpha=0.3)
        axes[0].axhline(0, color='red', linestyle='--', alpha=0.7)

        # IC distribution
        axes[1].hist(ic_df['mean_ic'], bins=20, alpha=0.7, edgecolor='black')
        axes[1].set_title('Distribution of Daily Mean Rank-IC')
        axes[1].set_xlabel('Mean Rank-IC')
        axes[1].set_ylabel('Count')
        axes[1].axvline(0, color='red', linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

    return momentum_features, ic_df

# =============================================================================
# CELL 7: Regime Detection and Market Structure Analysis
# =============================================================================
def cell_7_regime_detection(train_labels, target_taxonomy):
    """Detect market regimes based on dispersion and volatility patterns"""
    print("="*80)
    print("ADVANCED EDA - CELL 7: REGIME DETECTION & MARKET STRUCTURE")
    print("="*80)

    target_cols = [col for col in train_labels.columns if col.startswith('target_')]

    print("1) CROSS-SECTIONAL DISPERSION REGIMES:")

    # Calculate daily cross-sectional statistics
    daily_stats = []
    for date_id in sorted(train_labels['date_id'].unique()):
        date_data = train_labels[train_labels['date_id'] == date_id]
        available_data = date_data[target_cols].dropna(axis=1).iloc[0]

        if len(available_data) > 1:
            cs_std = available_data.std()
            cs_mean = available_data.mean()
            cs_skew = available_data.skew()
            cs_kurt = available_data.kurtosis()
            n_targets = len(available_data)

            # Calculate extreme value metrics
            abs_values = available_data.abs()
            p95_abs = abs_values.quantile(0.95)
            max_abs = abs_values.max()

            daily_stats.append({
                'date_id': date_id,
                'cs_std': cs_std,
                'cs_mean': cs_mean,
                'cs_skew': cs_skew,
                'cs_kurt': cs_kurt,
                'n_targets': n_targets,
                'p95_abs': p95_abs,
                'max_abs': max_abs
            })

    regime_df = pd.DataFrame(daily_stats)

    # Define regimes based on dispersion
    regime_df['dispersion_regime'] = pd.cut(regime_df['cs_std'],
                                          bins=3,
                                          labels=['Low', 'Medium', 'High'])

    # Define volatility regimes based on extreme values
    vol_threshold_95 = regime_df['p95_abs'].quantile(0.9)
    regime_df['volatility_regime'] = regime_df['p95_abs'].apply(
        lambda x: 'High' if x > vol_threshold_95 else 'Normal'
    )

    print(f"   • Dispersion regime distribution:")
    disp_counts = regime_df['dispersion_regime'].value_counts()
    for regime, count in disp_counts.items():
        print(f"     - {regime}: {count} days ({count/len(regime_df)*100:.1f}%)")

    print(f"\n   • Volatility regime distribution:")
    vol_counts = regime_df['volatility_regime'].value_counts()
    for regime, count in vol_counts.items():
        print(f"     - {regime}: {count} days ({count/len(regime_df)*100:.1f}%)")

    # Identify the stress period (dates 562-575 from previous analysis)
    stress_period = regime_df[(regime_df['date_id'] >= 560) & (regime_df['date_id'] <= 580)]
    normal_period = regime_df[(regime_df['date_id'] < 560) | (regime_df['date_id'] > 580)]

    print(f"\n2) STRESS PERIOD ANALYSIS:")
    print(f"   • Stress period (dates 560-580): {len(stress_period)} days")
    print(f"   • Normal period: {len(normal_period)} days")

    if len(stress_period) > 0 and len(normal_period) > 0:
        print(f"   • Stress vs Normal dispersion:")
        print(f"     - Normal mean std: {normal_period['cs_std'].mean():.4f}")
        print(f"     - Stress mean std: {stress_period['cs_std'].mean():.4f}")
        print(f"     - Ratio: {stress_period['cs_std'].mean() / normal_period['cs_std'].mean():.1f}x")

        print(f"   • Stress vs Normal extreme values:")
        print(f"     - Normal mean p95: {normal_period['p95_abs'].mean():.4f}")
        print(f"     - Stress mean p95: {stress_period['p95_abs'].mean():.4f}")
        print(f"     - Ratio: {stress_period['p95_abs'].mean() / normal_period['p95_abs'].mean():.1f}x")

    # Regime transition analysis
    print(f"\n3) REGIME TRANSITION ANALYSIS:")
    regime_df['prev_disp_regime'] = regime_df['dispersion_regime'].shift(1)
    regime_df['prev_vol_regime'] = regime_df['volatility_regime'].shift(1)

    # Calculate transition probabilities
    transitions = regime_df.groupby(['prev_disp_regime', 'dispersion_regime']).size().unstack(fill_value=0)
    transition_probs = transitions.div(transitions.sum(axis=1), axis=0)

    print(f"   • Dispersion regime transition probabilities:")
    print(transition_probs.round(3))

    # Visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # Dispersion over time
    axes[0,0].plot(regime_df['date_id'], regime_df['cs_std'], alpha=0.7)
    axes[0,0].set_title('Cross-Sectional Dispersion Over Time')
    axes[0,0].set_xlabel('Date ID')
    axes[0,0].set_ylabel('Cross-Sectional Std')
    axes[0,0].grid(True, alpha=0.3)

    # Extreme values over time
    axes[0,1].plot(regime_df['date_id'], regime_df['p95_abs'], alpha=0.7, label='95th percentile')
    axes[0,1].plot(regime_df['date_id'], regime_df['max_abs'], alpha=0.7, label='Maximum')
    axes[0,1].set_title('Extreme Values Over Time')
    axes[0,1].set_xlabel('Date ID')
    axes[0,1].set_ylabel('|Target Value|')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)

    # Regime distribution
    regime_df['dispersion_regime'].value_counts().plot(kind='bar', ax=axes[0,2])
    axes[0,2].set_title('Dispersion Regime Distribution')
    axes[0,2].set_xlabel('Regime')
    axes[0,2].set_ylabel('Count')

    # Dispersion vs number of targets
    axes[1,0].scatter(regime_df['n_targets'], regime_df['cs_std'], alpha=0.6)
    axes[1,0].set_title('Dispersion vs Number of Targets')
    axes[1,0].set_xlabel('Number of Available Targets')
    axes[1,0].set_ylabel('Cross-Sectional Std')
    axes[1,0].grid(True, alpha=0.3)

    # Regime heatmap
    regime_counts = regime_df.groupby(['dispersion_regime', 'volatility_regime']).size().unstack(fill_value=0)
    sns.heatmap(regime_counts, annot=True, fmt='d', ax=axes[1,1])
    axes[1,1].set_title('Regime Combination Heatmap')

    # Stress period highlighting
    axes[1,2].plot(regime_df['date_id'], regime_df['cs_std'], alpha=0.7)
    stress_mask = (regime_df['date_id'] >= 560) & (regime_df['date_id'] <= 580)
    axes[1,2].scatter(regime_df.loc[stress_mask, 'date_id'],
                     regime_df.loc[stress_mask, 'cs_std'],
                     color='red', s=50, alpha=0.8, label='Stress Period')
    axes[1,2].set_title('Stress Period Identification')
    axes[1,2].set_xlabel('Date ID')
    axes[1,2].set_ylabel('Cross-Sectional Std')
    axes[1,2].legend()
    axes[1,2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    return regime_df

# =============================================================================
# CELL 8: Series Health Check and Structural Breaks
# =============================================================================
def cell_8_series_health_check(train):
    """Identify problematic series with low coverage or structural breaks"""
    print("="*80)
    print("ADVANCED EDA - CELL 8: SERIES HEALTH CHECK")
    print("="*80)

    # Exclude date_id column
    feature_cols = [col for col in train.columns if col != 'date_id']

    print("1) COVERAGE ANALYSIS:")
    coverage_stats = {}

    for col in feature_cols:
        data = train[col]
        non_missing = data.notna()
        coverage_pct = (non_missing.sum() / len(data)) * 100

        # Find longest gap
        missing_mask = data.isna()
        gap_lengths = []
        current_gap = 0

        for is_missing in missing_mask:
            if is_missing:
                current_gap += 1
            else:
                if current_gap > 0:
                    gap_lengths.append(current_gap)
                current_gap = 0

        if current_gap > 0:
            gap_lengths.append(current_gap)

        max_gap = max(gap_lengths) if gap_lengths else 0

        coverage_stats[col] = {
            'coverage_pct': coverage_pct,
            'max_gap': max_gap,
            'n_gaps': len(gap_lengths)
        }

    coverage_df = pd.DataFrame(coverage_stats).T

    # Identify problematic series
    low_coverage_threshold = 50
    high_gap_threshold = 100

    low_coverage_series = coverage_df[coverage_df['coverage_pct'] < low_coverage_threshold]
    high_gap_series = coverage_df[coverage_df['max_gap'] > high_gap_threshold]

    print(f"   • Total features analyzed: {len(coverage_df)}")
    print(f"   • Low coverage (<{low_coverage_threshold}%): {len(low_coverage_series)}")
    print(f"   • High gaps (>{high_gap_threshold} days): {len(high_gap_series)}")

    print(f"\n   • Worst coverage series:")
    worst_coverage = coverage_df.nsmallest(10, 'coverage_pct')
    for series, row in worst_coverage.iterrows():
        print(f"     {series}: {row['coverage_pct']:.1f}% coverage, max gap: {row['max_gap']} days")

    # Asset class breakdown
    print(f"\n2) COVERAGE BY ASSET CLASS:")
    asset_classes = ['LME', 'JPX', 'US_Stock', 'FX']

    for asset_class in asset_classes:
        asset_cols = [col for col in feature_cols if col.startswith(f'{asset_class}_')]
        if len(asset_cols) > 0:
            asset_coverage = coverage_df.loc[asset_cols, 'coverage_pct']
            print(f"   • {asset_class}: {len(asset_cols)} series")
            print(f"     - Mean coverage: {asset_coverage.mean():.1f}%")
            print(f"     - Min coverage: {asset_coverage.min():.1f}%")
            print(f"     - Series <50% coverage: {(asset_coverage < 50).sum()}")

    # Simple structural break detection using variance changes
    print(f"\n3) STRUCTURAL BREAK DETECTION (SIMPLIFIED):")

    # Sample a few series for break detection
    sample_series = feature_cols[:20]  # First 20 series for demo
    break_results = {}

    for col in sample_series:
        data = train[col].dropna()
        if len(data) > 100:  # Need sufficient data
            # Split into two halves and compare variances
            mid_point = len(data) // 2
            first_half = data.iloc[:mid_point]
            second_half = data.iloc[mid_point:]

            if len(first_half) > 10 and len(second_half) > 10:
                var_ratio = second_half.var() / first_half.var()
                mean_shift = abs(second_half.mean() - first_half.mean()) / first_half.std()

                break_results[col] = {
                    'variance_ratio': var_ratio,
                    'mean_shift_zscore': mean_shift,
                    'potential_break': var_ratio > 2 or var_ratio < 0.5 or mean_shift > 2
                }

    break_df = pd.DataFrame(break_results).T
    potential_breaks = break_df[break_df['potential_break'] == True]

    print(f"   • Series analyzed for breaks: {len(break_df)}")
    print(f"   • Potential structural breaks: {len(potential_breaks)}")

    if len(potential_breaks) > 0:
        print(f"   • Series with potential breaks:")
        for series, row in potential_breaks.head().iterrows():
            print(f"     {series}: var_ratio={row['variance_ratio']:.2f}, mean_shift={row['mean_shift_zscore']:.2f}")

    # Visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Coverage distribution
    axes[0,0].hist(coverage_df['coverage_pct'], bins=30, alpha=0.7, edgecolor='black')
    axes[0,0].axvline(low_coverage_threshold, color='red', linestyle='--', label=f'{low_coverage_threshold}% threshold')
    axes[0,0].set_title('Distribution of Series Coverage %')
    axes[0,0].set_xlabel('Coverage %')
    axes[0,0].set_ylabel('Count')
    axes[0,0].legend()

    # Max gap distribution
    axes[0,1].hist(coverage_df['max_gap'], bins=30, alpha=0.7, edgecolor='black')
    axes[0,1].axvline(high_gap_threshold, color='red', linestyle='--', label=f'{high_gap_threshold} day threshold')
    axes[0,1].set_title('Distribution of Maximum Gaps')
    axes[0,1].set_xlabel('Max Gap (days)')
    axes[0,1].set_ylabel('Count')
    axes[0,1].legend()

    # Coverage vs gaps
    axes[1,0].scatter(coverage_df['coverage_pct'], coverage_df['max_gap'], alpha=0.6)
    axes[1,0].set_title('Coverage % vs Maximum Gap')
    axes[1,0].set_xlabel('Coverage %')
    axes[1,0].set_ylabel('Max Gap (days)')
    axes[1,0].grid(True, alpha=0.3)

    # Structural break indicators
    if len(break_df) > 0:
        axes[1,1].scatter(break_df['variance_ratio'], break_df['mean_shift_zscore'], alpha=0.6)
        axes[1,1].axvline(2, color='red', linestyle='--', alpha=0.7)
        axes[1,1].axvline(0.5, color='red', linestyle='--', alpha=0.7)
        axes[1,1].axhline(2, color='red', linestyle='--', alpha=0.7)
        axes[1,1].set_title('Structural Break Indicators')
        axes[1,1].set_xlabel('Variance Ratio (2nd half / 1st half)')
        axes[1,1].set_ylabel('Mean Shift (Z-score)')
        axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # Create drop list
    drop_list = list(low_coverage_series.index) + list(potential_breaks.index)
    drop_list = list(set(drop_list))  # Remove duplicates

    print(f"\n4) RECOMMENDED ACTIONS:")
    print(f"   • Series to consider dropping: {len(drop_list)}")
    print(f"   • Low coverage series: {len(low_coverage_series)}")
    print(f"   • Structural break series: {len(potential_breaks)}")

    return coverage_df, break_df, drop_list

# =============================================================================
# CELL 9: Industry-Grounded Feature Engineering
# =============================================================================
def cell_9_industry_feature_engineering(train, target_pairs):
    """Create industry-specific features based on economic relationships"""
    print("="*80)
    print("ADVANCED EDA - CELL 9: INDUSTRY-GROUNDED FEATURE ENGINEERING")
    print("="*80)

    print("1) METALS-MINERS LINKAGE FEATURES:")

    # Get LME metals and US mining stocks
    lme_cols = [col for col in train.columns if col.startswith('LME_') and col.endswith('_close')]
    mining_stocks = [col for col in train.columns
                    if col.startswith('US_Stock_') and col.endswith('_close') and
                    any(metal in col.upper() for metal in ['COPPER', 'GOLD', 'SILVER', 'FCX', 'NEM', 'SCCO'])]

    print(f"   • LME metals found: {len(lme_cols)}")
    print(f"   • Mining stocks found: {len(mining_stocks)}")

    # Create metal-miner spread features
    metal_miner_features = {}

    # Copper-related features
    copper_cols = [col for col in lme_cols if 'COPPER' in col.upper()]
    copper_miners = [col for col in mining_stocks if any(ticker in col for ticker in ['FCX', 'SCCO'])]

    if len(copper_cols) > 0 and len(copper_miners) > 0:
        print(f"   • Creating copper-miner features:")
        for copper_col in copper_cols:
            for miner_col in copper_miners:
                # Log price ratio
                copper_prices = train[copper_col]
                miner_prices = train[miner_col]

                feature_name = f"copper_miner_spread_{copper_col.split('_')[1]}_{miner_col.split('_')[2]}"
                spread = np.log(copper_prices) - np.log(miner_prices)
                metal_miner_features[feature_name] = spread
                print(f"     - {feature_name}")

    print(f"\n2) FX PASS-THROUGH FEATURES:")

    # Get commodity currencies and related commodities
    commodity_fx = [col for col in train.columns
                   if col.startswith('FX_') and col.endswith('_close') and
                   any(curr in col for curr in ['AUD', 'CAD', 'NZD', 'NOK'])]

    fx_passthrough_features = {}

    # AUD vs metals (Australia is major metals exporter)
    aud_cols = [col for col in commodity_fx if 'AUD' in col]
    if len(aud_cols) > 0 and len(lme_cols) > 0:
        print(f"   • Creating AUD-metals pass-through features:")
        for aud_col in aud_cols:
            for metal_col in lme_cols[:3]:  # First 3 metals
                feature_name = f"aud_metal_passthrough_{aud_col.split('_')[1]}_{metal_col.split('_')[1]}"
                aud_returns = np.log(train[aud_col] / train[aud_col].shift(1))
                metal_returns = np.log(train[metal_col] / train[metal_col].shift(1))

                # Rolling correlation as feature
                rolling_corr = aud_returns.rolling(20).corr(metal_returns)
                fx_passthrough_features[feature_name] = rolling_corr
                print(f"     - {feature_name}")

    print(f"\n3) TERM STRUCTURE FEATURES:")

    # Look for different contract months in JPX futures
    jpx_cols = [col for col in train.columns if col.startswith('JPX_') and col.endswith('_close')]

    term_structure_features = {}

    # Group by underlying asset
    jpx_assets = {}
    for col in jpx_cols:
        parts = col.split('_')
        if len(parts) >= 3:
            asset_base = '_'.join(parts[1:-2])  # Remove JPX prefix and _close suffix
            if asset_base not in jpx_assets:
                jpx_assets[asset_base] = []
            jpx_assets[asset_base].append(col)

    print(f"   • JPX asset groups found: {len(jpx_assets)}")

    # Create term structure spreads for assets with multiple contracts
    for asset, contracts in jpx_assets.items():
        if len(contracts) >= 2:
            print(f"   • Creating term structure for {asset}: {len(contracts)} contracts")

            # Sort contracts (assuming naming convention includes month/year)
            contracts_sorted = sorted(contracts)

            # Create front-back spread
            front_contract = train[contracts_sorted[0]]
            back_contract = train[contracts_sorted[-1]]

            spread_name = f"jpx_term_spread_{asset}"
            term_spread = np.log(back_contract) - np.log(front_contract)
            term_structure_features[spread_name] = term_spread

    print(f"\n4) CROSS-ASSET MOMENTUM FEATURES:")

    # Create momentum features across asset classes
    momentum_features = {}

    asset_classes = {
        'LME': [col for col in train.columns if col.startswith('LME_') and col.endswith('_close')],
        'US_Stock': [col for col in train.columns if col.startswith('US_Stock_') and col.endswith('_close')],
        'FX': [col for col in train.columns if col.startswith('FX_') and col.endswith('_close')]
    }

    # Calculate asset class momentum
    for asset_class, cols in asset_classes.items():
        if len(cols) > 0:
            # Take first few assets for demo
            sample_cols = cols[:5]

            for lag in [1, 5, 10]:
                class_momentum = []

                for col in sample_cols:
                    momentum = np.log(train[col] / train[col].shift(lag))
                    class_momentum.append(momentum)

                if len(class_momentum) > 0:
                    # Average momentum across asset class
                    avg_momentum = pd.concat(class_momentum, axis=1).mean(axis=1)
                    feature_name = f"{asset_class}_momentum_{lag}d"
                    momentum_features[feature_name] = avg_momentum
                    print(f"   • Created {feature_name}")

    # Combine all features
    all_features = {}
    all_features.update(metal_miner_features)
    all_features.update(fx_passthrough_features)
    all_features.update(term_structure_features)
    all_features.update(momentum_features)

    print(f"\n5) FEATURE SUMMARY:")
    print(f"   • Metal-miner features: {len(metal_miner_features)}")
    print(f"   • FX pass-through features: {len(fx_passthrough_features)}")
    print(f"   • Term structure features: {len(term_structure_features)}")
    print(f"   • Cross-asset momentum features: {len(momentum_features)}")
    print(f"   • Total engineered features: {len(all_features)}")

    return all_features

# =============================================================================
# CELL 10: Final Summary and Modeling Recommendations
# =============================================================================
def cell_10_final_summary_and_recommendations():
    """Provide final summary and modeling recommendations"""
    print("="*80)
    print("ADVANCED EDA - CELL 10: FINAL SUMMARY & MODELING RECOMMENDATIONS")
    print("="*80)

    print("🎯 KEY FINDINGS SUMMARY:")
    print("="*50)

    print("\n1) DATA STRUCTURE:")
    print("   ✅ Target calculation verified: log(A[t+lag+1]/A[t+1]) - log(B[t+lag+1]/B[t+1])")
    print("   ✅ Look-ahead structure understood: targets use future prices")
    print("   ✅ 424 targets across 4 lag periods (1-4 days)")
    print("   ⚠️  10.6% missing data overall, some targets 17.9% missing")

    print("\n2) MARKET REGIMES:")
    print("   🔥 Major stress period identified: dates 562-575")
    print("   📊 3.7x dispersion increase during stress")
    print("   🔄 Moderate rank persistence: 0.452 Spearman correlation")
    print("   ⚡ Short half-lives: 1.3 days average")

    print("\n3) DATA QUALITY HIERARCHY:")
    print("   🥇 FX: Excellent coverage, low noise")
    print("   🥈 LME: Good coverage, moderate noise")
    print("   🥉 US_Stock: Variable coverage, some delisted assets")
    print("   🔴 JPX: Moderate issues, gaps present")

    print("\n4) FEATURE ENGINEERING OPPORTUNITIES:")
    print("   🏭 Metal-miner linkages: Strong correlations (0.77-0.89)")
    print("   💱 FX pass-through effects: Commodity currencies")
    print("   📈 Term structure signals: JPX futures spreads")
    print("   🌊 Cross-asset momentum: Asset class rotation")

    print("\n" + "="*50)
    print("🚀 MODELING RECOMMENDATIONS:")
    print("="*50)

    print("\n1) TIME-BASED CROSS-VALIDATION:")
    print("   • Use expanding window CV with stress period isolation")
    print("   • Separate validation for normal vs stress regimes")
    print("   • Respect lag structure: no future leakage")

    print("\n2) FEATURE ENGINEERING PRIORITIES:")
    print("   • Relative returns following target_calculation.py patterns")
    print("   • Cross-sectional z-scores within asset groups")
    print("   • Regime-aware features (normal vs stress indicators)")
    print("   • Industry-grounded relationships (metals-miners, FX-commodities)")

    print("\n3) MODEL ARCHITECTURE:")
    print("   • Ensemble approach: separate models for each regime")
    print("   • Pair-aware models understanding cross-asset relationships")
    print("   • Online learning for adaptation to regime changes")
    print("   • Spearman-optimized loss functions")

    print("\n4) RISK MANAGEMENT:")
    print("   • Monitor for regime transitions using dispersion metrics")
    print("   • Implement coverage-aware predictions (handle missing targets)")
    print("   • Stress-test on identified spike periods")
    print("   • Use triangle residuals for FX data quality monitoring")

    print("\n5) EVALUATION STRATEGY:")
    print("   • Daily Spearman rank correlation as primary metric")
    print("   • Sharpe ratio of daily correlations for stability")
    print("   • Regime-specific performance analysis")
    print("   • Coverage-adjusted metrics for missing targets")

    print("\n" + "="*50)
    print("⚡ IMMEDIATE NEXT STEPS:")
    print("="*50)

    print("\n1. Load target_pairs.csv and verify all target mappings")
    print("2. Implement leak-free feature engineering pipeline")
    print("3. Create regime detection system using dispersion metrics")
    print("4. Build time-based CV framework with proper lag handling")
    print("5. Develop Spearman-optimized baseline model")
    print("6. Test on stress period (dates 562-575) for robustness")

    print("\n" + "="*80)
    print("🎉 ADVANCED EDA COMPLETE - READY FOR MODELING!")
    print("="*80)

# =============================================================================
# MAIN EXECUTION FUNCTION
# =============================================================================
def run_advanced_eda():
    """Execute all advanced EDA cells in sequence"""
    print("🚀 STARTING ADVANCED EDA FOR COMMODITY PRICES COMPETITION")
    print("="*80)

    try:
        # Cell 1: Load data and analyze target pairs
        train, train_labels, target_pairs, target_taxonomy = cell_1_load_and_target_pairs()

        # Cell 2: Alignment audit
        future_leak_risk = cell_2_alignment_audit(train, train_labels, target_pairs)

        # Cell 3: Coverage dashboard
        coverage_df = cell_3_coverage_integrity_dashboard(train_labels, target_taxonomy)

        # Cell 4: Small-N day anatomy
        availability_df = cell_4_small_n_day_anatomy(train_labels, target_taxonomy)

        # Cell 5: FX triangle residuals
        triangle_residuals, triangles_found = cell_5_fx_triangle_residuals(train)

        # Cell 6: Leak-free signal scouting
        momentum_features, ic_df = cell_6_leak_free_signal_scouting(train, train_labels, target_taxonomy)

        # Cell 7: Regime detection
        regime_df = cell_7_regime_detection(train_labels, target_taxonomy)

        # Cell 8: Series health check
        coverage_df_features, break_df, drop_list = cell_8_series_health_check(train)

        # Cell 9: Industry feature engineering
        engineered_features = cell_9_industry_feature_engineering(train, target_pairs)

        # Cell 10: Final summary
        cell_10_final_summary_and_recommendations()

        print("\n✅ ALL ADVANCED EDA CELLS COMPLETED SUCCESSFULLY!")

        return {
            'train': train,
            'train_labels': train_labels,
            'target_pairs': target_pairs,
            'target_taxonomy': target_taxonomy,
            'coverage_df': coverage_df,
            'availability_df': availability_df,
            'triangle_residuals': triangle_residuals,
            'momentum_features': momentum_features,
            'ic_df': ic_df,
            'regime_df': regime_df,
            'engineered_features': engineered_features,
            'drop_list': drop_list,
            'future_leak_risk': future_leak_risk
        }

    except Exception as e:
        print(f"❌ ERROR in advanced EDA: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if __name__ == "__main__":
    # Run the complete advanced EDA
    results = run_advanced_eda()

    if results:
        print("\n🎯 EDA Results available in 'results' dictionary")
        print("📊 Key objects: train, train_labels, target_pairs, regime_df, engineered_features")
    else:
        print("\n❌ EDA failed - check error messages above")