# Cell 1: Setup and Data Loading
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings

# Configure display and plotting
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('default')
sns.set_palette("husl")

# Auto-detect environment and set data path
def get_data_path():
    """Auto-detect if running on Kaggle, Colab, or local"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if kaggle_path.exists():
        print("🔍 Detected Kaggle environment")
        return kaggle_path
    elif colab_path.exists():
        print("🔍 Detected Google Colab environment")
        return colab_path
    elif local_path.exists():
        print("🔍 Detected local environment")
        return local_path
    else:
        print("⚠️  No data path found, using local path as default")
        return local_path

DATA_PATH = get_data_path()

# Load train.csv
print(f"Loading train.csv from: {DATA_PATH}")
train_df = pd.read_csv(DATA_PATH / "train.csv")

print(f"✅ Successfully loaded train.csv")
print(f"Shape: {train_df.shape}")

# Cell 2: Basic Dataset Overview
print("="*60)
print("TRAIN.CSV - BASIC OVERVIEW")
print("="*60)

# Basic info
print(f"Dataset shape: {train_df.shape}")
print(f"Rows (time periods): {train_df.shape[0]:,}")
print(f"Columns (features + date_id): {train_df.shape[1]:,}")

# Memory usage
memory_mb = train_df.memory_usage(deep=True).sum() / 1024**2
print(f"Memory usage: {memory_mb:.2f} MB")

# Column types
print(f"\nColumn types:")
print(train_df.dtypes.value_counts())

# First few rows
print(f"\nFirst 5 rows:")
train_df.head()

# Cell 3: Date ID and Temporal Structure
print("="*60)
print("DATE_ID AND TEMPORAL STRUCTURE")
print("="*60)

# Examine date_id column
print(f"Date ID column info:")
print(f"  Data type: {train_df['date_id'].dtype}")
print(f"  Unique values: {train_df['date_id'].nunique()}")
print(f"  Min date_id: {train_df['date_id'].min()}")
print(f"  Max date_id: {train_df['date_id'].max()}")
print(f"  Date range span: {train_df['date_id'].max() - train_df['date_id'].min()} days")

# Check for duplicates
duplicates = train_df['date_id'].duplicated().sum()
print(f"  Duplicate date_ids: {duplicates}")

# Check for gaps in the sequence
date_ids = sorted(train_df['date_id'].unique())
expected_range = list(range(min(date_ids), max(date_ids) + 1))
missing_dates = set(expected_range) - set(date_ids)

print(f"\nTemporal continuity:")
print(f"  Expected dates in range: {len(expected_range)}")
print(f"  Actual dates present: {len(date_ids)}")
print(f"  Missing dates: {len(missing_dates)}")

if len(missing_dates) > 0:
    print(f"  Missing date_ids: {sorted(list(missing_dates))[:10]}{'...' if len(missing_dates) > 10 else ''}")

# Show first and last few date_ids
print(f"\nFirst 10 date_ids: {date_ids[:10]}")
print(f"Last 10 date_ids: {date_ids[-10:]}")