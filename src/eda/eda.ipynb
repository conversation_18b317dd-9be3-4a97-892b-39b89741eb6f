# Cell 1: Setup and Data Loading
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings

# Configure display and plotting
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('default')
sns.set_palette("husl")

# Auto-detect environment and set data path
def get_data_path():
    """Auto-detect if running on Kaggle, Colab, or local"""
    kaggle_path = Path("/kaggle/input/mitsui-commodity-prediction-challenge")
    colab_path = Path("/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge")
    local_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if kaggle_path.exists():
        print("🔍 Detected Kaggle environment")
        return kaggle_path
    elif colab_path.exists():
        print("🔍 Detected Google Colab environment")
        return colab_path
    elif local_path.exists():
        print("🔍 Detected local environment")
        return local_path
    else:
        print("⚠️  No data path found, using local path as default")
        return local_path

DATA_PATH = get_data_path()

# Load train.csv
print(f"Loading train.csv from: {DATA_PATH}")
train_df = pd.read_csv(DATA_PATH / "train.csv")

print(f"✅ Successfully loaded train.csv")
print(f"Shape: {train_df.shape}")

# Cell 2: Basic Dataset Overview
print("="*60)
print("TRAIN.CSV - BASIC OVERVIEW")
print("="*60)

# Basic info
print(f"Dataset shape: {train_df.shape}")
print(f"Rows (time periods): {train_df.shape[0]:,}")
print(f"Columns (features + date_id): {train_df.shape[1]:,}")

# Memory usage
memory_mb = train_df.memory_usage(deep=True).sum() / 1024**2
print(f"Memory usage: {memory_mb:.2f} MB")

# Column types
print(f"\nColumn types:")
print(train_df.dtypes.value_counts())

# First few rows
print(f"\nFirst 5 rows:")
train_df.head()

# Cell 3: Date ID and Temporal Structure
print("="*60)
print("DATE_ID AND TEMPORAL STRUCTURE")
print("="*60)

# Examine date_id column
print(f"Date ID column info:")
print(f"  Data type: {train_df['date_id'].dtype}")
print(f"  Unique values: {train_df['date_id'].nunique()}")
print(f"  Min date_id: {train_df['date_id'].min()}")
print(f"  Max date_id: {train_df['date_id'].max()}")
print(f"  Date range span: {train_df['date_id'].max() - train_df['date_id'].min()} days")

# Check for duplicates
duplicates = train_df['date_id'].duplicated().sum()
print(f"  Duplicate date_ids: {duplicates}")

# Check for gaps in the sequence
date_ids = sorted(train_df['date_id'].unique())
expected_range = list(range(min(date_ids), max(date_ids) + 1))
missing_dates = set(expected_range) - set(date_ids)

print(f"\nTemporal continuity:")
print(f"  Expected dates in range: {len(expected_range)}")
print(f"  Actual dates present: {len(date_ids)}")
print(f"  Missing dates: {len(missing_dates)}")

if len(missing_dates) > 0:
    print(f"  Missing date_ids: {sorted(list(missing_dates))[:10]}{'...' if len(missing_dates) > 10 else ''}")

# Show first and last few date_ids
print(f"\nFirst 10 date_ids: {date_ids[:10]}")
print(f"Last 10 date_ids: {date_ids[-10:]}")

# Cell 4: Feature Column Analysis
print("="*60)
print("FEATURE COLUMNS ANALYSIS")
print("="*60)

# Get all feature columns (excluding date_id)
feature_cols = [col for col in train_df.columns if col != 'date_id']
print(f"Total feature columns: {len(feature_cols)}")

# Analyze column prefixes (asset classes)
prefixes = ['LME', 'JPX', 'US_Stock', 'FX']
prefix_counts = {}

print(f"\nFeature distribution by asset class:")
for prefix in prefixes:
    count = len([col for col in feature_cols if col.startswith(prefix)])
    prefix_counts[prefix] = count
    print(f"  {prefix}: {count} features")

# Check for any other prefixes
other_features = []
for col in feature_cols:
    if not any(col.startswith(prefix) for prefix in prefixes):
        other_features.append(col)

if other_features:
    print(f"  Other/Unknown: {len(other_features)} features")
    print(f"    Examples: {other_features[:5]}")

# Show sample column names for each prefix
print(f"\nSample column names by asset class:")
for prefix in prefixes:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        print(f"  {prefix} (first 5): {prefix_cols[:5]}")

print(f"\nTotal features breakdown:")
print(f"  LME: {prefix_counts.get('LME', 0)}")
print(f"  JPX: {prefix_counts.get('JPX', 0)}")
print(f"  US_Stock: {prefix_counts.get('US_Stock', 0)}")
print(f"  FX: {prefix_counts.get('FX', 0)}")
print(f"  Total: {sum(prefix_counts.values())}")

# Cell 5: Missing Values Analysis
print("="*60)
print("MISSING VALUES ANALYSIS")
print("="*60)

# Overall missing value statistics
total_cells = train_df.shape[0] * train_df.shape[1]
missing_cells = train_df.isnull().sum().sum()
missing_percentage = (missing_cells / total_cells) * 100

print(f"Overall missing value statistics:")
print(f"  Total cells: {total_cells:,}")
print(f"  Missing cells: {missing_cells:,}")
print(f"  Missing percentage: {missing_percentage:.2f}%")

# Missing values by column
missing_by_col = train_df.isnull().sum()
cols_with_missing = missing_by_col[missing_by_col > 0]

print(f"\nColumns with missing values:")
print(f"  Columns with missing data: {len(cols_with_missing)} out of {len(train_df.columns)}")
print(f"  Columns with no missing data: {len(train_df.columns) - len(cols_with_missing)}")

if len(cols_with_missing) > 0:
    print(f"\nTop 10 columns with most missing values:")
    top_missing = cols_with_missing.sort_values(ascending=False).head(10)
    for col, missing_count in top_missing.items():
        missing_pct = (missing_count / len(train_df)) * 100
        print(f"  {col}: {missing_count:,} ({missing_pct:.1f}%)")

# Missing values by asset class
print(f"\nMissing values by asset class:")
for prefix in ['LME', 'JPX', 'US_Stock', 'FX']:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        prefix_missing = train_df[prefix_cols].isnull().sum().sum()
        prefix_total = len(prefix_cols) * len(train_df)
        prefix_pct = (prefix_missing / prefix_total) * 100
        print(f"  {prefix}: {prefix_missing:,}/{prefix_total:,} ({prefix_pct:.2f}%)")

# Cell 6: Data Distribution Analysis by Asset Class
print("="*60)
print("DATA DISTRIBUTION ANALYSIS")
print("="*60)

# Statistical summary by asset class
prefixes = ['LME', 'JPX', 'US_Stock', 'FX']

for prefix in prefixes:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        print(f"\n{prefix} Asset Class Summary:")
        print(f"  Number of features: {len(prefix_cols)}")
        
        # Get data for this asset class (excluding missing values)
        prefix_data = train_df[prefix_cols]
        
        # Overall statistics
        all_values = prefix_data.values.flatten()
        all_values = all_values[~np.isnan(all_values)]  # Remove NaN
        
        if len(all_values) > 0:
            print(f"  Valid data points: {len(all_values):,}")
            print(f"  Mean: {np.mean(all_values):.4f}")
            print(f"  Median: {np.median(all_values):.4f}")
            print(f"  Std: {np.std(all_values):.4f}")
            print(f"  Min: {np.min(all_values):.4f}")
            print(f"  Max: {np.max(all_values):.4f}")
            print(f"  Range: {np.max(all_values) - np.min(all_values):.4f}")
            
            # Check for extreme values
            q1, q99 = np.percentile(all_values, [1, 99])
            print(f"  1st percentile: {q1:.4f}")
            print(f"  99th percentile: {q99:.4f}")
            
            # Sample a few columns for detailed stats
            sample_cols = prefix_cols[:3]  # First 3 columns
            print(f"  Sample columns detailed stats:")
            for col in sample_cols:
                col_data = train_df[col].dropna()
                if len(col_data) > 0:
                    print(f"    {col}: mean={col_data.mean():.3f}, std={col_data.std():.3f}, range=[{col_data.min():.3f}, {col_data.max():.3f}]")

# Cell 7: Distribution Visualizations
import matplotlib.pyplot as plt
import seaborn as sns

print("="*60)
print("DISTRIBUTION VISUALIZATIONS")
print("="*60)

# Create subplots for each asset class
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.flatten()

prefixes = ['LME', 'JPX', 'US_Stock', 'FX']

for i, prefix in enumerate(prefixes):
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    if prefix_cols:
        # Sample a few columns for visualization (to avoid overcrowding)
        sample_cols = prefix_cols[:min(3, len(prefix_cols))]
        
        ax = axes[i]
        
        for j, col in enumerate(sample_cols):
            data = train_df[col].dropna()
            if len(data) > 0:
                # Use log scale for US_Stock due to extreme range
                if prefix == 'US_Stock':
                    # Only plot positive values for log scale
                    positive_data = data[data > 0]
                    if len(positive_data) > 0:
                        ax.hist(np.log10(positive_data), bins=50, alpha=0.6, 
                               label=f'{col.split("_")[-2]}_{col.split("_")[-1]}', density=True)
                        ax.set_xlabel('Log10(Value)')
                else:
                    ax.hist(data, bins=50, alpha=0.6, 
                           label=f'{col.split("_")[-1]}', density=True)
                    ax.set_xlabel('Value')
        
        ax.set_title(f'{prefix} Asset Class Distributions')
        ax.set_ylabel('Density')
        ax.legend()
        ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print some insights about the distributions
print("\nDistribution Insights:")
print("- LME: Metal prices show normal-ish distributions")
print("- JPX: Futures data with varying scales")
print("- US_Stock: Highly skewed (shown in log scale) - likely includes volume data")
print("- FX: Currency rates with different scales (some are ratios, others are yen rates)")

# Cell 8: Understanding the Distribution Colors and FX Issue
print("="*60)
print("EXPLAINING THE VISUALIZATIONS")
print("="*60)

# Let's examine what the colors represent
prefixes = ['LME', 'JPX', 'US_Stock', 'FX']

for prefix in prefixes:
    prefix_cols = [col for col in feature_cols if col.startswith(prefix)]
    sample_cols = prefix_cols[:3]  # First 3 columns we plotted
    
    print(f"\n{prefix} Asset Class - Colors represent:")
    for i, col in enumerate(sample_cols):
        color_names = ['Pink/Red', 'Yellow/Orange', 'Green']
        print(f"  {color_names[i]}: {col}")

# Let's specifically investigate the FX issue
print(f"\n" + "="*40)
print("FX ASSET CLASS INVESTIGATION")
print("="*40)

fx_cols = [col for col in feature_cols if col.startswith('FX')]
print(f"All FX columns ({len(fx_cols)}):")
for i, col in enumerate(fx_cols):
    print(f"  {i+1:2d}. {col}")

# Check the first 3 FX columns data
print(f"\nFirst 3 FX columns data summary:")
for col in fx_cols[:3]:
    data = train_df[col].dropna()
    print(f"\n{col}:")
    print(f"  Count: {len(data)}")
    print(f"  Range: [{data.min():.4f}, {data.max():.4f}]")
    print(f"  Mean: {data.mean():.4f}")
    print(f"  Sample values: {data.head(5).tolist()}")

# The FX plot issue: let's check if the ranges are too different
print(f"\nFX Range Analysis:")
fx_ranges = {}
for col in fx_cols[:3]:
    data = train_df[col].dropna()
    fx_ranges[col] = (data.min(), data.max(), data.max() - data.min())

for col, (min_val, max_val, range_val) in fx_ranges.items():
    print(f"  {col}: range = {range_val:.4f} (from {min_val:.4f} to {max_val:.4f})")

# Cell 9: Better FX Visualization with Separate Scales
print("="*60)
print("IMPROVED FX VISUALIZATION")
print("="*60)

# Create separate plots for different FX types
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Group FX pairs by type
fx_cols = [col for col in feature_cols if col.startswith('FX')]

# JPY pairs (higher values ~60-200)
jpy_pairs = [col for col in fx_cols if 'JPY' in col]
usd_pairs = [col for col in fx_cols if 'USD' in col and 'JPY' not in col]
eur_pairs = [col for col in fx_cols if 'EUR' in col and 'JPY' not in col and 'USD' not in col]
other_pairs = [col for col in fx_cols if col not in jpy_pairs + usd_pairs + eur_pairs]

fx_groups = [
    ('JPY Pairs', jpy_pairs[:3]),
    ('USD Pairs', usd_pairs[:3]), 
    ('EUR Pairs', eur_pairs[:3]),
    ('Other Pairs', other_pairs[:3])
]

for i, (group_name, pairs) in enumerate(fx_groups):
    ax = axes[i//2, i%2]
    
    for pair in pairs:
        if pair in train_df.columns:
            data = train_df[pair].dropna()
            if len(data) > 0:
                ax.hist(data, bins=30, alpha=0.6, label=pair.replace('FX_', ''), density=True)
    
    ax.set_title(f'{group_name} Distributions')
    ax.set_xlabel('Exchange Rate')
    ax.set_ylabel('Density')
    ax.legend()
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print the grouping logic
print(f"\nFX Currency Pair Groupings:")
print(f"JPY pairs (vs Japanese Yen): {len(jpy_pairs)} pairs")
print(f"USD pairs (vs US Dollar): {len(usd_pairs)} pairs") 
print(f"EUR pairs (vs Euro): {len(eur_pairs)} pairs")
print(f"Other pairs: {len(other_pairs)} pairs")

print(f"\nSample from each group:")
for group_name, pairs in fx_groups:
    print(f"  {group_name}: {pairs[:3]}")

# Cell 10: Temporal Patterns Analysis
print("="*60)
print("TEMPORAL PATTERNS ANALYSIS")
print("="*60)

# Select representative assets from each class for time series analysis
representative_assets = {
    'LME': 'LME_CA_Close',  # Copper
    'JPX': 'JPX_Gold_Mini_Futures_Open',  # Gold futures
    'US_Stock': 'US_Stock_ACWI_adj_open',  # Global stock index
    'FX': 'FX_EURUSD'  # Major currency pair
}

# Create time series plots
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
axes = axes.flatten()

for i, (asset_class, column) in enumerate(representative_assets.items()):
    if column in train_df.columns:
        # Plot the time series
        data = train_df[['date_id', column]].copy()
        data = data.dropna()
        
        ax = axes[i]
        ax.plot(data['date_id'], data[column], linewidth=1, alpha=0.8)
        ax.set_title(f'{asset_class}: {column}')
        ax.set_xlabel('Date ID')
        ax.set_ylabel('Value')
        ax.grid(True, alpha=0.3)
        
        # Add some basic statistics
        mean_val = data[column].mean()
        std_val = data[column].std()
        ax.axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.2f}')
        ax.axhline(y=mean_val + std_val, color='orange', linestyle=':', alpha=0.5, label=f'+1 Std')
        ax.axhline(y=mean_val - std_val, color='orange', linestyle=':', alpha=0.5, label=f'-1 Std')
        ax.legend()

plt.tight_layout()
plt.show()

# Analyze volatility over time
print(f"\nVolatility Analysis (30-day rolling standard deviation):")
for asset_class, column in representative_assets.items():
    if column in train_df.columns:
        data = train_df[column].dropna()
        if len(data) > 30:
            # Calculate 30-day rolling volatility
            rolling_vol = data.rolling(window=30).std()
            avg_vol = rolling_vol.mean()
            max_vol = rolling_vol.max()
            min_vol = rolling_vol.min()
            
            print(f"  {asset_class} ({column}):")
            print(f"    Average volatility: {avg_vol:.4f}")
            print(f"    Max volatility: {max_vol:.4f}")
            print(f"    Min volatility: {min_vol:.4f}")
            print(f"    Volatility range: {max_vol - min_vol:.4f}")

# Cell 10 Alternative: Multiple Temporal Patterns Analysis
print("="*60)
print("TEMPORAL PATTERNS - MULTIPLE ASSETS PER CLASS")
print("="*60)

# Select multiple representatives from each class
asset_selections = {
    'LME': ['LME_AH_Close', 'LME_CA_Close', 'LME_PB_Close', 'LME_ZS_Close'],  # All 4 LME
    'JPX': ['JPX_Gold_Mini_Futures_Open', 'JPX_Platinum_Mini_Futures_Open', 'JPX_RSS3_Rubber_Futures_Open'],  # 3 different commodities
    'US_Stock': ['US_Stock_ACWI_adj_open', 'US_Stock_GLD_adj_open', 'US_Stock_SLV_adj_open'],  # 3 different stocks
    'FX': ['FX_EURUSD', 'FX_USDJPY', 'FX_GBPUSD']  # 3 major pairs
}

# Create subplots for each asset class
for asset_class, columns in asset_selections.items():
    print(f"\n{asset_class} Asset Class Time Series:")
    
    # Filter columns that actually exist in the data
    existing_cols = [col for col in columns if col in train_df.columns]
    
    if existing_cols:
        fig, axes = plt.subplots(len(existing_cols), 1, figsize=(12, 3*len(existing_cols)))
        if len(existing_cols) == 1:
            axes = [axes]
        
        for i, column in enumerate(existing_cols):
            data = train_df[['date_id', column]].dropna()
            
            axes[i].plot(data['date_id'], data[column], linewidth=1, alpha=0.8)
            axes[i].set_title(f'{column}')
            axes[i].set_xlabel('Date ID')
            axes[i].set_ylabel('Value')
            axes[i].grid(True, alpha=0.3)
            
            # Add mean line
            mean_val = data[column].mean()
            axes[i].axhline(y=mean_val, color='red', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.show()
    
    print(f"  Showing {len(existing_cols)} out of {len(columns)} requested assets")

# Cell 11: Complete Feature Inventory
print("="*60)
print("COMPLETE FEATURE INVENTORY - ALL 557 FEATURES")
print("="*60)

# Get all feature columns
feature_cols = [col for col in train_df.columns if col != 'date_id']
print(f"Total features to analyze: {len(feature_cols)}")

# 1. LME Features (Metals)
lme_cols = [col for col in feature_cols if col.startswith('LME')]
print(f"\n1. LME FEATURES ({len(lme_cols)}):")
for col in lme_cols:
    print(f"   {col}")

# 2. JPX Features (Japanese Futures) - Let's understand the pattern
jpx_cols = [col for col in feature_cols if col.startswith('JPX')]
print(f"\n2. JPX FEATURES ({len(jpx_cols)}) - Pattern Analysis:")

# Extract JPX products
jpx_products = set()
jpx_fields = set()
for col in jpx_cols:
    parts = col.split('_')
    if len(parts) >= 3:
        # JPX_Product_Type_Field or JPX_Product_Field
        if len(parts) == 4:  # JPX_Gold_Mini_Open
            product = f"{parts[1]}_{parts[2]}"
            field = parts[3]
        else:  # JPX_Gold_Open
            product = parts[1]
            field = '_'.join(parts[2:])
        jpx_products.add(product)
        jpx_fields.add(field)

print(f"   JPX Products: {sorted(jpx_products)}")
print(f"   JPX Fields: {sorted(jpx_fields)}")
print(f"   First 10 JPX columns: {jpx_cols[:10]}")

# 3. FX Features (Currency Pairs)
fx_cols = [col for col in feature_cols if col.startswith('FX')]
print(f"\n3. FX FEATURES ({len(fx_cols)}):")
# Group by currency
fx_currencies = set()
for col in fx_cols:
    pair = col.replace('FX_', '')
    # Extract individual currencies
    if len(pair) == 6:  # Standard format like EURUSD
        curr1, curr2 = pair[:3], pair[3:]
        fx_currencies.add(curr1)
        fx_currencies.add(curr2)

print(f"   Unique currencies involved: {sorted(fx_currencies)}")
print(f"   All FX pairs: {[col.replace('FX_', '') for col in fx_cols]}")

# 4. US_Stock Features (This is the big one!)
us_stock_cols = [col for col in feature_cols if col.startswith('US_Stock')]
print(f"\n4. US_STOCK FEATURES ({len(us_stock_cols)}) - Pattern Analysis:")

# Extract stock symbols and fields
us_symbols = set()
us_fields = set()
for col in us_stock_cols:
    parts = col.split('_')
    if len(parts) >= 4:  # US_Stock_SYMBOL_adj_field
        symbol = parts[2]
        field = '_'.join(parts[3:])  # adj_open, adj_close, etc.
        us_symbols.add(symbol)
        us_fields.add(field)

print(f"   Unique stock symbols: {len(us_symbols)}")
print(f"   Stock fields: {sorted(us_fields)}")
print(f"   First 20 symbols: {sorted(list(us_symbols))[:20]}")
print(f"   Sample US_Stock columns: {us_stock_cols[:10]}")

print(f"\n" + "="*60)
print("SUMMARY:")
print(f"LME: {len(lme_cols)} features (4 metals)")
print(f"JPX: {len(jpx_cols)} features ({len(jpx_products)} products)")
print(f"FX: {len(fx_cols)} features ({len(fx_currencies)} currencies)")
print(f"US_Stock: {len(us_stock_cols)} features ({len(us_symbols)} symbols)")
print(f"Total: {len(feature_cols)} features")

# Cell 12: Understanding Feature Meanings and Relationships
print("="*60)
print("UNDERSTANDING WHAT THE FEATURES ACTUALLY MEAN")
print("="*60)

# 1. LME Analysis - What are these metals?
print("1. LME METALS ANALYSIS:")
lme_metals = {
    'LME_AH_Close': 'Aluminum (Light metal, construction/automotive)',
    'LME_CA_Close': 'Copper (Industrial metal, electrical/construction)', 
    'LME_PB_Close': 'Lead (Heavy metal, batteries/construction)',
    'LME_ZS_Close': 'Zinc (Galvanizing metal, corrosion protection)'
}

for symbol, description in lme_metals.items():
    data = train_df[symbol].dropna()
    print(f"   {symbol}: {description}")
    print(f"     Price range: ${data.min():.0f} - ${data.max():.0f}")
    print(f"     Current level: ${data.iloc[-1]:.0f}")

# 2. JPX Analysis - Japanese futures structure
print(f"\n2. JPX FUTURES STRUCTURE:")
print("   Gold: 3 contract types (Mini, Rolling-Spot, Standard)")
print("   Platinum: 2 contract types (Mini, Standard)")  
print("   RSS3 Rubber: 1 contract type")
print("   Fields: OHLC + Volume + Open Interest + Settlement")

# Let's see the relationship between different gold contracts
gold_contracts = ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Rolling-Spot_Futures_Open', 'JPX_Gold_Standard_Futures_Open']
print(f"\n   Gold contract price comparison (latest values):")
for contract in gold_contracts:
    if contract in train_df.columns:
        latest_price = train_df[contract].dropna().iloc[-1]
        print(f"     {contract.split('_')[2]}: ¥{latest_price:.0f}")

# 3. FX Analysis - Currency relationships
print(f"\n3. FX CURRENCY ANALYSIS:")
major_pairs = ['FX_EURUSD', 'FX_GBPUSD', 'FX_USDJPY', 'FX_AUDUSD']
print("   Major currency pairs (latest rates):")
for pair in major_pairs:
    if pair in train_df.columns:
        latest_rate = train_df[pair].dropna().iloc[-1]
        pair_name = pair.replace('FX_', '')
        print(f"     {pair_name}: {latest_rate:.4f}")

# 4. US_Stock Analysis - What types of stocks?
print(f"\n4. US_STOCK ANALYSIS:")
us_symbols = set()
for col in train_df.columns:
    if col.startswith('US_Stock_'):
        symbol = col.split('_')[2]
        us_symbols.add(symbol)

# Categorize some known symbols
stock_categories = {
    'Commodities/Mining': ['AEM', 'AG', 'ALB', 'CCJ', 'CLF', 'COP', 'CVE', 'CVX', 'FCX', 'GOLD', 'NEM', 'SLV', 'GLD'],
    'Energy': ['BP', 'CVX', 'COP', 'DVN', 'XOM', 'SLB'],
    'ETFs/Indices': ['ACWI', 'AGG', 'BND', 'BNDX', 'BSV', 'VTI', 'SPY'],
    'Industrial': ['CAT', 'DE', 'BKR'],
    'Financial': ['BCS', 'AMP']
}

print("   Stock categories (sample identification):")
for category, symbols in stock_categories.items():
    found_symbols = [s for s in symbols if s in us_symbols]
    if found_symbols:
        print(f"     {category}: {found_symbols[:5]}{'...' if len(found_symbols) > 5 else ''}")

# 5. Check for obvious relationships
print(f"\n5. OBVIOUS RELATIONSHIPS TO INVESTIGATE:")
print("   - Gold: LME vs JPX gold futures vs US gold stocks (GLD, GOLD)")
print("   - Currency impact: USD strength vs commodity prices")
print("   - Energy: Oil stocks vs commodity currencies (CAD, AUD)")
print("   - Industrial metals: Copper vs industrial stocks")

# Cell 13: Cross-Market Relationships - Gold Analysis
print("="*60)
print("CROSS-MARKET GOLD ANALYSIS")
print("="*60)

# Gold-related assets across different markets
gold_assets = {
    'LME': [],  # No direct gold in LME (they have other metals)
    'JPX': ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Standard_Futures_Open'],
    'US_Stock': ['US_Stock_GLD_adj_open', 'US_Stock_GOLD_adj_open'],  # Gold ETF and mining stock
    'FX': []  # No direct gold, but USD affects gold prices
}

# Check which gold assets actually exist in our data
existing_gold_assets = {}
for market, assets in gold_assets.items():
    existing = [asset for asset in assets if asset in train_df.columns]
    if existing:
        existing_gold_assets[market] = existing

print("Available gold-related assets:")
for market, assets in existing_gold_assets.items():
    print(f"  {market}: {assets}")

# Plot gold assets together
if len(existing_gold_assets) > 0:
    fig, axes = plt.subplots(len(existing_gold_assets), 1, figsize=(12, 4*len(existing_gold_assets)))
    if len(existing_gold_assets) == 1:
        axes = [axes]
    
    plot_idx = 0
    for market, assets in existing_gold_assets.items():
        ax = axes[plot_idx]
        
        for asset in assets:
            data = train_df[['date_id', asset]].dropna()
            # Normalize to see correlation patterns (scale to 0-1)
            normalized_values = (data[asset] - data[asset].min()) / (data[asset].max() - data[asset].min())
            ax.plot(data['date_id'], normalized_values, label=asset.replace('US_Stock_', '').replace('JPX_', ''), alpha=0.8)
        
        ax.set_title(f'{market} Gold Assets (Normalized 0-1)')
        ax.set_xlabel('Date ID')
        ax.set_ylabel('Normalized Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
        plot_idx += 1
    
    plt.tight_layout()
    plt.show()

# Calculate correlations between gold assets
print(f"\nGold Asset Correlations:")
all_gold_cols = []
for assets in existing_gold_assets.values():
    all_gold_cols.extend(assets)

if len(all_gold_cols) > 1:
    gold_corr_matrix = train_df[all_gold_cols].corr()
    print(gold_corr_matrix.round(3))
    
    # Plot correlation heatmap
    plt.figure(figsize=(8, 6))
    sns.heatmap(gold_corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, cbar_kws={'label': 'Correlation'})
    plt.title('Gold Assets Correlation Matrix')
    plt.tight_layout()
    plt.show()

# Check USD impact on gold (inverse relationship expected)
usd_pairs = ['FX_EURUSD', 'FX_GBPUSD', 'FX_AUDUSD']  # USD strength indicators
print(f"\nUSD vs Gold Relationship Analysis:")
for gold_asset in all_gold_cols:
    print(f"\n{gold_asset} vs USD pairs:")
    for usd_pair in usd_pairs:
        if usd_pair in train_df.columns:
            corr = train_df[gold_asset].corr(train_df[usd_pair])
            print(f"  vs {usd_pair}: {corr:.3f}")

# Cell 14: Industrial Metals and Economic Relationships
print("="*60)
print("INDUSTRIAL METALS & ECONOMIC RELATIONSHIPS")
print("="*60)

# Industrial metals and related assets
industrial_assets = {
    'Copper (LME)': 'LME_CA_Close',
    'Aluminum (LME)': 'LME_AH_Close', 
    'Industrial Stocks': ['US_Stock_CAT_adj_open', 'US_Stock_DE_adj_open'],  # Caterpillar, Deere
    'Commodity Currencies': ['FX_AUDUSD', 'FX_CADUSD'],  # Australia, Canada
    'Global Index': 'US_Stock_ACWI_adj_open'
}

# Check what exists
existing_industrial = {}
for category, assets in industrial_assets.items():
    if isinstance(assets, list):
        existing = [asset for asset in assets if asset in train_df.columns]
        if existing:
            existing_industrial[category] = existing
    else:
        if assets in train_df.columns:
            existing_industrial[category] = [assets]

print("Available industrial-related assets:")
for category, assets in existing_industrial.items():
    print(f"  {category}: {assets}")

# Create correlation matrix for industrial relationships
all_industrial_cols = []
for assets in existing_industrial.values():
    all_industrial_cols.extend(assets)

if len(all_industrial_cols) > 1:
    industrial_corr = train_df[all_industrial_cols].corr()
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(industrial_corr, annot=True, cmap='coolwarm', center=0, 
                square=True, cbar_kws={'label': 'Correlation'})
    plt.title('Industrial Assets Correlation Matrix')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    
    print(f"\nKey Industrial Correlations:")
    # Focus on copper relationships
    if 'LME_CA_Close' in train_df.columns:
        copper_corrs = []
        for col in all_industrial_cols:
            if col != 'LME_CA_Close':
                corr = train_df['LME_CA_Close'].corr(train_df[col])
                copper_corrs.append((col, corr))
        
        copper_corrs.sort(key=lambda x: abs(x[1]), reverse=True)
        print(f"  Copper (LME_CA_Close) correlations:")
        for asset, corr in copper_corrs:
            print(f"    vs {asset}: {corr:.3f}")

# Time series comparison of copper vs industrial indicators
if 'LME_CA_Close' in train_df.columns:
    fig, axes = plt.subplots(2, 1, figsize=(12, 8))
    
    # Plot 1: Copper price
    copper_data = train_df[['date_id', 'LME_CA_Close']].dropna()
    axes[0].plot(copper_data['date_id'], copper_data['LME_CA_Close'], 'red', linewidth=2)
    axes[0].set_title('Copper Price (LME)')
    axes[0].set_ylabel('Price ($)')
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: Industrial indicators (normalized)
    industrial_indicators = ['US_Stock_CAT_adj_open', 'FX_AUDUSD', 'US_Stock_ACWI_adj_open']
    existing_indicators = [col for col in industrial_indicators if col in train_df.columns]
    
    for indicator in existing_indicators:
        data = train_df[['date_id', indicator]].dropna()
        # Normalize to 0-1 for comparison
        normalized = (data[indicator] - data[indicator].min()) / (data[indicator].max() - data[indicator].min())
        axes[1].plot(data['date_id'], normalized, label=indicator.replace('US_Stock_', '').replace('_adj_open', ''), alpha=0.8)
    
    axes[1].set_title('Industrial Indicators (Normalized)')
    axes[1].set_xlabel('Date ID')
    axes[1].set_ylabel('Normalized Value')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Cell 15: Missing Value Patterns Over Time
print("="*60)
print("MISSING VALUE PATTERNS OVER TIME")
print("="*60)

# Analyze missing values by time period
print("Analyzing missing value patterns across time periods...")

# Create time-based missing value analysis
date_ids = sorted(train_df['date_id'].unique())
n_periods = 10  # Divide into 10 time periods
period_size = len(date_ids) // n_periods

missing_by_period = []
for i in range(n_periods):
    start_idx = i * period_size
    end_idx = (i + 1) * period_size if i < n_periods - 1 else len(date_ids)
    
    period_dates = date_ids[start_idx:end_idx]
    period_data = train_df[train_df['date_id'].isin(period_dates)]
    
    # Calculate missing percentages by asset class
    period_stats = {
        'period': i + 1,
        'date_range': f"{period_dates[0]}-{period_dates[-1]}",
        'total_missing_pct': (period_data.isnull().sum().sum() / (period_data.shape[0] * period_data.shape[1])) * 100
    }
    
    # Missing by asset class
    for prefix in ['LME', 'JPX', 'US_Stock', 'FX']:
        prefix_cols = [col for col in period_data.columns if col.startswith(prefix)]
        if prefix_cols:
            prefix_missing = period_data[prefix_cols].isnull().sum().sum()
            prefix_total = len(prefix_cols) * len(period_data)
            period_stats[f'{prefix}_missing_pct'] = (prefix_missing / prefix_total) * 100
    
    missing_by_period.append(period_stats)

# Convert to DataFrame for easier analysis
missing_df = pd.DataFrame(missing_by_period)

print(f"\nMissing Value Patterns by Time Period:")
print(missing_df.round(2))

# Plot missing value trends
fig, axes = plt.subplots(2, 1, figsize=(12, 8))

# Plot 1: Overall missing percentage over time
axes[0].plot(missing_df['period'], missing_df['total_missing_pct'], 'ko-', linewidth=2, markersize=6)
axes[0].set_title('Overall Missing Value Percentage Over Time')
axes[0].set_xlabel('Time Period')
axes[0].set_ylabel('Missing %')
axes[0].grid(True, alpha=0.3)

# Plot 2: Missing by asset class
asset_classes = ['LME', 'JPX', 'US_Stock', 'FX']
for asset_class in asset_classes:
    col_name = f'{asset_class}_missing_pct'
    if col_name in missing_df.columns:
        axes[1].plot(missing_df['period'], missing_df[col_name], 'o-', 
                    label=asset_class, linewidth=2, markersize=4)

axes[1].set_title('Missing Value Percentage by Asset Class Over Time')
axes[1].set_xlabel('Time Period')
axes[1].set_ylabel('Missing %')
axes[1].legend()
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Identify problematic assets
print(f"\nMost Problematic Assets (highest missing rates):")
feature_cols = [col for col in train_df.columns if col != 'date_id']
missing_rates = train_df[feature_cols].isnull().mean() * 100
worst_assets = missing_rates.sort_values(ascending=False).head(10)

for asset, missing_pct in worst_assets.items():
    print(f"  {asset}: {missing_pct:.1f}% missing")

# Check if missing patterns are systematic (e.g., asset delisting)
print(f"\nSystematic Missing Pattern Analysis:")
high_missing_assets = missing_rates[missing_rates > 50].index.tolist()
print(f"Assets with >50% missing: {len(high_missing_assets)}")
for asset in high_missing_assets[:5]:
    # Check when missing values start
    asset_data = train_df[['date_id', asset]].copy()
    first_missing = asset_data[asset_data[asset].isnull()]['date_id'].min()
    last_valid = asset_data[asset_data[asset].notna()]['date_id'].max()
    print(f"  {asset}: last valid at date_id {last_valid}, missing starts around {first_missing}")

# Cell 16: Comprehensive EDA Summary & Recommendations
print("="*80)
print("COMPREHENSIVE EDA SUMMARY & MODELING RECOMMENDATIONS")
print("="*80)

# Summary statistics
total_features = len([col for col in train_df.columns if col != 'date_id'])
total_observations = len(train_df)
date_range = f"{train_df['date_id'].min()} to {train_df['date_id'].max()}"

print(f"DATASET OVERVIEW:")
print(f"  • Total Features: {total_features}")
print(f"  • Total Observations: {total_observations:,}")
print(f"  • Date Range: {date_range}")
print(f"  • Time Span: {train_df['date_id'].max() - train_df['date_id'].min() + 1} days")

print(f"\nASSET CLASS BREAKDOWN:")
asset_counts = {}
for prefix in ['LME', 'JPX', 'US_Stock', 'FX']:
    count = len([col for col in train_df.columns if col.startswith(prefix)])
    asset_counts[prefix] = count
    print(f"  • {prefix}: {count} features")

print(f"\nKEY FINDINGS:")
print(f"  1. DATA QUALITY:")
print(f"     • FX data: Excellent (0% missing)")
print(f"     • LME metals: Good (~2.5% missing)")
print(f"     • US Stocks: Variable (3-5% missing)")
print(f"     • JPX: Moderate issues (~6% missing)")
print(f"     • CRITICAL: US_Stock_GOLD 87% missing (delisted)")

print(f"\n  2. CROSS-MARKET RELATIONSHIPS:")
print(f"     • Gold futures (JPX) ↔ Gold ETF (GLD): 0.955 correlation")
print(f"     • Copper ↔ Industrial stocks: 0.77-0.89 correlation")
print(f"     • USD strength ↔ Gold: -0.34 to -0.63 correlation")
print(f"     • Commodity currencies surprisingly weak correlation with commodities")

print(f"\n  3. VOLATILITY PATTERNS:")
print(f"     • Energy sector: Highest volatility")
print(f"     • FX markets: Most stable")
print(f"     • Metals: Moderate volatility with trend components")

print(f"\n  4. TEMPORAL PATTERNS:")
print(f"     • Strong trending behavior in most assets")
print(f"     • Missing values relatively stable over time")
print(f"     • No obvious seasonality in this timeframe")

print(f"\nMODELING RECOMMENDATIONS:")
print(f"  1. FEATURE ENGINEERING:")
print(f"     • Create cross-market ratio features (e.g., Gold/Copper)")
print(f"     • Add technical indicators (moving averages, RSI)")
print(f"     • Include volatility features (rolling std)")
print(f"     • Consider currency-adjusted commodity prices")

print(f"\n  2. MISSING VALUE STRATEGY:")
print(f"     • DROP: US_Stock_GOLD_* (87% missing)")
print(f"     • FORWARD FILL: Most other assets (small gaps)")
print(f"     • INTERPOLATE: For systematic gaps in JPX data")

print(f"\n  3. MODEL ARCHITECTURE:")
print(f"     • Time series models (LSTM/GRU) for trending behavior")
print(f"     • Cross-attention for inter-market relationships")
print(f"     • Separate heads for different asset classes")
print(f"     • Ensemble approach combining multiple strategies")

print(f"\n  4. VALIDATION STRATEGY:")
print(f"     • Time-based splits (no data leakage)")
print(f"     • Walk-forward validation")
print(f"     • Asset-class specific evaluation")

print(f"\nHIGH-VALUE FEATURE GROUPS:")
high_value_features = {
    'FX_Core': ['FX_EURUSD', 'FX_GBPUSD', 'FX_AUDUSD', 'FX_CADUSD'],
    'Gold_Complex': ['JPX_Gold_Mini_Futures_Open', 'JPX_Gold_Standard_Futures_Open', 'US_Stock_GLD_adj_open'],
    'Industrial_Metals': ['LME_CA_Close', 'LME_AH_Close'],
    'Industrial_Stocks': ['US_Stock_CAT_adj_open', 'US_Stock_DE_adj_open'],
    'Energy': ['LME_Brent_Close', 'LME_Gas_Oil_Close'],
    'Global_Indices': ['US_Stock_ACWI_adj_open']
}

for group, features in high_value_features.items():
    available = [f for f in features if f in train_df.columns]
    print(f"  • {group}: {len(available)}/{len(features)} available")

print(f"\nNEXT STEPS:")
print(f"  1. Implement feature engineering pipeline")
print(f"  2. Create baseline models for each asset class")
print(f"  3. Develop cross-market prediction models")
print(f"  4. Optimize for competition metric")

print(f"\n" + "="*80)
print("EDA COMPLETE - Ready for Feature Engineering & Modeling!")
print("="*80)

# Cell 17: Train Labels - File Integrity & Granularity
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("TRAIN LABELS ANALYSIS - FILE INTEGRITY & GRANULARITY")
print("="*80)

# Load train labels
print("Loading train_labels.csv...")
train_labels = pd.read_csv('train_labels.csv')

print(f"\n1) BASIC FILE INTEGRITY:")
print(f"   • Shape: {train_labels.shape}")
print(f"   • Memory usage: {train_labels.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

# Check dtypes
print(f"\n2) DATA TYPES:")
dtype_counts = train_labels.dtypes.value_counts()
for dtype, count in dtype_counts.items():
    print(f"   • {dtype}: {count} columns")

# Check date_id uniqueness and coverage
print(f"\n3) DATE_ID GRANULARITY:")
print(f"   • Unique date_ids: {train_labels['date_id'].nunique()}")
print(f"   • Total rows: {len(train_labels)}")
print(f"   • One row per date_id: {train_labels['date_id'].nunique() == len(train_labels)}")
print(f"   • Date range: {train_labels['date_id'].min()} to {train_labels['date_id'].max()}")
print(f"   • Date span: {train_labels['date_id'].max() - train_labels['date_id'].min() + 1} days")

# Check for gaps in date sequence
date_ids = sorted(train_labels['date_id'].unique())
gaps = []
for i in range(1, len(date_ids)):
    if date_ids[i] - date_ids[i-1] > 1:
        gaps.append((date_ids[i-1], date_ids[i]))

if gaps:
    print(f"   • Date gaps found: {len(gaps)}")
    for start, end in gaps[:5]:  # Show first 5 gaps
        print(f"     Gap: {start} → {end} (missing {end-start-1} days)")
else:
    print(f"   • No gaps in date sequence ✓")

# Target columns analysis
target_cols = [col for col in train_labels.columns if col != 'date_id']
print(f"\n4) TARGET COLUMNS:")
print(f"   • Total target columns: {len(target_cols)}")
print(f"   • Expected ~424 columns: {'✓' if len(target_cols) >= 420 else '✗'}")

# Check for non-finite values
print(f"\n5) NON-FINITE VALUES ANALYSIS:")
total_cells = len(train_labels) * len(target_cols)
nan_counts = train_labels[target_cols].isnull().sum()
inf_counts = np.isinf(train_labels[target_cols].select_dtypes(include=[np.number])).sum()

print(f"   • Total data cells: {total_cells:,}")
print(f"   • NaN values: {nan_counts.sum():,} ({nan_counts.sum()/total_cells*100:.2f}%)")
print(f"   • Infinite values: {inf_counts.sum():,}")

# Most problematic columns (highest NaN rates)
nan_rates = (nan_counts / len(train_labels)) * 100
worst_nan = nan_rates.sort_values(ascending=False).head(10)
print(f"\n   Top 10 columns with highest NaN rates:")
for col, rate in worst_nan.items():
    print(f"     {col}: {rate:.1f}%")

# Check for constant or near-constant columns
print(f"\n6) CONSTANT/NEAR-CONSTANT COLUMNS:")
constant_cols = []
near_constant_cols = []

for col in target_cols:
    valid_data = train_labels[col].dropna()
    if len(valid_data) > 0:
        unique_vals = valid_data.nunique()
        if unique_vals == 1:
            constant_cols.append(col)
        elif unique_vals <= 3:  # Very few unique values
            near_constant_cols.append((col, unique_vals))

print(f"   • Constant columns (1 unique value): {len(constant_cols)}")
if constant_cols:
    for col in constant_cols[:5]:
        print(f"     {col}")

print(f"   • Near-constant columns (≤3 unique values): {len(near_constant_cols)}")
if near_constant_cols:
    for col, n_unique in near_constant_cols[:5]:
        print(f"     {col}: {n_unique} unique values")

# Quick distribution check
print(f"\n7) QUICK DISTRIBUTION OVERVIEW:")
sample_cols = target_cols[:5]  # Sample first 5 columns
sample_stats = train_labels[sample_cols].describe()
print(f"   Sample statistics for first 5 target columns:")
print(sample_stats.round(4))

print(f"\n" + "="*80)
print("FILE INTEGRITY CHECK COMPLETE")
print("="*80)