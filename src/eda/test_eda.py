#!/usr/bin/env python3
"""
Test script to verify EDA functionality
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from eda import CommodityEDA

def test_data_loading():
    """Test if data loading works correctly"""
    print("Testing data loading...")
    
    data_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if not data_path.exists():
        print(f"❌ Data path does not exist: {data_path}")
        return False
    
    eda = CommodityEDA(data_path)
    
    try:
        eda.load_data()
        print("✅ Data loading successful")
        
        # Basic checks
        assert eda.train_df is not None, "Train data not loaded"
        assert eda.train_labels_df is not None, "Train labels not loaded"
        assert eda.target_pairs_df is not None, "Target pairs not loaded"
        assert len(eda.lagged_dfs) > 0, "Lagged data not loaded"
        
        print(f"✅ Train data shape: {eda.train_df.shape}")
        print(f"✅ Train labels shape: {eda.train_labels_df.shape}")
        print(f"✅ Target pairs shape: {eda.target_pairs_df.shape}")
        print(f"✅ Lagged files: {len(eda.lagged_dfs)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return False

def test_basic_analysis():
    """Test basic analysis functions"""
    print("\nTesting basic analysis...")
    
    data_path = Path("../../input/mitsui-commodity-prediction-challenge")
    eda = CommodityEDA(data_path)
    
    try:
        eda.load_data()
        
        # Test basic structure analysis
        prefix_counts = eda.basic_structure_analysis()
        assert isinstance(prefix_counts, dict), "Prefix counts should be a dictionary"
        print("✅ Basic structure analysis works")
        
        # Test temporal analysis
        date_ids, gaps = eda.temporal_analysis()
        assert isinstance(date_ids, list), "Date IDs should be a list"
        assert isinstance(gaps, list), "Gaps should be a list"
        print("✅ Temporal analysis works")
        
        # Test identifier analysis
        identifier_stats = eda.identifier_analysis()
        assert isinstance(identifier_stats, dict), "Identifier stats should be a dictionary"
        print("✅ Identifier analysis works")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic analysis failed: {e}")
        return False

def test_quick_run():
    """Test a quick run of key functions"""
    print("\nTesting quick EDA run...")
    
    data_path = Path("../../input/mitsui-commodity-prediction-challenge")
    
    if not data_path.exists():
        print(f"❌ Data path does not exist: {data_path}")
        return False
    
    eda = CommodityEDA(data_path)
    
    try:
        eda.load_data()
        eda.basic_structure_analysis()
        print("✅ Quick EDA run successful")
        return True
        
    except Exception as e:
        print(f"❌ Quick EDA run failed: {e}")
        return False

def main():
    """Run all tests"""
    print("EDA Testing Suite")
    print("=" * 50)
    
    tests = [
        test_data_loading,
        test_basic_analysis,
        test_quick_run
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! EDA script is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
