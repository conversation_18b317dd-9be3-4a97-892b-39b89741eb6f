# If running on kaggle, these are the paths to the data
/kaggle/input/mitsui-commodity-prediction-challenge/target_pairs.csv
/kaggle/input/mitsui-commodity-prediction-challenge/train_labels.csv
/kaggle/input/mitsui-commodity-prediction-challenge/train.csv
/kaggle/input/mitsui-commodity-prediction-challenge/test.csv
/kaggle/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_1.csv
/kaggle/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_4.csv
/kaggle/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_3.csv
/kaggle/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_2.csv
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/mitsui_inference_server.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/mitsui_gateway.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/__init__.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/templates.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/base_gateway.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/relay.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/kaggle_evaluation.proto
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/__init__.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/kaggle_evaluation_pb2.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/kaggle_evaluation_pb2_grpc.py
/kaggle/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/__init__.py

# If running on google colab, these are the paths to the data
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/test.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/train_labels.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/target_pairs.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/train.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/mitsui_gateway.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/mitsui_inference_server.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/__init__.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/templates.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/relay.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/base_gateway.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/kaggle_evaluation.proto
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/__init__.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/kaggle_evaluation_pb2.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/kaggle_evaluation_pb2_grpc.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/__init__.py
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_2.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_1.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_3.csv
/content/drive/MyDrive/commodity-prices/input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_4.csv

# If running locally, these are the paths to the data
../../input/mitsui-commodity-prediction-challenge/target_pairs.csv
../../input/mitsui-commodity-prediction-challenge/train_labels.csv
../../input/mitsui-commodity-prediction-challenge/train.csv
../../input/mitsui-commodity-prediction-challenge/test.csv
../../input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_1.csv
../../input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_2.csv
../../input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_3.csv
../../input/mitsui-commodity-prediction-challenge/lagged_test_labels/test_labels_lag_4.csv
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/mitsui_inference_server.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/mitsui_gateway.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/__init__.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/templates.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/base_gateway.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/relay.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/kaggle_evaluation.proto
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/__init__.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/kaggle_evaluation_pb2.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/kaggle_evaluation_pb2_grpc.py
../../input/mitsui-commodity-prediction-challenge/kaggle_evaluation/core/generated/__init__.py
