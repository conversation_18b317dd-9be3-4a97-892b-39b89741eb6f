MITSUI&CO. Commodity Prediction Challenge
Develop a robust model for accurate and stable prediction of commodity prices

Overview
In this competition, you are challenged to develop a model capable of predicting future commodity returns using historical data from London Metal Exchange (LME), Japan Exchange Group (JPX), US Stock, and Forex markets. Leveraging insights from these diverse datasets is key to building stable, long-term forecasts crucial for optimizing trading strategies and managing risk in global commodity markets.

Description
The Challenge: Aiming for More Accurate Commodity Price Forecasting
This competition tackles the critical need for more accurate and stable long-term commodity price predictions. Getting these predictions right has significant implications for both businesses and the global economy. Inaccurate forecasts can lead to suboptimal trading strategies, poor investment decisions, and increased financial risk for companies involved in commodity markets. By encouraging the development of advanced AI models that can accurately predict future commodity returns using historical data from LME, JPX, US Stock, and Forex, this competition aims to directly improve the precision of financial forecasting and enable the optimization of automated trading strategies.

In particular, participants are challenged to predict price-difference series—derived from the time-series differences between two distinct assets’ prices—to extract robust price-movement signals as features and deploy AI-driven trading techniques that turn those signals into sustainable trading profits.

Why It's Important: Stable Markets and Smarter Decisions
Accurate commodity price prediction is crucial for reducing financial risk and ensuring stability in the global market. Currently, the inherent volatility and unpredictability of commodity prices create several problems. Companies struggle with resource allocation, budgeting, and investment planning, often resulting in financial losses and inefficient operations. Inaccurate forecasts can also contribute to market instability and price fluctuations, negatively impacting producers, consumers, and investors alike. If this problem could be solved, businesses could optimize their trading strategies, make more informed investment decisions, and manage risk more effectively. This would lead to more efficient resource allocation, reduced price volatility, and a more stable and predictable global commodity market.

Current Limitations and What We Hope to Achieve
Advanced AI algorithms and high-precision models are currently employed for commodity market prediction, utilizing historical price datasets from LME, JPX, US Stock, and Forex. While these models show promise, they often encounter limitations in consistently achieving high accuracy across diverse market conditions and over extended time horizons. Existing models may exhibit over-reliance on specific data patterns, lacking the adaptability required to navigate the dynamic and constantly evolving nature of financial markets. This competition seeks a more diverse and robust set of algorithms to achieve reliable and stable long-term predictions.

Acknowledgements
AlpacaTech Co., Ltd. provided technical support for problem design and data creation in this competition.
The Forex data for this competition was supplied via the Exchange Rates API from APILayer.

Evaluation
The competition's metric is a variant of the Sharpe ratio, computed by dividing the mean Spearman rank correlation between the predictions and targets by the standard deviation. The metric code is available here.

```
import numpy as np
import pandas as pd


SOLUTION_NULL_FILLER = -999999


def rank_correlation_sharpe_ratio(merged_df: pd.DataFrame) -> float:
    """
    Calculates the rank correlation between predictions and target values,
    and returns its Sharpe ratio (mean / standard deviation).

    :param merged_df: DataFrame containing prediction columns (starting with 'prediction_')
                      and target columns (starting with 'target_')
    :return: Sharpe ratio of the rank correlation
    :raises ZeroDivisionError: If the standard deviation is zero
    """
    prediction_cols = [col for col in merged_df.columns if col.startswith('prediction_')]
    target_cols = [col for col in merged_df.columns if col.startswith('target_')]

    def _compute_rank_correlation(row):
        non_null_targets = [col for col in target_cols if not pd.isnull(row[col])]
        matching_predictions = [col for col in prediction_cols if col.replace('prediction', 'target') in non_null_targets]
        if not non_null_targets:
            raise ValueError('No non-null target values found')
        if row[non_null_targets].std(ddof=0) == 0 or row[matching_predictions].std(ddof=0) == 0:
            raise ZeroDivisionError('Denominator is zero, unable to compute rank correlation.')
        return np.corrcoef(row[matching_predictions].rank(method='average'), row[non_null_targets].rank(method='average'))[0, 1]

    daily_rank_corrs = merged_df.apply(_compute_rank_correlation, axis=1)
    std_dev = daily_rank_corrs.std(ddof=0)
    if std_dev == 0:
        raise ZeroDivisionError('Denominator is zero, unable to compute Sharpe ratio.')
    sharpe_ratio = daily_rank_corrs.mean() / std_dev
    return float(sharpe_ratio)


def score(solution: pd.DataFrame, submission: pd.DataFrame, row_id_column_name: str) -> float:
    """
    Calculates the rank correlation between predictions and target values,
    and returns its Sharpe ratio (mean / standard deviation).
    """
    del solution[row_id_column_name]
    del submission[row_id_column_name]
    assert all(solution.columns == submission.columns)

    submission = submission.rename(columns={col: col.replace('target_', 'prediction_') for col in submission.columns})

    # Not all securities trade on all dates, but solution files cannot contain nulls.
    # The filler value allows us to handle trading halts, holidays, & delistings.
    solution = solution.replace(SOLUTION_NULL_FILLER, None)
    return rank_correlation_sharpe_ratio(pd.concat([solution, submission], axis='columns'))
```

Submission File
You must submit to this competition using the provided evaluation API, which ensures that models do not peek forward in time. See this example notebook for more details.

```
"""
The evaluation API requires that you set up a server which will respond to inference requests.
We have already defined the server; you just need write the predict function.
When we evaluate your submission on the hidden test set the client defined in `mitsui_gateway` will run in a different container
with direct access to the hidden test set and hand off the data timestep by timestep.

Your code will always have access to the published copies of the competition files.
"""

import os

import pandas as pd
import polars as pl

import kaggle_evaluation.mitsui_inference_server


NUM_TARGET_COLUMNS = 424


def predict(
    test: pl.DataFrame,
    label_lags_1_batch: pl.DataFrame,
    label_lags_2_batch: pl.DataFrame,
    label_lags_3_batch: pl.DataFrame,
    label_lags_4_batch: pl.DataFrame,
) -> pl.DataFrame | pd.DataFrame:
    """Replace this function with your inference code.
    You can return either a Pandas or Polars dataframe, though Polars is recommended for performance.
    Each batch of predictions (except the very first) must be returned within 1 minute of the batch features being provided.
    """
    predictions = pl.DataFrame({f'target_{i}': i / 1000 for i in range(NUM_TARGET_COLUMNS)})

    assert isinstance(predictions, (pd.DataFrame, pl.DataFrame))
    assert len(predictions) == 1
    return predictions


# When your notebook is run on the hidden test set, inference_server.serve must be called within 15 minutes of the notebook starting
# or the gateway will throw an error. If you need more than 15 minutes to load your model you can do so during the very
# first `predict` call, which does not have the usual 1 minute response deadline.
inference_server = kaggle_evaluation.mitsui_inference_server.MitsuiInferenceServer(predict)

if os.getenv('KAGGLE_IS_COMPETITION_RERUN'):
    inference_server.serve()
else:
    inference_server.run_local_gateway(('/kaggle/input/mitsui-commodity-prediction-challenge/',))
```

Timeline
This is a forecasting competition with an active training phase and a separate forecasting phase where models will be run against real market returns.
Training Timeline:
July 24, 2025 - Start Date.
September 29, 2025 - - Entry Deadline. You must accept the competition rules before this date in order to compete.
September 29, 2025 - Team Merger Deadline. This is the last day participants may join or merge teams.
October 6, 2025 - Final Submission Deadline.
All deadlines are at 11:59 PM UTC on the corresponding day unless otherwise noted. The competition organizers reserve the right to update the contest timeline if they deem it necessary.

Forecasting Timeline:
Starting after the final submission deadline there will be periodic updates to the leaderboard to reflect market data updates that will be run against selected notebooks.

January 16, 2026 - Competition End Date


Prizes
1st Place - $20,000
2nd Place - $10,000
3rd Place - $10,000
4th - 15th Place - $5,000


Code Requirements


Submissions to this competition must be made through Notebooks. In order for the "Submit" button to be active after a commit, the following conditions must be met:

CPU Notebook <= 8 hours run-time
GPU Notebook <= 8 hours run-time
Internet access disabled
Forecasting Phase
The run-time limits for both CPU and GPU notebooks will be extended to 9 hours during the forecasting phase. You must ensure your submission completes within that time.

The extra hour is to help protect against time-out failures due to the extended size of the test set. You are still responsible for ensuring your submission completes within the 9 hour limit, however. See the Data page for details on the extended test set during the forecasting phase.

Please see the Code Competition FAQ for more information on how to submit. And review the code debugging doc if you are encountering submission errors.